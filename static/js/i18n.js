/**
 * 国际化（i18n）模块
 * 提供中英文双语支持
 */

const i18n = (function() {
    // 语言包定义
    const translations = {
        'zh-CN': {
            // 页面标题
            'page.title': '控制面板 - AI拼车共享平台',
            'page.title.short': 'AI拼车共享平台',
            
            // 导航菜单
            'nav.dashboard': '控制面板',
            'nav.usage_guide': '使用指南',
            'nav.subscription': '订阅',
            'nav.my_subscription': '我的订阅',
            'nav.pricing': '购买/续费',
            'nav.management': '管理',
            'nav.device_management': '设备管理',
            'nav.social_links': '社区链接',
            'nav.tg_group': 'TG群',
            'nav.tg_channel': 'TG频道',
            
            // 用户菜单
            'user.loading': '加载中...',
            'user.logout': '退出登录',
            'user.username': '用户名',
            'user.email': '邮箱',
            'user.created_at': '账号创建',
            'user.status': '账号状态',
            'user.load_failed': '无法加载用户信息。',
            'user.load_error': '加载用户信息时出错。',
            
            // 服务概览
            'overview.title': '服务概览',
            'overview.expiry_time': '订阅到期时间',
            'overview.subscription_status': '订阅状态',
            'overview.device_quota': '设备额度',
            'overview.quick_start': '快速开始',
            'overview.view_guide': '查看指南',
            
            // 订阅状态
            'status.active': '活跃',
            'status.inactive': '未激活',
            'status.expired': '已过期',
            'status.no_subscription': '无订阅',
            'status.error': '错误',
            
            // AdsPower登录
            'adspower.login_title': 'AdsPower 登录',
            'adspower.login_button': '登录AdsPower',
            'adspower.device_usage': '设备使用说明',
            'adspower.device_usage_desc': '您的订阅最多允许同时使用 {0} 台设备。系统会根据负载情况自动为您分配AdsPower账号。',
            'adspower.device_usage_active': '您的 {0} ({1}) 套餐包含 {2} 个设备额度。您当前已使用 {3} 个设备。',
            'adspower.device_usage_expired': '您的订阅已过期。续费后即可恢复设备使用。',
            'adspower.device_usage_none': '您当前没有有效订阅。购买订阅后即可查看和使用设备额度。',
            'adspower.important_reminder': '重要提醒',
            'adspower.before_login': '在点击"登录AdsPower"按钮前，请确保：',
            'adspower.requirement_1': '已经下载并安装AdsPower客户端',
            'adspower.requirement_2': 'AdsPower客户端可以正常启动',
            'adspower.requirement_3': '您已准备好立即进行登录操作',
            'adspower.download_client': '下载并安装AdsPower客户端',
            'adspower.official_download': '官方下载',
            'adspower.session_timeout': '注意：登录会话有3分钟时间限制，请确保准备就绪后再点击登录按钮。',
            'adspower.processing': '处理中...',
            'adspower.allocating': '正在为您分配并准备AdsPower账号，请稍候...',
            'adspower.dont_refresh': '(这可能需要一些时间，请勿刷新页面)',
            'adspower.login_ready': '登录链接已生成!',
            'adspower.login_desc': '点击下方按钮在新窗口打开登录页面。此链接有效时间较短，请尽快登录。',
            'adspower.open_login': '打开登录页面',
            'adspower.login_help': '如无法登录，请参考使用指南或稍后重试。',
            'adspower.login_failed': '创建登录请求失败!',
            'adspower.retry': '重试',
            'adspower.network_error': '网络错误!',
            'adspower.network_error_desc': '创建登录请求失败，请检查网络连接并稍后重试。',
            'adspower.click_hint': '点击上方按钮即可获取登录凭证并开始使用。',
            'adspower.confirm_install': '登录前请确认：\n\n1. 您已经下载并安装了AdsPower客户端\n2. AdsPower客户端可以正常启动\n3. 您已准备好立即登录（登录会话有时间限制）\n\n点击"确定"表示您已准备就绪\n点击"取消"前往下载AdsPower客户端',
            
            // 错误消息
            'error.account_in_use': '请稍等片刻，其他用户正在登录中。建议等待1-2分钟后再试。',
            'error.all_accounts_busy': '资源组内所有账号都在使用中，请稍等片刻后再试。通常等待1-2分钟即可。',
            'error.no_adspower_account': '您所在的资源组当前没有配置账号资源。请联系管理员。',
            'error.account_full': '您选择的账号刚刚被其他用户占用。请稍后重试。',
            'error.device_limit': '请前往"设备管理"页面登出不再使用的设备。',
            'error.no_subscription_error': '请前往"购买/续费"页面购买订阅。',
            'error.verification_failed': '账号可能存在问题，请联系管理员。',
            'error.session_failed': '系统暂时无法创建登录会话，请稍后重试。',
            'error.unknown': '如果问题持续存在，请联系管理员。',
            
            // 我的订阅
            'subscription.title': '我的订阅',
            'subscription.details': '订阅详情',
            'subscription.loading': '正在加载订阅信息...',
            'subscription.current_plan': '当前套餐',
            'subscription.status': '状态',
            'subscription.start_date': '开始日期',
            'subscription.end_date': '到期日期',
            'subscription.device_limit': '设备额度',
            'subscription.instance_name': '所属车次',
            'subscription.instance_desc': '车次描述',
            'subscription.none': '暂无',
            'subscription.devices_unit': '台',
            'subscription.buy_now': '立即购买/续费',
            'subscription.renew_now': '立即续费',
            'subscription.no_subscription_tip': '提示',
            'subscription.view_plans': '查看订阅套餐',
            'subscription.common': '普通订阅',
            'subscription.checking_current': '正在获取当前订阅信息...',
            'subscription.no_active_subscription': '您当前没有有效的订阅，无法续费',
            'subscription.no_subscription_type': '当前订阅缺少套餐类型信息，请联系管理员',
            'subscription.cannot_get_plan_info': '无法获取套餐详细信息',
            'subscription.plan_not_found': '您当前的订阅套餐已不可用，请选择其他套餐',
            
            // 购买/续费
            'pricing.title': '购买 / 续费',
            'pricing.select_plan': '选择订阅套餐',
            'pricing.select_desc': '选择或续费您的订阅计划。',
            'pricing.features': '我们的特点：基于指纹浏览器，环境安全，不降智不封号，原生官网号，全部功能都可以使用。直接获得正规官网账号访问权限！',
            'pricing.loading': '正在加载订阅套餐...',
            'pricing.login_required': '请先登录以查看或购买订阅套餐。',
            'pricing.no_plans': '暂无可用的公开订阅套餐。',
            'pricing.recommended': '推荐',
            'pricing.per_days': '/ {0}天',
            'pricing.standard_plan': '标准订阅计划',
            'pricing.max_devices': '最多 {0} 台设备同时在线',
            'pricing.subscribe_now': '立即订阅',
            'pricing.confirm_purchase': '您将要购买/续费 "{0}" (¥{1})。确认继续吗？',
            'pricing.confirm_activate': '您将要激活 "{0}"。确认继续吗？',
            'pricing.purchasing': '正在购买 {0}...',
            'pricing.activating': '正在激活 {0}...',
            'pricing.purchase_success': '购买成功！',
            'pricing.activated': '免费订阅已成功激活！',
            'pricing.operation_success': '操作成功',
            'pricing.operation_failed': '操作失败',
            'pricing.calculating_renewal_date': '正在计算续费信息...',
            'pricing.renewal_confirmation': '您正在为【{0}】续费。续费成功后，您的新到期日将为【{1}】。确认继续吗？',
            'pricing.cannot_calculate_date': '无法计算新到期日',
            'pricing.checking_eligibility': '检查购买资格...',
            'pricing.insufficient_balance': '余额不足',
            'pricing.balance_insufficient_desc': '您的余额不足以购买此套餐，请先充值。',
            'pricing.after_purchase_balance': '购买后余额',
            'pricing.new_expiry_date': '新到期时间',

            // 余额相关
            'balance.title': '账户余额',
            'balance.current': '当前余额',
            'balance.recharge': '充值',
            'balance.transactions': '余额记录',
            'balance.insufficient': '余额不足',
            'balance.loading': '正在加载余额...',
            'balance.load_failed': '加载余额失败',
            'balance.recharge_success': '充值成功',
            'balance.recharge_failed': '充值失败',

            // 兑换码相关
            'redemption.title': '兑换码充值',
            'redemption.code': '兑换码',
            'redemption.enter_code': '请输入兑换码',
            'redemption.code_placeholder': '请输入兑换码（如：GPT-ABCD1234）',
            'redemption.code_format': '兑换码通常以 GPT- 开头',
            'redemption.redeem': '兑换',
            'redemption.redeeming': '正在兑换...',
            'redemption.success': '兑换成功',
            'redemption.failed': '兑换失败',
            'redemption.invalid_code': '无效的兑换码',
            'redemption.code_used': '兑换码已被使用',
            'redemption.code_expired': '兑换码已过期',
            'redemption.enter_valid_code': '请输入有效的兑换码',
            'redemption.recharge_first': '请使用兑换码充值余额后再购买',

            // 通用错误信息
            'common.unknown_error': '未知错误',
            'common.network_error': '网络错误，请稍后重试',
            'common.loading': '加载中...',
            'common.success': '成功',
            'common.failed': '失败',
            'common.confirm': '确认',
            'common.cancel': '取消',
            'common.close': '关闭',
            'common.save': '保存',
            'common.edit': '编辑',
            'common.delete': '删除',
            'common.add': '添加',
            'common.refresh': '刷新',
            'common.search': '搜索',
            'common.clear': '清除',
            'common.view': '查看',
            'common.details': '详情',
            'common.status': '状态',
            'common.actions': '操作',
            'common.create_time': '创建时间',
            'common.update_time': '更新时间',
            'common.no_data': '暂无数据',

            // 管理员界面
            'admin.title': '管理后台 - ChatGPTPro俱乐部',
            'admin.dashboard': '系统总览',
            'admin.device_management': '设备管理',
            'admin.device_audits': '设备审计',
            'admin.user_management': '用户管理',
            'admin.adspower_management': 'AdsPower账号管理',
            'admin.subscription_types': '订阅类型管理',
            'admin.subscription_management': '订阅管理',
            'admin.subscription_instances': '订阅实例管理',
            'admin.order_management': '订单管理',
            'admin.redemption_management': '兑换码管理',

            // 兑换码管理
            'admin.redemption.title': '兑换码管理',
            'admin.redemption.generate': '生成兑换码',
            'admin.redemption.total_codes': '总兑换码数',
            'admin.redemption.used_codes': '已使用',
            'admin.redemption.unused_codes': '未使用',
            'admin.redemption.total_value': '总价值',
            'admin.redemption.filter_conditions': '筛选条件',
            'admin.redemption.usage_status': '使用状态',
            'admin.redemption.all_status': '全部状态',
            'admin.redemption.unused': '未使用',
            'admin.redemption.used': '已使用',
            'admin.redemption.creator': '创建者',
            'admin.redemption.creator_placeholder': '输入创建者邮箱',
            'admin.redemption.code_placeholder': '输入兑换码',
            'admin.redemption.code_list': '兑换码列表',
            'admin.redemption.code': '兑换码',
            'admin.redemption.amount': '金额',
            'admin.redemption.user': '使用者',
            'admin.redemption.used_time': '使用时间',
            'admin.redemption.expire_time': '过期时间',
            'admin.redemption.never_expire': '永不过期',
            'admin.redemption.disable': '禁用',
            'admin.redemption.expired': '已过期',

            // 生成兑换码对话框
            'admin.redemption.generate_title': '生成兑换码',
            'admin.redemption.redemption_amount': '兑换金额 (元)',
            'admin.redemption.amount_desc': '用户兑换后获得的余额金额',
            'admin.redemption.generate_count': '生成数量',
            'admin.redemption.count_desc': '一次最多生成100个兑换码',
            'admin.redemption.validity_days': '有效期 (天)',
            'admin.redemption.validity_desc': '兑换码的有效期，留空表示永不过期',
            'admin.redemption.description': '备注',
            'admin.redemption.description_placeholder': '可选的备注信息',
            'admin.redemption.description_desc': '用于标识这批兑换码的用途',
            'admin.redemption.generate_btn': '生成兑换码',
            'admin.redemption.generating': '生成中...',
            'admin.redemption.generate_success': '成功生成 {0} 个兑换码',
            'admin.redemption.generate_failed': '生成兑换码失败',

            // 兑换码详情对话框
            'admin.redemption.detail_title': '兑换码详情',
            'admin.redemption.basic_info': '基本信息',
            'admin.redemption.usage_info': '使用信息',
            'admin.redemption.disable_confirm': '确定要禁用这个兑换码吗？禁用后将无法使用。',
            'admin.redemption.disabled': '兑换码已禁用',
            'admin.redemption.disable_failed': '禁用兑换码失败',
            'admin.redemption.code_not_found': '兑换码不存在',

            // 使用指南
            'guide.title': '使用指南',
            'guide.login_steps': 'ChatGPT拼车账号登录步骤',
            'guide.step_1': '点击控制面板上的 登录AdsPower 按钮获取登录凭证。',
            'guide.step_2': '在弹出的新页面中，您将看到AdsPower账号的用户名、密码和验证码 (若需要)。',
            'guide.step_3': '下载并安装AdsPower客户端（官方下载）。强烈建议使用客户端，不要使用网页版。',
            'guide.step_4': '使用我们提供的用户名、密码和验证码登录AdsPower客户端。',
            'guide.step_5': '国内用户请选择 "国内环境" 登录。',
            'guide.step_6': '海外用户请选择 "directus环境" 或类似选项登录。',
            'guide.step_7': '登录成功后，您可以在AdsPower提供的浏览器中使用ChatGPT及所有Pro功能。',
            'guide.important_tips': '重要提示：',
            'guide.tip_1': '请勿修改AdsPower账号的任何设置（特别是密码或邮箱），这可能导致您的订阅失效且无法恢复。',
            'guide.tip_2': '请勿在AdsPower内安装非必要的浏览器扩展。',
            'guide.tip_3': '同一时间只允许一个用户登录同一个分配到的AdsPower账号。',
            'guide.tip_4': '您的订阅包含设备数量限制，请勿超额使用。',
            
            // 设备管理
            'device.title': '设备管理',
            'device.quota': '设备额度',
            'device.refresh': '刷新设备额度',
            'device.operation_tips': '操作提示',
            'device.tips_content': '您可以点击设备列表右侧的 登出 按钮来移除不再使用的设备，从而释放您的设备额度。',
            'device.tips_content2': '定期检查并清理不活跃的设备，确保您的账户安全和额度充足。',
            'device.logged_devices': '已登录设备列表',
            'device.list_desc': '这里列出了您当前账户下所有已记录的设备。您可以登出不再使用的设备以释放额度。',
            'device.name': '设备名称',
            'device.ip': 'IP 地址',
            'device.type': '设备类型',
            'device.operation': '操作',
            'device.logout': '登出',
            'device.unknown': '未知设备',
            'device.unknown_type': '未知',
            'device.no_devices': '当前没有已记录的设备。',
            'device.logout_confirm': '您确定要登出此设备吗？您将需要重新在该设备上登录。',
            'device.logout_processing': '正在登出设备...',
            'device.logout_success': '设备已成功登出。',
            'device.logout_failed': '登出设备失败',
            'device.network_error': '登出设备时发生网络错误。',
            'device.quota_refreshed': '设备额度已刷新',
            'device.quota_refresh_failed': '刷新设备额度失败，请稍后重试',
            'device.loading': '加载中...',
            'device.loading_quota': '正在加载额度信息...',
            'device.api_error': '获取设备列表失败',
            'device.quota_good': '设备额度状态良好。',
            'device.quota_warning': '设备额度即将用尽，请注意管理。',
            'device.quota_exceeded': '警告：已用设备数超出订阅额度！',
            'device.subscription_expired': '您的订阅已过期，但仍有 {0} 台已注册设备。',
            'device.subscription_invalid': '您的订阅无效，但仍有 {0} 台已注册设备。',
            'device.no_subscription': '您当前无有效订阅。',
            'device.subscription_expired_short': '您的订阅已过期。',
            'device.new_device_confirmed': '新设备已成功确认并添加。',
            'device.monitoring': '正在监测设备登录状态...',
            'device.monitoring_interval': '（每5秒检查一次）',
            'device.stop_monitoring': '停止监测',
            'device.new_device_detected': '检测到新设备登录，设备额度已更新',
            
            // 通用
            'common.loading': '加载中...',
            'common.processing': '请稍候...',
            'common.login_expired': '您的登录已过期，请重新登录。',
            'common.page_error': '页面元素错误，无法请求登录。',
            'common.already_ended': '已结束',
            'common.less_than_minute': '少于1分钟',
            'common.days': '天',
            'common.hours': '小时',
            'common.minutes': '分钟',
            'common.instance': '车次',
            'common.loading_error': '加载失败',
            'common.server_error': '服务器错误',
            'common.network_error': '网络错误',
            'common.unknown_error': '未知错误',
            'common.unable_load': '无法加载订阅信息',
            'common.unable_get': '无法获取有效订阅信息。',
            'common.api_error': '获取订阅信息失败',
            'common.check_network': '获取订阅信息失败。请检查网络连接或稍后重试。',
            'common.data_format_error': '返回数据格式错误或操作未成功',
            'common.backend_no_url': '后端未返回登录URL',
            'common.resource_in_use': '资源正在使用中',
            'common.operation_canceled': '操作已取消',
            
            // 语言
            'lang.zh_CN': '中文',
            'lang.en': 'English',
            
            // Welcome 页面
            'welcome.meta_description': 'AI拼车共享平台 - 尊贵AI拼车体验，多级拼车服务，专业技术支持，为您节省90%使用成本',
            'welcome.page_title': 'AI拼车共享平台 - 尊享AI拼车服务',
            'welcome.brand_name': 'AI拼车共享平台',
            'welcome.logo_alt': 'AI拼车共享平台 Logo',
            'welcome.currency_symbol': '¥',
            'welcome.login': '登录',
            'welcome.hero_title': '欢迎来到 AI拼车共享平台',
            'welcome.hero_subtitle': '尊贵AI助手拼车体验，为您节省90%使用成本',
            'welcome.view_plans': '查看套餐',
            'welcome.login_now': '立即登录',
            'welcome.why_choose_us': '为什么选择我们',
            'welcome.feature_safe_title': '安全可靠',
            'welcome.feature_safe_desc': '基于指纹浏览器技术，环境安全隔离，不降智不封号，原生官网账号',
            'welcome.feature_flexible_title': '灵活选择',
            'welcome.feature_flexible_desc': '1人独享车、5人车、10人车等多种套餐，满足不同需求',
            'welcome.feature_professional_title': '专业服务',
            'welcome.feature_professional_desc': '多位成员提供支持，快速响应，专业技术团队保障服务质量',
            'welcome.pricing_section_title': '选择您的套餐',
            'welcome.loading_plans': '正在加载套餐信息...',
            'welcome.no_plans_available': '暂无可用套餐',
            'welcome.per_days': '/{0}天',
            'welcome.max_devices': '最多{0}台设备',
            'welcome.login_to_subscribe': '登录后订阅',
            'welcome.testimonial_content': '加入5人共享后，GPT-4o生成的参考图帮我突破了设计瓶颈，素材准备时间从2小时缩短到20分钟。图像质量好到让客户惊讶，整个设计流程更加流畅高效。',
            'welcome.testimonial_author': '陈总',
            'welcome.testimonial_role': '创意公司合作伙伴',
            'welcome.cta_title': '准备好开始了吗？',
            'welcome.cta_subtitle': '加入200+尊贵会员，享受高品质AI服务',
            'welcome.join_now': '立即加入',
            'welcome.footer_copyright': '&copy; 2025 AI拼车共享平台. All rights reserved.',
            'welcome.loading': '加载中...'
        },
        'en': {
            // Page titles
            'page.title': 'Dashboard - ChatGPTPro Club',
            'page.title.short': 'ChatGPTPro Club',
            
            // Navigation menu
            'nav.dashboard': 'Dashboard',
            'nav.usage_guide': 'Usage Guide',
            'nav.subscription': 'Subscription',
            'nav.my_subscription': 'My Subscription',
            'nav.pricing': 'Purchase/Renew',
            'nav.management': 'Management',
            'nav.device_management': 'Device Management',
            'nav.social_links': 'Community Links',
            'nav.tg_group': 'TG Group',
            'nav.tg_channel': 'TG Channel',
            
            // User menu
            'user.loading': 'Loading...',
            'user.logout': 'Logout',
            'user.username': 'Username',
            'user.email': 'Email',
            'user.created_at': 'Account Created',
            'user.status': 'Account Status',
            'user.load_failed': 'Unable to load user info.',
            'user.load_error': 'Error loading user info.',
            
            // Service overview
            'overview.title': 'Service Overview',
            'overview.expiry_time': 'Subscription Expiry',
            'overview.subscription_status': 'Subscription Status',
            'overview.device_quota': 'Device Quota',
            'overview.quick_start': 'Quick Start',
            'overview.view_guide': 'View Guide',
            
            // Subscription status
            'status.active': 'Active',
            'status.inactive': 'Inactive',
            'status.expired': 'Expired',
            'status.no_subscription': 'No Subscription',
            'status.error': 'Error',
            
            // AdsPower login
            'adspower.login_title': 'AdsPower Login',
            'adspower.login_button': 'Login to AdsPower',
            'adspower.device_usage': 'Device Usage Info',
            'adspower.device_usage_desc': 'Your subscription allows up to {0} devices to be used simultaneously. The system will automatically allocate AdsPower accounts based on load.',
            'adspower.device_usage_active': 'Your {0} ({1}) plan includes {2} device slots. You are currently using {3} devices.',
            'adspower.device_usage_expired': 'Your subscription has expired. Renew to restore device usage.',
            'adspower.device_usage_none': 'You do not have an active subscription. Purchase a subscription to view and use device quota.',
            'adspower.important_reminder': 'Important Reminder',
            'adspower.before_login': 'Before clicking "Login to AdsPower", please ensure:',
            'adspower.requirement_1': 'You have downloaded and installed AdsPower client',
            'adspower.requirement_2': 'AdsPower client can start normally',
            'adspower.requirement_3': 'You are ready to login immediately',
            'adspower.download_client': 'Download and install AdsPower client',
            'adspower.official_download': 'Official Download',
            'adspower.session_timeout': 'Note: Login session has a 3-minute time limit. Please ensure you are ready before clicking the login button.',
            'adspower.processing': 'Processing...',
            'adspower.allocating': 'Allocating and preparing AdsPower account for you, please wait...',
            'adspower.dont_refresh': '(This may take some time, please do not refresh the page)',
            'adspower.login_ready': 'Login link generated!',
            'adspower.login_desc': 'Click the button below to open the login page in a new window. This link is valid for a short time, please login promptly.',
            'adspower.open_login': 'Open Login Page',
            'adspower.login_help': 'If you cannot login, please refer to the usage guide or try again later.',
            'adspower.login_failed': 'Failed to create login request!',
            'adspower.retry': 'Retry',
            'adspower.network_error': 'Network Error!',
            'adspower.network_error_desc': 'Failed to create login request. Please check your network connection and try again later.',
            'adspower.click_hint': 'Click the button above to get login credentials and start using.',
            'adspower.confirm_install': 'Before logging in, please confirm:\n\n1. You have downloaded and installed AdsPower client\n2. AdsPower client can start normally\n3. You are ready to login immediately (login session has time limit)\n\nClick "OK" to indicate you are ready\nClick "Cancel" to download AdsPower client',
            
            // Error messages
            'error.account_in_use': 'Please wait a moment, another user is logging in. Try again in 1-2 minutes.',
            'error.all_accounts_busy': 'All accounts in the resource group are in use. Please wait a moment and try again. Usually 1-2 minutes is enough.',
            'error.no_adspower_account': 'Your resource group currently has no configured account resources. Please contact the administrator.',
            'error.account_full': 'The account you selected was just occupied by another user. Please try again later.',
            'error.device_limit': 'Please go to "Device Management" to log out unused devices.',
            'error.no_subscription_error': 'Please go to "Purchase/Renew" to purchase a subscription.',
            'error.verification_failed': 'Account may have issues. Please contact the administrator.',
            'error.session_failed': 'System temporarily unable to create login session. Please try again later.',
            'error.unknown': 'If the problem persists, please contact the administrator.',
            
            // My subscription
            'subscription.title': 'My Subscription',
            'subscription.details': 'Subscription Details',
            'subscription.loading': 'Loading subscription info...',
            'subscription.current_plan': 'Current Plan',
            'subscription.status': 'Status',
            'subscription.start_date': 'Start Date',
            'subscription.end_date': 'End Date',
            'subscription.device_limit': 'Device Quota',
            'subscription.instance_name': 'Instance',
            'subscription.instance_desc': 'Instance Description',
            'subscription.none': 'None',
            'subscription.devices_unit': 'devices',
            'subscription.buy_now': 'Buy/Renew Now',
            'subscription.renew_now': 'Renew Now',
            'subscription.no_subscription_tip': 'Notice',
            'subscription.view_plans': 'View Subscription Plans',
            'subscription.common': 'Regular Subscription',
            'subscription.checking_current': 'Getting current subscription info...',
            'subscription.no_active_subscription': 'You have no active subscription to renew',
            'subscription.no_subscription_type': 'Current subscription lacks plan type info, please contact admin',
            'subscription.cannot_get_plan_info': 'Cannot get plan details',
            'subscription.plan_not_found': 'Your current subscription plan is no longer available, please choose another plan',
            
            // Pricing
            'pricing.title': 'Purchase / Renew',
            'pricing.select_plan': 'Select Subscription Plan',
            'pricing.select_desc': 'Select or renew your subscription plan.',
            'pricing.features': 'Our features: Based on fingerprint browser, safe environment, no intelligence reduction or account ban, native official accounts, all features available. Direct access to official website accounts!',
            'pricing.loading': 'Loading subscription plans...',
            'pricing.login_required': 'Please login to view or purchase subscription plans.',
            'pricing.no_plans': 'No public subscription plans available.',
            'pricing.recommended': 'Recommended',
            'pricing.per_days': '/ {0} days',
            'pricing.standard_plan': 'Standard subscription plan',
            'pricing.max_devices': 'Up to {0} devices online simultaneously',
            'pricing.subscribe_now': 'Subscribe Now',
            'pricing.confirm_purchase': 'You are about to purchase/renew "{0}" (¥{1}). Confirm to continue?',
            'pricing.confirm_activate': 'You are about to activate "{0}". Confirm to continue?',
            'pricing.purchasing': 'Purchasing {0}...',
            'pricing.activating': 'Activating {0}...',
            'pricing.purchase_success': 'Purchase successful!',
            'pricing.activated': 'Free subscription activated successfully!',
            'pricing.operation_success': 'Operation successful',
            'pricing.operation_failed': 'Operation failed',
            'pricing.calculating_renewal_date': 'Calculating renewal information...',
            'pricing.renewal_confirmation': 'You are about to renew "{0}". After successful renewal, your new expiration date will be "{1}". Do you want to continue?',
            'pricing.cannot_calculate_date': 'Cannot calculate new expiry date',
            'pricing.checking_eligibility': 'Checking purchase eligibility...',
            'pricing.insufficient_balance': 'Insufficient balance',
            'pricing.balance_insufficient_desc': 'Your balance is insufficient to purchase this plan, please recharge first.',
            'pricing.after_purchase_balance': 'Balance after purchase',
            'pricing.new_expiry_date': 'New expiry date',

            // Balance related
            'balance.title': 'Account Balance',
            'balance.current': 'Current Balance',
            'balance.recharge': 'Recharge',
            'balance.transactions': 'Balance Records',
            'balance.insufficient': 'Insufficient balance',
            'balance.loading': 'Loading balance...',
            'balance.load_failed': 'Failed to load balance',
            'balance.recharge_success': 'Recharge successful',
            'balance.recharge_failed': 'Recharge failed',

            // Redemption code related
            'redemption.title': 'Redemption Code Recharge',
            'redemption.code': 'Redemption Code',
            'redemption.enter_code': 'Please enter redemption code',
            'redemption.code_placeholder': 'Please enter redemption code (e.g., GPT-ABCD1234)',
            'redemption.code_format': 'Redemption codes usually start with GPT-',
            'redemption.redeem': 'Redeem',
            'redemption.redeeming': 'Redeeming...',
            'redemption.success': 'Redemption successful',
            'redemption.failed': 'Redemption failed',
            'redemption.invalid_code': 'Invalid redemption code',
            'redemption.code_used': 'Redemption code has been used',
            'redemption.code_expired': 'Redemption code has expired',
            'redemption.enter_valid_code': 'Please enter a valid redemption code',
            'redemption.recharge_first': 'Please recharge your balance using a redemption code before purchasing',

            // Common error messages
            'common.unknown_error': 'Unknown error',
            'common.network_error': 'Network error, please try again later',
            'common.loading': 'Loading...',
            'common.success': 'Success',
            'common.failed': 'Failed',
            'common.confirm': 'Confirm',
            'common.cancel': 'Cancel',
            'common.close': 'Close',
            'common.save': 'Save',
            'common.edit': 'Edit',
            'common.delete': 'Delete',
            'common.add': 'Add',
            'common.refresh': 'Refresh',
            'common.search': 'Search',
            'common.clear': 'Clear',
            'common.view': 'View',
            'common.details': 'Details',
            'common.status': 'Status',
            'common.actions': 'Actions',
            'common.create_time': 'Create Time',
            'common.update_time': 'Update Time',
            'common.no_data': 'No data available',

            // Admin interface
            'admin.title': 'Admin Dashboard - ChatGPTPro Club',
            'admin.dashboard': 'System Overview',
            'admin.device_management': 'Device Management',
            'admin.device_audits': 'Device Audits',
            'admin.user_management': 'User Management',
            'admin.adspower_management': 'AdsPower Account Management',
            'admin.subscription_types': 'Subscription Type Management',
            'admin.subscription_management': 'Subscription Management',
            'admin.subscription_instances': 'Subscription Instance Management',
            'admin.order_management': 'Order Management',
            'admin.redemption_management': 'Redemption Code Management',

            // Redemption code management
            'admin.redemption.title': 'Redemption Code Management',
            'admin.redemption.generate': 'Generate Redemption Code',
            'admin.redemption.total_codes': 'Total Codes',
            'admin.redemption.used_codes': 'Used',
            'admin.redemption.unused_codes': 'Unused',
            'admin.redemption.total_value': 'Total Value',
            'admin.redemption.filter_conditions': 'Filter Conditions',
            'admin.redemption.usage_status': 'Usage Status',
            'admin.redemption.all_status': 'All Status',
            'admin.redemption.unused': 'Unused',
            'admin.redemption.used': 'Used',
            'admin.redemption.creator': 'Creator',
            'admin.redemption.creator_placeholder': 'Enter creator email',
            'admin.redemption.code_placeholder': 'Enter redemption code',
            'admin.redemption.code_list': 'Redemption Code List',
            'admin.redemption.code': 'Redemption Code',
            'admin.redemption.amount': 'Amount',
            'admin.redemption.user': 'User',
            'admin.redemption.used_time': 'Used Time',
            'admin.redemption.expire_time': 'Expire Time',
            'admin.redemption.never_expire': 'Never Expire',
            'admin.redemption.disable': 'Disable',
            'admin.redemption.expired': 'Expired',

            // Generate redemption code dialog
            'admin.redemption.generate_title': 'Generate Redemption Code',
            'admin.redemption.redemption_amount': 'Redemption Amount (Yuan)',
            'admin.redemption.amount_desc': 'Balance amount user will receive after redemption',
            'admin.redemption.generate_count': 'Generate Count',
            'admin.redemption.count_desc': 'Maximum 100 redemption codes at once',
            'admin.redemption.validity_days': 'Validity Period (Days)',
            'admin.redemption.validity_desc': 'Validity period of redemption codes, leave empty for never expire',
            'admin.redemption.description': 'Description',
            'admin.redemption.description_placeholder': 'Optional description',
            'admin.redemption.description_desc': 'Used to identify the purpose of this batch of redemption codes',
            'admin.redemption.generate_btn': 'Generate Redemption Code',
            'admin.redemption.generating': 'Generating...',
            'admin.redemption.generate_success': 'Successfully generated {0} redemption codes',
            'admin.redemption.generate_failed': 'Failed to generate redemption codes',

            // Redemption code detail dialog
            'admin.redemption.detail_title': 'Redemption Code Details',
            'admin.redemption.basic_info': 'Basic Information',
            'admin.redemption.usage_info': 'Usage Information',
            'admin.redemption.disable_confirm': 'Are you sure you want to disable this redemption code? It cannot be used after being disabled.',
            'admin.redemption.disabled': 'Redemption code has been disabled',
            'admin.redemption.disable_failed': 'Failed to disable redemption code',
            'admin.redemption.code_not_found': 'Redemption code not found',

            // Usage guide
            'guide.title': 'Usage Guide',
            'guide.login_steps': 'ChatGPT Shared Account Login Steps',
            'guide.step_1': 'Click the "Login to AdsPower" button on the dashboard to get login credentials.',
            'guide.step_2': 'In the popup page, you will see AdsPower account username, password and verification code (if needed).',
            'guide.step_3': 'Download and install AdsPower client (Official Download). Strongly recommend using the client, not the web version.',
            'guide.step_4': 'Use the provided username, password and verification code to login to AdsPower client.',
            'guide.step_5': 'Domestic users please select "Domestic Environment" to login.',
            'guide.step_6': 'Overseas users please select "directus environment" or similar option to login.',
            'guide.step_7': 'After successful login, you can use ChatGPT and all Pro features in the browser provided by AdsPower.',
            'guide.important_tips': 'Important Tips:',
            'guide.tip_1': 'Do not modify any AdsPower account settings (especially password or email), this may invalidate your subscription and cannot be recovered.',
            'guide.tip_2': 'Do not install unnecessary browser extensions in AdsPower.',
            'guide.tip_3': 'Only one user can login to the same assigned AdsPower account at a time.',
            'guide.tip_4': 'Your subscription has device quantity limits, please do not exceed usage.',
            
            // Device management
            'device.title': 'Device Management',
            'device.quota': 'Device Quota',
            'device.refresh': 'Refresh Device Quota',
            'device.operation_tips': 'Operation Tips',
            'device.tips_content': 'You can click the "Logout" button on the right side of the device list to remove devices no longer in use, thereby releasing your device quota.',
            'device.tips_content2': 'Regularly check and clean up inactive devices to ensure your account security and sufficient quota.',
            'device.logged_devices': 'Logged In Devices',
            'device.list_desc': 'This lists all recorded devices under your current account. You can logout devices no longer in use to free up quota.',
            'device.name': 'Device Name',
            'device.ip': 'IP Address',
            'device.type': 'Device Type',
            'device.operation': 'Operation',
            'device.logout': 'Logout',
            'device.unknown': 'Unknown Device',
            'device.unknown_type': 'Unknown',
            'device.no_devices': 'No recorded devices currently.',
            'device.logout_confirm': 'Are you sure you want to logout this device? You will need to login again on that device.',
            'device.logout_processing': 'Logging out device...',
            'device.logout_success': 'Device logged out successfully.',
            'device.logout_failed': 'Failed to logout device',
            'device.network_error': 'Network error occurred while logging out device.',
            'device.quota_refreshed': 'Device quota refreshed',
            'device.quota_refresh_failed': 'Failed to refresh device quota, please try again later',
            'device.loading': 'Loading...',
            'device.loading_quota': 'Loading quota info...',
            'device.api_error': 'Failed to get device list',
            'device.quota_good': 'Device quota status is good.',
            'device.quota_warning': 'Device quota is running low, please manage carefully.',
            'device.quota_exceeded': 'Warning: Used devices exceed subscription quota!',
            'device.subscription_expired': 'Your subscription has expired, but you still have {0} registered devices.',
            'device.subscription_invalid': 'Your subscription is invalid, but you still have {0} registered devices.',
            'device.no_subscription': 'You currently have no valid subscription.',
            'device.subscription_expired_short': 'Your subscription has expired.',
            'device.new_device_confirmed': 'New device confirmed and added successfully.',
            'device.monitoring': 'Monitoring device login status...',
            'device.monitoring_interval': '(Checking every 5 seconds)',
            'device.stop_monitoring': 'Stop Monitoring',
            'device.new_device_detected': 'New device login detected, device quota updated',
            
            // Common
            'common.loading': 'Loading...',
            'common.processing': 'Please wait...',
            'common.login_expired': 'Your login has expired, please login again.',
            'common.page_error': 'Page element error, unable to request login.',
            'common.already_ended': 'Ended',
            'common.less_than_minute': 'Less than 1 minute',
            'common.days': 'days',
            'common.hours': 'hours',
            'common.minutes': 'minutes',
            'common.instance': 'Instance',
            'common.loading_error': 'Loading failed',
            'common.server_error': 'Server error',
            'common.network_error': 'Network error',
            'common.unknown_error': 'Unknown error',
            'common.unable_load': 'Unable to load subscription info',
            'common.unable_get': 'Unable to get valid subscription info.',
            'common.api_error': 'Failed to get subscription info',
            'common.check_network': 'Failed to get subscription info. Please check network connection or try again later.',
            'common.data_format_error': 'Response data format error or operation unsuccessful',
            'common.backend_no_url': 'Backend did not return login URL',
            'common.resource_in_use': 'Resource is in use',
            'common.operation_canceled': 'Operation canceled',
            
            // Languages
            'lang.zh_CN': '中文',
            'lang.en': 'English',
            
            // Welcome page
            'welcome.meta_description': 'AI Carpools - Premium AI carpooling experience, multi-level carpooling service, professional technical support, save 90% of your costs',
            'welcome.page_title': 'AI Carpools - Premium AI Carpooling Service',
            'welcome.brand_name': 'AI Carpools',
            'welcome.logo_alt': 'AI Carpools Logo',
            'welcome.currency_symbol': '¥',
            'welcome.login': 'Login',
            'welcome.hero_title': 'Welcome to AI Carpools',
            'welcome.hero_subtitle': 'Premium AI assistant carpooling experience, save 90% of your costs',
            'welcome.view_plans': 'View Plans',
            'welcome.login_now': 'Login Now',
            'welcome.why_choose_us': 'Why Choose Us',
            'welcome.feature_safe_title': 'Safe & Reliable',
            'welcome.feature_safe_desc': 'Based on fingerprint browser technology, safe environment isolation, no intelligence reduction or account ban, native official accounts',
            'welcome.feature_flexible_title': 'Flexible Options',
            'welcome.feature_flexible_desc': '1-person exclusive, 5-person, 10-person and other plans to meet different needs',
            'welcome.feature_professional_title': 'Professional Service',
            'welcome.feature_professional_desc': 'Multiple members provide support, quick response, professional technical team ensures service quality',
            'welcome.pricing_section_title': 'Choose Your Plan',
            'welcome.loading_plans': 'Loading plans...',
            'welcome.no_plans_available': 'No plans available',
            'welcome.per_days': '/{0} days',
            'welcome.max_devices': 'Up to {0} devices',
            'welcome.login_to_subscribe': 'Login to Subscribe',
            'welcome.testimonial_content': 'After joining the 5-person share, GPT-4o generated reference images helped me break through design bottlenecks, reducing material preparation time from 2 hours to 20 minutes. The image quality amazed clients, making the entire design process more smooth and efficient.',
            'welcome.testimonial_author': 'Mr. Chen',
            'welcome.testimonial_role': 'Creative Company Partner',
            'welcome.cta_title': 'Ready to Get Started?',
            'welcome.cta_subtitle': 'Join 200+ premium members, enjoy high-quality AI services',
            'welcome.join_now': 'Join Now',
            'welcome.footer_copyright': '&copy; 2025 ChatGPTPro Club. All rights reserved.',
            'welcome.loading': 'Loading...'
        }
    };
    
    // 当前语言，默认为中文
    let currentLang = localStorage.getItem('language') || 'zh-CN';
    
    // 获取当前语言
    function getCurrentLanguage() {
        return currentLang;
    }
    
    // 设置语言
    function setLanguage(lang) {
        if (translations[lang]) {
            currentLang = lang;
            localStorage.setItem('language', lang);
            updatePageLanguage();
            return true;
        }
        return false;
    }
    
    // 获取翻译文本
    function t(key, ...args) {
        const translation = translations[currentLang][key] || translations['zh-CN'][key] || key;
        
        // 处理参数替换 {0}, {1}, etc.
        if (args.length > 0) {
            return translation.replace(/{(\d+)}/g, (match, number) => {
                return typeof args[number] !== 'undefined' ? args[number] : match;
            });
        }
        
        return translation;
    }
    
    // 更新页面上的所有文本
    function updatePageLanguage() {
        // 更新所有带有 data-i18n 属性的元素
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const args = element.getAttribute('data-i18n-args');
            
            let translatedText;
            if (args) {
                const argsArray = JSON.parse(args);
                translatedText = t(key, ...argsArray);
            } else {
                translatedText = t(key);
            }
            
            // 检查是否包含 HTML 实体或标签
            if (translatedText.includes('&') || translatedText.includes('<')) {
                element.innerHTML = translatedText;
            } else {
                element.textContent = translatedText;
            }
        });
        
        // 更新带有 data-i18n-placeholder 的元素
        document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            element.placeholder = t(key);
        });
        
        // 更新带有 data-i18n-title 的元素
        document.querySelectorAll('[data-i18n-title]').forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            element.title = t(key);
        });
        
        // 更新带有 data-i18n-alt 的元素
        document.querySelectorAll('[data-i18n-alt]').forEach(element => {
            const key = element.getAttribute('data-i18n-alt');
            element.alt = t(key);
        });
        
        // 更新页面标题
        // 优先使用页面特定的标题，如果有设置的话
        const pageTitleElement = document.querySelector('[data-page-title-key]');
        if (pageTitleElement) {
            const titleKey = pageTitleElement.getAttribute('data-page-title-key');
            document.title = t(titleKey);
        }
        
        // 更新HTML lang属性
        document.documentElement.lang = currentLang;
        
        // 触发自定义事件，通知其他组件语言已更改
        window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: currentLang } }));
    }
    
    // 初始化
    function init() {
        // 检测浏览器语言
        if (!localStorage.getItem('language')) {
            const browserLang = navigator.language || navigator.userLanguage;
            if (browserLang.startsWith('en')) {
                currentLang = 'en';
            } else {
                currentLang = 'zh-CN';
            }
            localStorage.setItem('language', currentLang);
        }
        
        // 页面加载完成后更新语言
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', updatePageLanguage);
        } else {
            updatePageLanguage();
        }
    }
    
    // 暴露公共API
    return {
        t,
        getCurrentLanguage,
        setLanguage,
        init,
        updatePageLanguage
    };
})();

// 初始化i18n
i18n.init();

// 将i18n暴露到全局，方便其他脚本使用
window.i18n = i18n;