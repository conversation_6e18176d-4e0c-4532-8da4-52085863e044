// --- 订单管理相关函数开始 ---

// 全局变量
var currentOrderPage = 1;
var orderPerPage = 20;
var orderFilters = {};

// 加载订单列表
async function loadOrders(page = 1) {
  try {
    const token = localStorage.getItem('token');
    
    // 构建查询参数
    const params = new URLSearchParams({
      page: page,
      per_page: orderPerPage,
      ...orderFilters
    });
    
    const response = await fetch(`/api/admin/payments?${params}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      if (handleUnauthorizedAdminResponse(response)) return;
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.success) {
      displayOrders(result.data.payments);
      updateOrderPagination(result.data);
      currentOrderPage = page;
    } else {
      showToast(result.message || '加载订单失败', 'danger');
    }
  } catch (error) {
    showToast(`加载订单时发生错误: ${error.message}`, 'danger');
    console.error('Error loading orders:', error);
  }
}

// 显示订单列表
function displayOrders(orders) {
  const tbody = document.getElementById('orders-table-body');
  if (!tbody) return;
  
  tbody.innerHTML = '';
  
  if (orders.length === 0) {
    tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">暂无订单数据</td></tr>';
    return;
  }
  
  orders.forEach(order => {
    const row = document.createElement('tr');
    
    // 状态样式映射
    const statusClasses = {
      'pending': 'badge bg-warning',
      'paid': 'badge bg-success',
      'cancelled': 'badge bg-secondary',
      'refunded': 'badge bg-info',
      'error': 'badge bg-danger'
    };
    
    const statusTexts = {
      'pending': '待支付',
      'paid': '已支付',
      'cancelled': '已取消',
      'refunded': '已退款',
      'error': '支付失败'
    };
    
    row.innerHTML = `
      <td>${escapeHtml(order.order_id)}</td>
      <td>${escapeHtml(order.user_email || '未知用户')}</td>
      <td>¥${order.amount.toFixed(2)}</td>
      <td><span class="${statusClasses[order.status] || 'badge bg-secondary'}">${statusTexts[order.status] || order.status}</span></td>
      <td>${order.payment_method || '-'}</td>
      <td>${order.subscription_type_name || '-'}</td>
      <td>${formatDateTime(order.created_at)}</td>
      <td>${order.paid_at ? formatDateTime(order.paid_at) : '-'}</td>
      <td>
        <button class="btn btn-sm btn-primary" onclick="viewOrderDetail(${order.id})" title="查看详情">
          <i class="bi bi-eye"></i>
        </button>
        ${order.status === 'pending' ? `
          <button class="btn btn-sm btn-success" onclick="confirmOrderPayment(${order.id})" title="确认支付">
            <i class="bi bi-check-circle"></i>
          </button>
        ` : ''}
      </td>
    `;
    
    tbody.appendChild(row);
  });
}

// 更新分页
function updateOrderPagination(data) {
  const pagination = document.getElementById('orders-pagination');
  if (!pagination) return;
  
  pagination.innerHTML = '';
  
  if (data.pages <= 1) return;
  
  // 上一页
  if (data.has_prev) {
    pagination.innerHTML += `
      <li class="page-item">
        <a class="page-link" href="#" onclick="loadOrders(${data.current_page - 1}); return false;">上一页</a>
      </li>
    `;
  }
  
  // 页码
  const startPage = Math.max(1, data.current_page - 2);
  const endPage = Math.min(data.pages, data.current_page + 2);
  
  for (let i = startPage; i <= endPage; i++) {
    pagination.innerHTML += `
      <li class="page-item ${i === data.current_page ? 'active' : ''}">
        <a class="page-link" href="#" onclick="loadOrders(${i}); return false;">${i}</a>
      </li>
    `;
  }
  
  // 下一页
  if (data.has_next) {
    pagination.innerHTML += `
      <li class="page-item">
        <a class="page-link" href="#" onclick="loadOrders(${data.current_page + 1}); return false;">下一页</a>
      </li>
    `;
  }
}

// 加载订单统计
async function loadOrderStatistics() {
  try {
    const token = localStorage.getItem('token');
    
    // 构建时间范围参数
    const params = new URLSearchParams();
    if (orderFilters.start_date) params.append('start_date', orderFilters.start_date);
    if (orderFilters.end_date) params.append('end_date', orderFilters.end_date);
    
    const response = await fetch(`/api/admin/payments/statistics?${params}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      if (handleUnauthorizedAdminResponse(response)) return;
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.success) {
      displayOrderStatistics(result.data);
    } else {
      showToast(result.message || '加载统计数据失败', 'danger');
    }
  } catch (error) {
    showToast(`加载统计数据时发生错误: ${error.message}`, 'danger');
    console.error('Error loading statistics:', error);
  }
}

// 显示订单统计
function displayOrderStatistics(stats) {
  // 更新统计卡片
  document.getElementById('total-orders').textContent = stats.summary.total_orders;
  document.getElementById('paid-orders').textContent = stats.summary.paid_orders;
  document.getElementById('pending-orders').textContent = stats.summary.pending_orders;
  document.getElementById('total-revenue').textContent = `¥${stats.summary.paid_amount.toFixed(2)}`;
}

// 搜索订单
function searchOrders() {
  // 收集筛选条件
  orderFilters = {
    status: document.getElementById('order-status-filter').value,
    payment_method: document.getElementById('payment-method-filter').value,
    start_date: document.getElementById('start-date-filter').value,
    end_date: document.getElementById('end-date-filter').value,
    order_id: document.getElementById('order-id-filter').value,
    user_email: document.getElementById('user-email-filter').value
  };
  
  // 移除空值
  Object.keys(orderFilters).forEach(key => {
    if (!orderFilters[key]) delete orderFilters[key];
  });
  
  // 重新加载订单和统计
  loadOrders(1);
  loadOrderStatistics();
}

// 重置筛选条件
function resetOrderFilters() {
  document.getElementById('order-status-filter').value = '';
  document.getElementById('payment-method-filter').value = '';
  document.getElementById('start-date-filter').value = '';
  document.getElementById('end-date-filter').value = '';
  document.getElementById('order-id-filter').value = '';
  document.getElementById('user-email-filter').value = '';
  
  orderFilters = {};
  loadOrders(1);
  loadOrderStatistics();
}

// 查看订单详情
async function viewOrderDetail(orderId) {
  try {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`/api/admin/payments/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      if (handleUnauthorizedAdminResponse(response)) return;
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.success) {
      showOrderDetailModal(result.data);
    } else {
      showToast(result.message || '获取订单详情失败', 'danger');
    }
  } catch (error) {
    showToast(`获取订单详情时发生错误: ${error.message}`, 'danger');
    console.error('Error viewing order detail:', error);
  }
}

// 显示订单详情模态框
function showOrderDetailModal(order) {
  // 状态样式映射
  const statusClasses = {
    'pending': 'badge bg-warning',
    'paid': 'badge bg-success',
    'cancelled': 'badge bg-secondary',
    'refunded': 'badge bg-info',
    'error': 'badge bg-danger'
  };
  
  const statusTexts = {
    'pending': '待支付',
    'paid': '已支付',
    'cancelled': '已取消',
    'refunded': '已退款',
    'error': '支付失败'
  };
  
  // 创建模态框HTML
  const modalHtml = `
    <div class="modal fade" id="orderDetailModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">订单详情 - ${order.order_id}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-md-6">
                <h6>基本信息</h6>
                <p><strong>订单号：</strong>${order.order_id}</p>
                <p><strong>用户：</strong>${order.user_email || '未知'}</p>
                <p><strong>金额：</strong>¥${order.amount.toFixed(2)}</p>
                <p><strong>状态：</strong><span class="${statusClasses[order.status] || 'badge bg-secondary'}">${statusTexts[order.status] || order.status}</span></p>
                <p><strong>支付方式：</strong>${order.payment_method || '-'}</p>
                ${order.payment_id ? `<p><strong>支付平台ID：</strong>${order.payment_id}</p>` : ''}
                ${order.transaction_id ? `<p><strong>交易号：</strong>${order.transaction_id}</p>` : ''}
              </div>
              <div class="col-md-6">
                <h6>时间信息</h6>
                <p><strong>创建时间：</strong>${formatDateTime(order.created_at)}</p>
                <p><strong>支付时间：</strong>${order.paid_at ? formatDateTime(order.paid_at) : '-'}</p>
              </div>
            </div>
            
            ${order.subscription_id || order.subscription_type_name || order.subscription_status || order.subscription_end_date ? `
            <hr>
            <div class="row">
              <div class="col-12">
                <h6>订阅信息</h6>
                <div class="row">
                  <div class="col-md-6">
                    ${order.subscription_type_name ? `<p><strong>订阅类型：</strong>${order.subscription_type_name}</p>` : ''}
                    ${order.subscription_type_price !== null && order.subscription_type_price !== undefined ? `<p><strong>套餐价格：</strong>¥${order.subscription_type_price.toFixed(2)}</p>` : ''}
                    ${order.subscription_days ? `<p><strong>订阅天数：</strong>${order.subscription_days} 天</p>` : ''}
                    ${order.subscription_max_devices ? `<p><strong>设备容量：</strong>${order.subscription_max_devices} 台</p>` : ''}
                  </div>
                  <div class="col-md-6">
                    ${order.subscription_id ? `<p><strong>订阅ID：</strong>${order.subscription_id}</p>` : ''}
                    ${order.subscription_status ? `<p><strong>订阅状态：</strong><span class="badge ${order.subscription_status === '有效' ? 'bg-success' : 'bg-secondary'}">${order.subscription_status}</span></p>` : ''}
                    ${order.subscription_start_date ? `<p><strong>开始时间：</strong>${formatDateTime(order.subscription_start_date)}</p>` : ''}
                    ${order.subscription_end_date ? `<p><strong>到期时间：</strong>${formatDateTime(order.subscription_end_date)}</p>` : ''}
                    ${order.subscription_instance_name ? `<p><strong>所属实例：</strong>${order.subscription_instance_name}</p>` : ''}
                  </div>
                </div>
                ${order.subscription_remark ? `
                <div class="row mt-2">
                  <div class="col-12">
                    <p><strong>订阅备注：</strong>${order.subscription_remark}</p>
                  </div>
                </div>
                ` : ''}
              </div>
            </div>
            ` : ''}
            
            ${order.remarks ? `
            <hr>
            <div class="mt-3">
              <h6>备注</h6>
              <p>${order.remarks}</p>
            </div>
            ` : ''}
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
          </div>
        </div>
      </div>
    </div>
  `;
  
  // 移除旧的模态框
  const oldModal = document.getElementById('orderDetailModal');
  if (oldModal) oldModal.remove();
  
  // 添加新模态框
  document.body.insertAdjacentHTML('beforeend', modalHtml);
  
  // 显示模态框
  const modal = new bootstrap.Modal(document.getElementById('orderDetailModal'));
  modal.show();
}

// 确认订单支付
async function confirmOrderPayment(orderId) {
  if (!confirm('确定要手动确认该订单为已支付状态吗？\n\n此操作将：\n1. 将订单状态更新为已支付\n2. 自动为用户创建/延长相应的订阅服务')) return;
  
  try {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`/api/admin/payments/${orderId}/status`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ status: 'paid' })
    });
    
    if (!response.ok) {
      if (handleUnauthorizedAdminResponse(response)) return;
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.success) {
      showToast('订单状态更新成功', 'success');
      loadOrders(currentOrderPage);
      loadOrderStatistics();
    } else {
      showToast(result.message || '更新订单状态失败', 'danger');
    }
  } catch (error) {
    showToast(`更新订单状态时发生错误: ${error.message}`, 'danger');
    console.error('Error confirming payment:', error);
  }
}

// 导出订单
function exportOrders() {
  // TODO: 实现订单导出功能
  showToast('订单导出功能开发中...', 'info');
}

// formatDateTime 函数已移至 utils.js，这里使用全局函数

// escapeHtml 函数已移至 utils.js，这里使用全局函数

// --- 订单管理相关函数结束 ---