/**
 * 通用工具函数库
 * 包含时间处理、HTML转义等常用功能
 */

// 时间处理函数 - 根据浏览器本地时区显示时间
const TimeUtils = {
  /**
   * 格式化日期时间为本地时间
   * @param {string} dateTimeStr - UTC时间字符串
   * @returns {string} 格式化后的本地时间字符串
   */
  formatDateTime: function(dateTimeStr) {
    if (!dateTimeStr) return '-';
    
    try {
      // 确保时间字符串被解释为UTC
      let utcDateString = dateTimeStr;
      // 如果时间字符串没有时区信息（不包含Z或+/-），添加Z表示UTC
      if (!dateTimeStr.includes('Z') && !dateTimeStr.includes('+') && !dateTimeStr.includes('-', 10)) {
        utcDateString = dateTimeStr + 'Z';
      }
      
      const date = new Date(utcDateString);
      if (isNaN(date.getTime())) {
        // 如果解析失败，尝试原始字符串
        const fallbackDate = new Date(dateTimeStr);
        if (isNaN(fallbackDate.getTime())) return dateTimeStr;
        return this._formatToLocal(fallbackDate);
      }
      
      return this._formatToLocal(date);
    } catch (e) {
      console.error("日期格式化错误:", e, dateTimeStr);
      return dateTimeStr;
    }
  },

  /**
   * 内部方法：将Date对象格式化为本地时间字符串
   * @private
   */
  _formatToLocal: function(date) {
    // 使用浏览器默认的本地时区
    const localTimeStr = date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
    
    // 将格式从 "2024/01/15, 18:30:00" 转换为 "2024/01/15 18:30:00"
    return localTimeStr.replace(/,\s*/, ' ');
  },

  /**
   * 将UTC时间转换为本地时间的ISO字符串（用于datetime-local输入框）
   * @param {string} utcDateString - UTC时间字符串
   * @returns {string} YYYY-MM-DDTHH:MM 格式的本地时间
   */
  toLocalISOString: function(utcDateString) {
    if (!utcDateString) return '';
    try {
      // 确保时间字符串被解释为UTC
      let dateStr = utcDateString;
      if (!utcDateString.includes('Z') && !utcDateString.includes('+') && !utcDateString.includes('-', 10)) {
        dateStr = utcDateString + 'Z';
      }
      
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '';
      
      // 格式化为本地时间 YYYY-MM-DDTHH:MM
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day}T${hours}:${minutes}`;
    } catch (e) {
      console.error('toLocalISOString 错误:', e);
      return '';
    }
  },

  /**
   * 将本地时间的ISO字符串转换为UTC（用于提交到后端）
   * @param {string} localDateTimeString - 本地时间字符串
   * @returns {string|null} UTC ISO字符串
   */
  localToUTCString: function(localDateTimeString) {
    if (!localDateTimeString) return null;
    try {
      // 处理可能缺少秒数的日期时间字符串
      let formattedStr = localDateTimeString;
      if (!formattedStr.includes(':')) {
        formattedStr += 'T00:00:00';
      } else if (formattedStr.split(':').length === 2) {
        formattedStr += ':00';
      }
      
      // 创建Date对象，浏览器会将其解释为本地时间
      const localDate = new Date(formattedStr);
      if (isNaN(localDate.getTime())) return null;
      
      // 直接返回UTC格式
      return localDate.toISOString();
    } catch (e) {
      console.error('localToUTCString 错误:', e);
      return null;
    }
  }
};

// HTML处理函数
const HtmlUtils = {
  /**
   * HTML转义，防止XSS攻击
   * @param {string} str - 需要转义的字符串
   * @returns {string} 转义后的字符串
   */
  escapeHtml: function(str) {
    if (!str) return '';
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
  }
};

// Toast通知函数
const NotificationUtils = {
  /**
   * 显示 Toast 通知
   * @param {string} message - 通知消息
   * @param {string} type - 通知类型 ('info', 'success', 'warning', 'danger')
   * @param {number} duration - 持续时间（毫秒）
   */
  showToast: function(message, type = 'info', duration = 5000) {
    const toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
      console.warn('Toast容器不存在');
      return;
    }

    const toastId = 'toast-' + Date.now();
    const formattedMessage = message.replace(/\n/g, '<br>');
    const toastHtml = `
      <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'info' ? 'primary' : type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
          <div class="toast-body" style="white-space: pre-wrap;">
            ${formattedMessage}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
      </div>
    `;
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: duration });
    toast.show();

    // 自动清理DOM
    toastElement.addEventListener('hidden.bs.toast', function () {
      toastElement.remove();
    });
  }
};

// 导出工具函数（兼容性考虑，同时支持全局变量）
if (typeof window !== 'undefined') {
  // 将主要函数挂载到window对象，保持向后兼容
  window.formatDateTime = TimeUtils.formatDateTime.bind(TimeUtils);
  window.toLocalISOString = TimeUtils.toLocalISOString.bind(TimeUtils);
  window.localToUTCString = TimeUtils.localToUTCString.bind(TimeUtils);
  window.escapeHtml = HtmlUtils.escapeHtml.bind(HtmlUtils);
  window.showToast = NotificationUtils.showToast.bind(NotificationUtils);
  
  // 兼容旧代码中的函数名（北京时间相关的函数现在都使用本地时间）
  window.toBeijingISOString = TimeUtils.toLocalISOString.bind(TimeUtils);
  window.beijingToUTCString = TimeUtils.localToUTCString.bind(TimeUtils);
  window.toUTCISOString = TimeUtils.localToUTCString.bind(TimeUtils);
  
  // 同时导出工具对象
  window.TimeUtils = TimeUtils;
  window.HtmlUtils = HtmlUtils;
  window.NotificationUtils = NotificationUtils;
  
  // 添加测试函数
  window.testTimeConversion = function() {
    const testCases = [
      '2024-01-15T10:30:00Z',
      '2024-01-15T10:30:00.000Z',
      '2024-01-15T10:30:00+00:00',
      '2025-05-25T07:47:00.129500',
      new Date().toISOString()
    ];
    
    console.log('时间转换测试:');
    testCases.forEach(testTime => {
      console.log(`UTC: ${testTime} -> 本地时间: ${formatDateTime(testTime)}`);
    });
  };
}