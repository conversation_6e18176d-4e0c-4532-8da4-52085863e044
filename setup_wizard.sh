#!/bin/bash

# AI拼车共享平台配置向导
# 用于快速配置部署后的系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置文件路径
CONFIG_FILE="/opt/aicarpools/.env.production"
PROJECT_DIR="/opt/aicarpools"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_question() {
    echo -e "${CYAN}[?]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${GREEN}"
    echo "=================================="
    echo "  AI拼车共享平台配置向导"
    echo "=================================="
    echo -e "${NC}"
    echo "本向导将帮助您快速配置系统的基本设置"
    echo ""
}

# 检查环境
check_environment() {
    log_step "检查环境..."
    
    if [[ ! -d "$PROJECT_DIR" ]]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        log_info "请先运行部署脚本: curl -fsSL https://raw.githubusercontent.com/fzlzjerry/gpt_share_automation/main/deploy.sh | bash"
        exit 1
    fi
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    cd "$PROJECT_DIR"
    log_info "环境检查通过"
}

# 配置域名
configure_domain() {
    log_step "配置域名设置"
    
    echo ""
    log_question "请输入您的主域名 (例如: aicarpools.com):"
    read -p "> " DOMAIN
    
    if [[ -z "$DOMAIN" ]]; then
        log_warn "使用默认域名: aicarpools.com"
        DOMAIN="aicarpools.com"
    fi
    
    # 更新配置文件
    sed -i "s/DOMAIN=.*/DOMAIN=$DOMAIN/" "$CONFIG_FILE"
    sed -i "s|DASHBOARD_URL=.*|DASHBOARD_URL=https://dashboard.$DOMAIN|" "$CONFIG_FILE"
    sed -i "s|AUTH_URL=.*|AUTH_URL=https://auth.$DOMAIN|" "$CONFIG_FILE"
    
    log_info "域名配置完成: $DOMAIN"
}

# 配置邮件服务
configure_email() {
    log_step "配置邮件服务"
    
    echo ""
    echo "邮件服务用于发送验证码和通知，请选择邮件服务商："
    echo "1) Gmail"
    echo "2) QQ邮箱"
    echo "3) 163邮箱"
    echo "4) 自定义SMTP"
    
    read -p "请选择 (1-4): " EMAIL_CHOICE
    
    case $EMAIL_CHOICE in
        1)
            MAIL_SERVER="smtp.gmail.com"
            MAIL_PORT="587"
            MAIL_USE_TLS="true"
            MAIL_USE_SSL="false"
            ;;
        2)
            MAIL_SERVER="smtp.qq.com"
            MAIL_PORT="587"
            MAIL_USE_TLS="true"
            MAIL_USE_SSL="false"
            ;;
        3)
            MAIL_SERVER="smtp.163.com"
            MAIL_PORT="465"
            MAIL_USE_TLS="false"
            MAIL_USE_SSL="true"
            ;;
        4)
            log_question "请输入SMTP服务器地址:"
            read -p "> " MAIL_SERVER
            log_question "请输入SMTP端口 (通常是587或465):"
            read -p "> " MAIL_PORT
            log_question "是否使用TLS? (y/n):"
            read -p "> " USE_TLS
            if [[ $USE_TLS == "y" || $USE_TLS == "Y" ]]; then
                MAIL_USE_TLS="true"
                MAIL_USE_SSL="false"
            else
                MAIL_USE_TLS="false"
                MAIL_USE_SSL="true"
            fi
            ;;
        *)
            log_warn "无效选择，跳过邮件配置"
            return
            ;;
    esac
    
    log_question "请输入发件邮箱地址:"
    read -p "> " MAIL_USERNAME
    
    log_question "请输入邮箱密码或应用专用密码:"
    read -s -p "> " MAIL_PASSWORD
    echo ""
    
    if [[ -z "$MAIL_USERNAME" || -z "$MAIL_PASSWORD" ]]; then
        log_warn "邮箱信息不完整，跳过邮件配置"
        return
    fi
    
    # 更新配置文件
    sed -i "s/MAIL_SERVER=.*/MAIL_SERVER=$MAIL_SERVER/" "$CONFIG_FILE"
    sed -i "s/MAIL_PORT=.*/MAIL_PORT=$MAIL_PORT/" "$CONFIG_FILE"
    sed -i "s/MAIL_USE_TLS=.*/MAIL_USE_TLS=$MAIL_USE_TLS/" "$CONFIG_FILE"
    sed -i "s/MAIL_USE_SSL=.*/MAIL_USE_SSL=$MAIL_USE_SSL/" "$CONFIG_FILE"
    sed -i "s/MAIL_USERNAME=.*/MAIL_USERNAME=$MAIL_USERNAME/" "$CONFIG_FILE"
    sed -i "s/MAIL_PASSWORD=.*/MAIL_PASSWORD=$MAIL_PASSWORD/" "$CONFIG_FILE"
    sed -i "s|MAIL_DEFAULT_SENDER=.*|MAIL_DEFAULT_SENDER=\"AI拼车共享平台\" <$MAIL_USERNAME>|" "$CONFIG_FILE"
    
    log_info "邮件服务配置完成"
}

# 配置AdsPower
configure_adspower() {
    log_step "配置AdsPower API"
    
    echo ""
    log_question "请输入AdsPower API地址 (默认: http://local.adspower.net:50325):"
    read -p "> " ADSPOWER_API_URL
    
    if [[ -z "$ADSPOWER_API_URL" ]]; then
        ADSPOWER_API_URL="http://local.adspower.net:50325"
    fi
    
    log_question "请输入AdsPower API密钥 (可选):"
    read -p "> " ADSPOWER_API_KEY
    
    # 更新配置文件
    sed -i "s|ADSPOWER_API_URL=.*|ADSPOWER_API_URL=$ADSPOWER_API_URL|" "$CONFIG_FILE"
    if [[ -n "$ADSPOWER_API_KEY" ]]; then
        sed -i "s/ADSPOWER_API_KEY=.*/ADSPOWER_API_KEY=$ADSPOWER_API_KEY/" "$CONFIG_FILE"
    fi
    
    log_info "AdsPower API配置完成"
}

# 创建管理员账号
create_admin_user() {
    log_step "创建管理员账号"
    
    echo ""
    log_question "请输入管理员邮箱地址:"
    read -p "> " ADMIN_EMAIL
    
    log_question "请输入管理员密码:"
    read -s -p "> " ADMIN_PASSWORD
    echo ""
    
    if [[ -z "$ADMIN_EMAIL" || -z "$ADMIN_PASSWORD" ]]; then
        log_warn "管理员信息不完整，跳过创建"
        return
    fi
    
    # 创建管理员账号
    docker-compose exec -T app python -c "
from adspower_manager.models import User, db
from adspower_manager import create_app
import sys

app = create_app()
with app.app_context():
    # 检查是否已存在
    existing_admin = User.query.filter_by(email='$ADMIN_EMAIL').first()
    if existing_admin:
        print('管理员账号已存在，更新密码')
        existing_admin.set_password('$ADMIN_PASSWORD')
        existing_admin.is_admin = True
    else:
        print('创建新管理员账号')
        admin = User(email='$ADMIN_EMAIL', is_admin=True)
        admin.set_password('$ADMIN_PASSWORD')
        db.session.add(admin)
    
    try:
        db.session.commit()
        print('管理员账号创建/更新成功')
    except Exception as e:
        print(f'创建管理员账号失败: {e}')
        sys.exit(1)
"
    
    if [[ $? -eq 0 ]]; then
        log_info "管理员账号配置完成"
    else
        log_error "管理员账号创建失败"
    fi
}

# 配置系统设置
configure_system() {
    log_step "配置系统设置"
    
    echo ""
    log_question "是否启用WebDriver功能? (用于浏览器自动化) (y/n):"
    read -p "> " ENABLE_WEBDRIVER
    
    if [[ $ENABLE_WEBDRIVER == "y" || $ENABLE_WEBDRIVER == "Y" ]]; then
        sed -i "s/ENABLE_WEBDRIVER=.*/ENABLE_WEBDRIVER=true/" "$CONFIG_FILE"
        log_info "WebDriver功能已启用"
    else
        sed -i "s/ENABLE_WEBDRIVER=.*/ENABLE_WEBDRIVER=false/" "$CONFIG_FILE"
        log_info "WebDriver功能已禁用"
    fi
    
    log_question "是否使用协议模式? (推荐，更稳定) (y/n):"
    read -p "> " USE_PROTOCOL_MODE
    
    if [[ $USE_PROTOCOL_MODE == "y" || $USE_PROTOCOL_MODE == "Y" ]]; then
        sed -i "s/USE_PROTOCOL_MODE=.*/USE_PROTOCOL_MODE=true/" "$CONFIG_FILE"
        log_info "协议模式已启用"
    else
        sed -i "s/USE_PROTOCOL_MODE=.*/USE_PROTOCOL_MODE=false/" "$CONFIG_FILE"
        log_info "协议模式已禁用"
    fi
}

# 测试配置
test_configuration() {
    log_step "测试配置"
    
    echo ""
    log_info "重启服务以应用新配置..."
    docker-compose restart
    
    # 等待服务启动
    sleep 15
    
    # 测试服务状态
    if docker-compose ps | grep -q "Up"; then
        log_info "✅ 服务启动成功"
    else
        log_error "❌ 服务启动失败"
        log_info "请检查日志: docker-compose logs -f"
        return 1
    fi
    
    # 测试邮件配置
    log_info "测试邮件配置..."
    docker-compose exec -T app python -c "
from adspower_manager import create_app
from flask_mail import Mail, Message
import sys

app = create_app()
with app.app_context():
    try:
        mail = Mail(app)
        print('邮件配置测试通过')
    except Exception as e:
        print(f'邮件配置测试失败: {e}')
" 2>/dev/null
    
    log_info "配置测试完成"
}

# 显示完成信息
show_completion() {
    log_step "配置完成！"
    
    echo ""
    echo -e "${GREEN}=================================="
    echo "  配置向导完成！"
    echo "==================================${NC}"
    echo ""
    echo "系统信息:"
    echo "  域名: $DOMAIN"
    echo "  项目目录: $PROJECT_DIR"
    echo "  配置文件: $CONFIG_FILE"
    echo ""
    echo "访问地址:"
    echo "  主页: https://$DOMAIN"
    echo "  管理后台: https://$DOMAIN/admin"
    echo ""
    if [[ -n "$ADMIN_EMAIL" ]]; then
        echo "管理员账号:"
        echo "  邮箱: $ADMIN_EMAIL"
        echo "  密码: [已设置]"
        echo ""
    fi
    echo "常用命令:"
    echo "  查看服务状态: cd $PROJECT_DIR && docker-compose ps"
    echo "  查看日志: cd $PROJECT_DIR && docker-compose logs -f"
    echo "  重启服务: cd $PROJECT_DIR && docker-compose restart"
    echo ""
    echo "下一步:"
    echo "1. 访问管理后台配置订阅类型"
    echo "2. 生成兑换码供用户使用"
    echo "3. 配置AdsPower账号"
    echo ""
    log_info "享受使用AI拼车共享平台！"
}

# 主函数
main() {
    show_welcome
    check_environment
    configure_domain
    configure_email
    configure_adspower
    create_admin_user
    configure_system
    test_configuration
    show_completion
}

# 执行主函数
main "$@"
