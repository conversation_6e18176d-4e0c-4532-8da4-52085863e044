# Flask配置
# APP_ENV: testing (测试环境,本地登录) 或 production (生产环境,单点登录)
APP_ENV=production
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
PORT=5000

# 数据库配置
DATABASE_URL=sqlite:///instance/app.db

# JWT配置
JWT_ACCESS_TOKEN_EXPIRES_HOURS=24
JWT_REFRESH_TOKEN_EXPIRES_DAYS=30

# AdsPower配置
ADSPOWER_API_BASE=https://api-global.adspower.net/v1
ADSPOWER_API_KEY=your-adspower-api-key

# 邮件配置
MAIL_SERVER=smtp.163.com
MAIL_PORT=465
MAIL_USE_TLS=False
MAIL_USE_SSL=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_DEFAULT_SENDER="AI服务拼车共享平台 <<EMAIL>>"
EMAIL_CODE_EXPIRY_MINUTES=10

# 密码策略
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_NUMBER=True
PASSWORD_REQUIRE_SPECIAL_CHAR=False
FAILED_LOGIN_MAX_ATTEMPTS=5
FAILED_LOGIN_LOCKOUT_TIME=300

# 支付配置已删除 - 不再使用支付功能

# 域名配置
DOMAIN=aicarpools.com
DASHBOARD_URL=https://dashboard.aicarpools.com
AUTH_URL=https://auth.aicarpools.com

# OIDC (Keycloak) 配置
OIDC_ISSUER_URL=https://auth.example.com/realms/myrealm
OIDC_CLIENT_ID=my-flask-client
OIDC_CLIENT_SECRET=your-oidc-client-secret
OIDC_REDIRECT_URI=https://yourdomain.com/api/oidc/callback
OIDC_SCOPES=openid email profile
OIDC_JIT_USER_PROVISIONING=True

# WebDriver配置
WEBDRIVER_POOL_SIZE=10
WEBDRIVER_DRIVER_TIMEOUT=1800
WEBDRIVER_CHECK_INTERVAL=300

# 默认用户配置（仅用于初始化）
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=changeme123
DEFAULT_USER_EMAIL=<EMAIL>
DEFAULT_USER_PASSWORD=userpass123
CREATE_DEFAULT_USER=False

# 默认订阅类型配置
CREATE_DEFAULT_SUB_TYPE=True
DEFAULT_SUB_MAX_DEVICES=1
DEFAULT_SUB_PRICE=1.0
DEFAULT_SUB_DISCOUNT=100
DEFAULT_SUB_DAYS=1

# 维护模式
MAINTENANCE_MODE=false