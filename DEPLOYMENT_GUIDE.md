# 🚀 AI拼车共享平台部署教程

本教程将指导您如何在服务器上部署AI拼车共享平台，并绑定到域名 `aicarpools.com`。

## 📋 前置要求

### 服务器要求
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / Debian 10+
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 20GB，推荐 50GB+
- **CPU**: 最低 2核，推荐 4核+
- **网络**: 公网IP，支持80/443端口

### 域名要求
- 已购买域名 `aicarpools.com`
- 域名DNS已指向服务器IP
- 建议配置以下子域名：
  - `dashboard.aicarpools.com` (主应用)
  - `auth.aicarpools.com` (认证服务，可选)

## 🛠️ 第一步：服务器环境准备

### 1.1 更新系统
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### 1.2 安装基础软件
```bash
# Ubuntu/Debian
sudo apt install -y git curl wget vim nginx python3 python3-pip python3-venv \
    build-essential libssl-dev libffi-dev python3-dev \
    sqlite3 supervisor certbot python3-certbot-nginx

# CentOS/RHEL
sudo yum install -y git curl wget vim nginx python3 python3-pip \
    gcc openssl-devel libffi-devel python3-devel \
    sqlite supervisor certbot python3-certbot-nginx
```

### 1.3 安装Docker (推荐)
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到docker组
sudo usermod -aG docker $USER
```

## 📦 第二步：下载和配置项目

### 2.1 克隆项目
```bash
# 创建项目目录
sudo mkdir -p /opt/aicarpools
sudo chown $USER:$USER /opt/aicarpools
cd /opt/aicarpools

# 克隆项目
git clone https://github.com/fzlzjerry/gpt_share_automation.git .
```

### 2.2 创建环境配置文件
```bash
# 创建生产环境配置
cp .env.example .env.production

# 编辑配置文件
vim .env.production
```

### 2.3 配置环境变量
在 `.env.production` 中配置以下内容：

```bash
# === 基础配置 ===
FLASK_ENV=production
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-too

# === 数据库配置 ===
# 使用SQLite（简单部署）
DATABASE_URL=sqlite:///instance/production.db
# 或使用PostgreSQL（推荐生产环境）
# DATABASE_URL=postgresql://username:password@localhost:5432/aicarpools

# === 域名配置 ===
DOMAIN=aicarpools.com
DASHBOARD_URL=https://dashboard.aicarpools.com
AUTH_URL=https://auth.aicarpools.com

# === 邮件配置 ===
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USE_SSL=false
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-app-password
MAIL_DEFAULT_SENDER="AI拼车共享平台" <<EMAIL>>

# === OIDC配置（可选，如果使用SSO）===
USE_SSO=false
OIDC_ISSUER_URL=https://auth.aicarpools.com/realms/aicarpools.com
OIDC_CLIENT_ID=aicarpools-client
OIDC_CLIENT_SECRET=your-oidc-client-secret
OIDC_REDIRECT_URI=https://dashboard.aicarpools.com/api/oidc/callback

# === AdsPower API配置 ===
ADSPOWER_API_URL=http://local.adspower.net:50325
ADSPOWER_API_KEY=your-adspower-api-key
USE_PROTOCOL_MODE=true

# === 其他配置 ===
ENABLE_WEBDRIVER=false
CLAUDE_SYNC_API_KEY=your-claude-sync-api-key
```

## 🐳 第三步：Docker部署（推荐）

### 3.1 创建Docker Compose文件
```bash
vim docker-compose.yml
```

```yaml
version: '3.8'

services:
  app:
    build: .
    container_name: aicarpools-app
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      - ./instance:/app/instance
      - ./logs:/app/logs
      - ./.env.production:/app/.env
    environment:
      - FLASK_ENV=production
    depends_on:
      - redis
    networks:
      - aicarpools-network

  redis:
    image: redis:7-alpine
    container_name: aicarpools-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - aicarpools-network

  nginx:
    image: nginx:alpine
    container_name: aicarpools-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - /etc/letsencrypt:/etc/letsencrypt
    depends_on:
      - app
    networks:
      - aicarpools-network

volumes:
  redis_data:

networks:
  aicarpools-network:
    driver: bridge
```

### 3.2 创建Nginx配置
```bash
vim nginx.conf
```

```nginx
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript 
               application/javascript application/xml+rss 
               application/json application/xml;

    # 上游服务器
    upstream aicarpools_app {
        server app:5000;
    }

    # HTTP重定向到HTTPS
    server {
        listen 80;
        server_name aicarpools.com www.aicarpools.com dashboard.aicarpools.com;
        return 301 https://$server_name$request_uri;
    }

    # 主应用服务器
    server {
        listen 443 ssl http2;
        server_name aicarpools.com www.aicarpools.com dashboard.aicarpools.com;

        # SSL配置
        ssl_certificate /etc/letsencrypt/live/aicarpools.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/aicarpools.com/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # 静态文件缓存
        location /static/ {
            proxy_pass http://aicarpools_app;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # API路由
        location /api/ {
            proxy_pass http://aicarpools_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 管理员路由
        location /admin/ {
            proxy_pass http://aicarpools_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 主应用
        location / {
            proxy_pass http://aicarpools_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
    }
}
```

### 3.3 构建和启动服务
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f app
```

## 🔒 第四步：SSL证书配置

### 4.1 获取Let's Encrypt证书
```bash
# 停止nginx（如果正在运行）
sudo systemctl stop nginx
# 或者停止Docker中的nginx
docker-compose stop nginx

# 获取证书
sudo certbot certonly --standalone -d aicarpools.com -d www.aicarpools.com -d dashboard.aicarpools.com

# 重启nginx
docker-compose start nginx
```

### 4.2 设置证书自动续期
```bash
# 创建续期脚本
sudo vim /etc/cron.d/certbot-renew
```

```bash
# 每天凌晨2点检查证书续期
0 2 * * * root certbot renew --quiet --deploy-hook "docker-compose -f /opt/aicarpools/docker-compose.yml restart nginx"
```

## 🗄️ 第五步：数据库初始化

### 5.1 初始化数据库
```bash
# 进入应用容器
docker-compose exec app bash

# 初始化数据库
flask db upgrade
flask init-db

# 创建管理员账号（可选）
python -c "
from adspower_manager.models import User, db
from adspower_manager import create_app
app = create_app()
with app.app_context():
    admin = User(email='<EMAIL>', is_admin=True)
    admin.set_password('your-admin-password')
    db.session.add(admin)
    db.session.commit()
    print('管理员账号创建成功')
"

# 退出容器
exit
```

## 🔧 第六步：系统服务配置

### 6.1 创建系统服务文件
```bash
sudo vim /etc/systemd/system/aicarpools.service
```

```ini
[Unit]
Description=AI拼车共享平台
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/aicarpools
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
```

### 6.2 启用服务
```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable aicarpools.service

# 启动服务
sudo systemctl start aicarpools.service

# 检查状态
sudo systemctl status aicarpools.service
```

## 📊 第七步：监控和日志

### 7.1 设置日志轮转
```bash
sudo vim /etc/logrotate.d/aicarpools
```

```bash
/opt/aicarpools/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker-compose -f /opt/aicarpools/docker-compose.yml restart app
    endscript
}
```

### 7.2 监控脚本
```bash
vim /opt/aicarpools/monitor.sh
```

```bash
#!/bin/bash
# AI拼车共享平台监控脚本

LOG_FILE="/opt/aicarpools/logs/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查服务状态
check_service() {
    if docker-compose ps | grep -q "Up"; then
        echo "[$DATE] 服务运行正常" >> $LOG_FILE
        return 0
    else
        echo "[$DATE] 服务异常，尝试重启" >> $LOG_FILE
        docker-compose restart
        return 1
    fi
}

# 检查磁盘空间
check_disk() {
    DISK_USAGE=$(df /opt/aicarpools | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $DISK_USAGE -gt 80 ]; then
        echo "[$DATE] 磁盘空间不足: ${DISK_USAGE}%" >> $LOG_FILE
    fi
}

# 执行检查
cd /opt/aicarpools
check_service
check_disk
```

```bash
# 设置执行权限
chmod +x /opt/aicarpools/monitor.sh

# 添加到crontab
echo "*/5 * * * * /opt/aicarpools/monitor.sh" | sudo crontab -
```

## 🌐 第八步：域名DNS配置

### 8.1 DNS记录配置
在您的域名DNS管理面板中添加以下记录：

```
类型    名称                    值                  TTL
A       @                      YOUR_SERVER_IP      300
A       www                    YOUR_SERVER_IP      300
A       dashboard              YOUR_SERVER_IP      300
A       auth                   YOUR_SERVER_IP      300
CNAME   *.aicarpools.com       aicarpools.com      300
```

### 8.2 验证DNS解析
```bash
# 检查DNS解析
nslookup aicarpools.com
nslookup dashboard.aicarpools.com

# 检查网站访问
curl -I https://aicarpools.com
curl -I https://dashboard.aicarpools.com
```

## ✅ 第九步：部署验证

### 9.1 功能测试
1. **访问主页**: https://aicarpools.com
2. **用户注册**: 测试邮箱验证功能
3. **用户登录**: 测试登录流程
4. **管理后台**: https://aicarpools.com/admin
5. **API接口**: 测试关键API功能

### 9.2 性能测试
```bash
# 安装测试工具
sudo apt install apache2-utils

# 并发测试
ab -n 1000 -c 10 https://aicarpools.com/

# 监控资源使用
htop
docker stats
```

## 🔧 故障排除

### 常见问题

1. **服务无法启动**
```bash
# 查看详细日志
docker-compose logs app
docker-compose logs nginx

# 检查端口占用
sudo netstat -tlnp | grep :5000
sudo netstat -tlnp | grep :80
```

2. **SSL证书问题**
```bash
# 检查证书状态
sudo certbot certificates

# 手动续期
sudo certbot renew --dry-run
```

3. **数据库问题**
```bash
# 进入容器检查数据库
docker-compose exec app bash
sqlite3 instance/production.db ".tables"
```

4. **邮件发送问题**
```bash
# 测试邮件配置
docker-compose exec app python -c "
from adspower_manager import create_app
from adspower_manager.services.email_service import EmailService
app = create_app()
with app.app_context():
    EmailService.send_verification_code('<EMAIL>', '123456')
"
```

## 📈 性能优化

### 1. 数据库优化
```bash
# 如果使用PostgreSQL
sudo apt install postgresql postgresql-contrib
sudo -u postgres createdb aicarpools
sudo -u postgres createuser aicarpools_user
```

### 2. Redis缓存
```bash
# Redis已在docker-compose中配置
# 可以在应用中启用缓存功能
```

### 3. CDN配置
- 建议使用CloudFlare或其他CDN服务
- 配置静态资源缓存
- 启用GZIP压缩

## 🔄 备份策略

### 1. 数据库备份
```bash
vim /opt/aicarpools/backup.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/opt/aicarpools/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份SQLite数据库
cp /opt/aicarpools/instance/production.db $BACKUP_DIR/db_backup_$DATE.db

# 备份配置文件
cp /opt/aicarpools/.env.production $BACKUP_DIR/env_backup_$DATE

# 删除7天前的备份
find $BACKUP_DIR -name "*.db" -mtime +7 -delete
find $BACKUP_DIR -name "env_backup_*" -mtime +7 -delete

echo "备份完成: $DATE"
```

```bash
chmod +x /opt/aicarpools/backup.sh
echo "0 3 * * * /opt/aicarpools/backup.sh" | sudo crontab -
```

## 🎉 部署完成

恭喜！您已经成功部署了AI拼车共享平台。现在您可以：

1. 访问 https://aicarpools.com 查看主页
2. 访问 https://aicarpools.com/admin 进入管理后台
3. 开始配置订阅类型和AdsPower账号
4. 邀请用户注册使用

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看日志文件：`docker-compose logs -f`
2. 检查系统资源：`htop`, `df -h`
3. 验证网络连接：`curl`, `ping`
4. 查看GitHub Issues获取社区支持

---

**注意**: 请确保定期更新系统和应用，监控安全漏洞，并保持备份策略的有效性。

## 🔧 高级配置

### 1. 负载均衡配置（多服务器部署）

如果需要部署多台服务器，可以使用以下配置：

```nginx
# 在主负载均衡器上配置
upstream aicarpools_backend {
    server ************:5000 weight=3;
    server ************:5000 weight=2;
    server ************:5000 weight=1;
    keepalive 32;
}

server {
    listen 443 ssl http2;
    server_name aicarpools.com;

    location / {
        proxy_pass http://aicarpools_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 会话保持
        proxy_set_header Connection "";
        proxy_http_version 1.1;
    }
}
```

### 2. 数据库集群配置

#### PostgreSQL主从配置
```bash
# 主数据库配置
sudo -u postgres vim /etc/postgresql/13/main/postgresql.conf
```

```conf
# 主数据库配置
listen_addresses = '*'
wal_level = replica
max_wal_senders = 3
wal_keep_segments = 64
archive_mode = on
archive_command = 'cp %p /var/lib/postgresql/13/main/archive/%f'
```

```bash
# 从数据库配置
sudo -u postgres vim /etc/postgresql/13/main/recovery.conf
```

```conf
# 从数据库配置
standby_mode = 'on'
primary_conninfo = 'host=************ port=5432 user=replicator'
restore_command = 'cp /var/lib/postgresql/13/main/archive/%f %p'
```

### 3. Redis集群配置

```yaml
# docker-compose.redis-cluster.yml
version: '3.8'

services:
  redis-master:
    image: redis:7-alpine
    command: redis-server --appendonly yes --replica-announce-ip ************
    ports:
      - "6379:6379"
    volumes:
      - redis_master_data:/data

  redis-slave1:
    image: redis:7-alpine
    command: redis-server --appendonly yes --slaveof redis-master 6379
    ports:
      - "6380:6379"
    volumes:
      - redis_slave1_data:/data
    depends_on:
      - redis-master

  redis-sentinel:
    image: redis:7-alpine
    command: redis-sentinel /etc/redis/sentinel.conf
    ports:
      - "26379:26379"
    volumes:
      - ./redis-sentinel.conf:/etc/redis/sentinel.conf
    depends_on:
      - redis-master
      - redis-slave1

volumes:
  redis_master_data:
  redis_slave1_data:
```

### 4. 安全加固

#### 防火墙配置
```bash
# 安装ufw
sudo apt install ufw

# 默认策略
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许SSH
sudo ufw allow ssh

# 允许HTTP/HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 允许特定IP访问管理端口
sudo ufw allow from ***********/24 to any port 5000

# 启用防火墙
sudo ufw enable

# 查看状态
sudo ufw status verbose
```

#### Fail2Ban配置
```bash
# 安装fail2ban
sudo apt install fail2ban

# 创建配置文件
sudo vim /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5
backend = systemd

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
```

#### SSL安全配置
```nginx
# 强化SSL配置
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_stapling on;
ssl_stapling_verify on;

# HSTS
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# 其他安全头
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none';" always;
```

## 📊 监控和告警

### 1. Prometheus + Grafana监控

#### Prometheus配置
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'

volumes:
  prometheus_data:
  grafana_data:
```

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'aicarpools-app'
    static_configs:
      - targets: ['app:5000']
    metrics_path: '/metrics'

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
```

### 2. 日志聚合（ELK Stack）

```yaml
# docker-compose.elk.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  logstash:
    image: docker.elastic.co/logstash/logstash:7.15.0
    container_name: logstash
    ports:
      - "5044:5044"
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    container_name: kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch

volumes:
  elasticsearch_data:
```

### 3. 告警配置

#### 邮件告警脚本
```bash
#!/bin/bash
# /opt/aicarpools/alert.sh

ALERT_EMAIL="<EMAIL>"
SERVICE_NAME="AI拼车共享平台"

send_alert() {
    local subject="$1"
    local message="$2"

    echo "$message" | mail -s "$subject" "$ALERT_EMAIL"

    # 也可以发送到企业微信/钉钉
    # curl -X POST "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY" \
    #      -H 'Content-Type: application/json' \
    #      -d "{\"msgtype\": \"text\", \"text\": {\"content\": \"$subject: $message\"}}"
}

# 检查服务状态
check_service_health() {
    if ! curl -f -s http://localhost:5000/health > /dev/null; then
        send_alert "[$SERVICE_NAME] 服务异常" "服务健康检查失败，请立即检查"
        return 1
    fi
    return 0
}

# 检查磁盘空间
check_disk_space() {
    local usage=$(df /opt/aicarpools | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $usage -gt 85 ]; then
        send_alert "[$SERVICE_NAME] 磁盘空间告警" "磁盘使用率已达到 ${usage}%，请及时清理"
    fi
}

# 检查内存使用
check_memory() {
    local mem_usage=$(free | awk 'NR==2{printf "%.2f", $3*100/$2}')
    if (( $(echo "$mem_usage > 90" | bc -l) )); then
        send_alert "[$SERVICE_NAME] 内存使用告警" "内存使用率已达到 ${mem_usage}%"
    fi
}

# 检查数据库连接
check_database() {
    if ! docker-compose exec -T app python -c "
from adspower_manager import create_app
from adspower_manager.models import db
app = create_app()
with app.app_context():
    db.engine.execute('SELECT 1')
print('Database OK')
" > /dev/null 2>&1; then
        send_alert "[$SERVICE_NAME] 数据库连接异常" "无法连接到数据库，请检查数据库服务"
    fi
}

# 执行所有检查
cd /opt/aicarpools
check_service_health
check_disk_space
check_memory
check_database
```

## 🔄 自动化部署

### 1. CI/CD Pipeline (GitHub Actions)

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Run tests
      run: |
        python -m pytest tests/

    - name: Run security scan
      run: |
        pip install bandit
        bandit -r adspower_manager/

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /opt/aicarpools
          git pull origin main
          docker-compose build
          docker-compose up -d
          docker-compose exec -T app flask db upgrade
```

### 2. 自动备份脚本

```bash
#!/bin/bash
# /opt/aicarpools/auto_backup.sh

BACKUP_DIR="/opt/aicarpools/backups"
REMOTE_BACKUP_HOST="backup.aicarpools.com"
REMOTE_BACKUP_USER="backup"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
echo "开始备份数据库..."
docker-compose exec -T app python -c "
import sqlite3
import shutil
shutil.copy('/app/instance/production.db', '/app/backups/db_backup_$DATE.db')
print('数据库备份完成')
"

# 备份配置文件
echo "备份配置文件..."
tar -czf $BACKUP_DIR/config_backup_$DATE.tar.gz \
    .env.production \
    docker-compose.yml \
    nginx.conf

# 备份用户上传文件
echo "备份用户文件..."
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz \
    instance/uploads/ 2>/dev/null || true

# 上传到远程备份服务器
echo "上传到远程备份服务器..."
rsync -avz --delete $BACKUP_DIR/ \
    $REMOTE_BACKUP_USER@$REMOTE_BACKUP_HOST:/backups/aicarpools/

# 清理本地旧备份（保留7天）
echo "清理本地旧备份..."
find $BACKUP_DIR -name "*.db" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "备份完成: $DATE"
```

### 3. 健康检查脚本

```bash
#!/bin/bash
# /opt/aicarpools/health_check.sh

HEALTH_URL="https://aicarpools.com/health"
SLACK_WEBHOOK="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"

check_health() {
    local response=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

    if [ "$response" != "200" ]; then
        # 发送告警
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 AI拼车共享平台健康检查失败！HTTP状态码: $response\"}" \
            $SLACK_WEBHOOK

        # 尝试重启服务
        echo "$(date): 健康检查失败，尝试重启服务" >> /opt/aicarpools/logs/health_check.log
        docker-compose restart app

        # 等待30秒后再次检查
        sleep 30
        local retry_response=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

        if [ "$retry_response" == "200" ]; then
            curl -X POST -H 'Content-type: application/json' \
                --data "{\"text\":\"✅ AI拼车共享平台服务已恢复正常\"}" \
                $SLACK_WEBHOOK
        else
            curl -X POST -H 'Content-type: application/json' \
                --data "{\"text\":\"❌ AI拼车共享平台服务重启后仍然异常，需要人工介入\"}" \
                $SLACK_WEBHOOK
        fi
    fi
}

check_health
```

## 📱 移动端适配

### 1. PWA配置

```json
// static/manifest.json
{
  "name": "AI拼车共享平台",
  "short_name": "AI拼车",
  "description": "AI服务拼车共享平台",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#007bff",
  "icons": [
    {
      "src": "/static/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/static/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

```javascript
// static/js/sw.js - Service Worker
const CACHE_NAME = 'aicarpools-v1';
const urlsToCache = [
  '/',
  '/static/css/bootstrap.min.css',
  '/static/js/bootstrap.bundle.min.js',
  '/static/js/dashboard.js'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        if (response) {
          return response;
        }
        return fetch(event.request);
      }
    )
  );
});
```

### 2. 响应式设计优化

```css
/* static/css/mobile.css */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .card {
    margin-bottom: 1rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 576px) {
  .navbar-brand {
    font-size: 1rem;
  }

  .card-header h5 {
    font-size: 1rem;
  }

  .modal-dialog {
    margin: 0.5rem;
  }
}
```

## 🔐 安全最佳实践

### 1. 应用安全配置

```python
# adspower_manager/security.py
from flask import request, abort
from functools import wraps
import re

def validate_input(field_name, pattern=None, max_length=None):
    """输入验证装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            data = request.get_json() or {}
            value = data.get(field_name)

            if value is None:
                abort(400, f"Missing required field: {field_name}")

            if max_length and len(str(value)) > max_length:
                abort(400, f"Field {field_name} exceeds maximum length")

            if pattern and not re.match(pattern, str(value)):
                abort(400, f"Field {field_name} format invalid")

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def rate_limit(max_requests=100, window=3600):
    """简单的速率限制"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 实现速率限制逻辑
            client_ip = request.environ.get('HTTP_X_REAL_IP', request.remote_addr)
            # 这里可以使用Redis来存储请求计数
            return f(*args, **kwargs)
        return decorated_function
    return decorator
```

### 2. 数据库安全

```python
# 数据库连接安全配置
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_pre_ping': True,
    'pool_recycle': 300,
    'connect_args': {
        'check_same_thread': False,  # SQLite
        'timeout': 20
    }
}

# 敏感数据加密
from cryptography.fernet import Fernet

class DataEncryption:
    def __init__(self, key=None):
        if key is None:
            key = Fernet.generate_key()
        self.cipher_suite = Fernet(key)

    def encrypt(self, data):
        return self.cipher_suite.encrypt(data.encode()).decode()

    def decrypt(self, encrypted_data):
        return self.cipher_suite.decrypt(encrypted_data.encode()).decode()
```

## 🎯 性能优化进阶

### 1. 缓存策略

```python
# adspower_manager/cache.py
from flask_caching import Cache
import redis

cache = Cache()

# Redis缓存配置
CACHE_CONFIG = {
    'CACHE_TYPE': 'redis',
    'CACHE_REDIS_HOST': 'redis',
    'CACHE_REDIS_PORT': 6379,
    'CACHE_REDIS_DB': 0,
    'CACHE_DEFAULT_TIMEOUT': 300
}

def cache_key_prefix():
    """动态缓存键前缀"""
    return f"aicarpools:{request.endpoint}:"

@cache.memoize(timeout=300)
def get_user_subscriptions(user_id):
    """缓存用户订阅信息"""
    # 实现获取用户订阅的逻辑
    pass

@cache.memoize(timeout=600)
def get_subscription_types():
    """缓存订阅类型"""
    # 实现获取订阅类型的逻辑
    pass
```

### 2. 数据库查询优化

```python
# 数据库查询优化示例
from sqlalchemy.orm import joinedload, selectinload

class OptimizedQueries:
    @staticmethod
    def get_user_with_subscriptions(user_id):
        """优化的用户订阅查询"""
        return User.query.options(
            joinedload(User.subscriptions),
            selectinload(User.devices)
        ).filter_by(id=user_id).first()

    @staticmethod
    def get_active_subscriptions():
        """优化的活跃订阅查询"""
        return Subscription.query.options(
            joinedload(Subscription.user),
            joinedload(Subscription.subscription_type)
        ).filter(
            Subscription.end_date > datetime.utcnow()
        ).all()
```

### 3. 静态资源优化

```bash
# 静态资源压缩脚本
#!/bin/bash
# /opt/aicarpools/optimize_assets.sh

echo "优化静态资源..."

# 压缩CSS
for file in static/css/*.css; do
    if [[ ! $file == *.min.css ]]; then
        npx cleancss -o "${file%.css}.min.css" "$file"
    fi
done

# 压缩JavaScript
for file in static/js/*.js; do
    if [[ ! $file == *.min.js ]]; then
        npx terser "$file" -o "${file%.js}.min.js"
    fi
done

# 优化图片
for file in static/images/*.{jpg,jpeg,png}; do
    if [[ -f "$file" ]]; then
        npx imagemin "$file" --out-dir=static/images/optimized/
    fi
done

echo "静态资源优化完成"
```

---

这个完整的部署教程涵盖了从基础部署到高级配置的所有方面。您可以根据实际需求选择相应的配置级别。建议先按照基础部署步骤完成部署，然后根据业务发展需要逐步添加高级功能。
