<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title data-i18n="admin.title">管理后台 - ChatGPTPro俱乐部</title>
  <!-- 引入 Bootstrap 样式表和图标 -->
  <link
    href="https://gcore.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
    rel="stylesheet"
  />
  <link
    rel="stylesheet"
    href="https://gcore.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css"
  />
  <!-- Chart.js 图表库 -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
  <!-- 渐变色生成器 -->
  <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-gradient@0.6.1/dist/chartjs-plugin-gradient.min.js"></script>
    <style>
    /* 全局样式 */
        body {
            background-color: #f5f5f5;
      font-family: "Helvetica Neue", Arial, sans-serif;
      color: #333;
    }

    /* 主要颜色或自定义变量可视需要进行调整 */
    :root {
      --bs-primary: #0d6efd;
      --bs-success: #198754;
      --bs-danger: #dc3545;
      --bs-warning: #ffc107;
      --bs-info: #0dcaf0;
      --bs-secondary: #6c757d;
    }

    /* 顶部导航栏 */
    .navbar {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .navbar-brand {
      font-size: 1.25rem;
      font-weight: bold;
    }

    /* 左侧侧边栏 */
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
      padding-top: 4rem; /* 留出与顶部导航的间距（因为顶部导航是 fixed 的） */
            overflow-x: hidden;
            overflow-y: auto;
      box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
    }

    /* 左侧导航的链接状态 */
    .nav-link.active {
      background-color: rgba(0, 0, 0, 0.1);
      font-weight: 500;
    }

    /* 主体内容区域 */
        .main-content {
      /* 因为顶部导航 fixed，所以这里预留一些顶部空间 */
      margin-top: 56px;
        }

    /* 卡片统一样式 */
        .card {
            margin-bottom: 20px;
      border-radius: 0.5rem;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    }
    .card .card-header {
      background-color: #fff;
      font-weight: 500;
      border-bottom: 1px solid #eee;
    }

    /* 段落、表格等细节 */
    .table-striped tbody tr:nth-of-type(2n+1) {
      background-color: #fafafa;
    }
    .table th,
    .table td {
      vertical-align: middle;
    }

    /* 设备或配置项列表样式 */
        .device-item {
      border-left: 4px solid var(--bs-primary);
            margin-bottom: 10px;
      padding-left: 8px;
        }
        .profile-item {
      border-left: 4px solid var(--bs-success);
      padding-left: 8px;
        }

    /* 倒计时样式 */
        .date-countdown {
            font-weight: bold;
      color: var(--bs-primary);
        }

    /* 状态文本色 */
        .status-active {
      color: white;
      background-color: #198754;
        }
        .status-inactive {
      color: var(--bs-danger);
    }

    /* 批量操作容器 */
    .batch-container {
      margin-bottom: 1rem;
      padding: 1rem;
      border: 1px solid #dee2e6;
      border-radius: 0.25rem;
      background-color: #f8f9fa;
    }

    /* Cookies显示区 */
    .cookie-display {
      max-height: 400px;
      overflow-y: auto;
    }

    /* 隐藏元素 */
    .hidden {
      display: none;
    }

    /* 编辑用户模态框样式 */
    #editUserModal .modal-dialog {
      max-height: 90vh;
    }
    #editUserModal .modal-body {
      max-height: calc(90vh - 120px);
      overflow-y: auto;
    }
    @media (min-width: 992px) {
      #editUserModal .modal-dialog {
        max-width: 800px;
      }
    }

    /* 新的仪表板样式 */
    .dashboard-metric-card {
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      border: none;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }
    .dashboard-metric-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }
    .metric-icon {
      position: absolute;
      right: 20px;
      top: 20px;
      opacity: 0.2;
      font-size: 3rem;
    }
    .metric-trend {
      font-size: 0.875rem;
      display: inline-flex;
      align-items: center;
      gap: 4px;
      margin-top: 8px;
    }
    .trend-up {
      color: #10b981;
    }
    .trend-down {
      color: #ef4444;
    }
    .chart-container {
      position: relative;
      height: 300px;
      width: 100%;
    }
    .activity-timeline {
      max-height: 400px;
      overflow-y: auto;
    }
    .timeline-item {
      padding: 12px 0;
      border-left: 2px solid #e5e7eb;
      margin-left: 20px;
      padding-left: 20px;
      position: relative;
    }
    .timeline-item::before {
      content: '';
      position: absolute;
      left: -7px;
      top: 16px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #6366f1;
      border: 2px solid #fff;
    }
    .timeline-item.warning::before {
      background-color: #f59e0b;
    }
    .timeline-item.danger::before {
      background-color: #ef4444;
    }
    .insight-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 20px;
    }
    .insight-card h6 {
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 12px;
    }
    .insight-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    .insight-item:last-child {
      border-bottom: none;
    }
    
    /* 渐变背景样式 */
    .bg-gradient-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }
    .bg-gradient-success {
      background: linear-gradient(135deg, #0fd850 0%, #00a651 100%) !important;
    }
    .bg-gradient-warning {
      background: linear-gradient(135deg, #f9c74f 0%, #f8961e 100%) !important;
    }
    .bg-gradient-info {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    }

    /* 搜索框宽度 */
    #search-input,
    #device-search,
    #user-search {
      width: 300px;
    }

    .device-id {
      font-family: monospace;
      font-size: 0.9rem;
      background-color: #f8f9fa;
      padding: 2px 5px;
      border-radius: 3px;
      color: #495057;
      border: 1px solid #dee2e6;
        }

    /* Table Sorting */
    .table th[data-sortable] {
      cursor: pointer;
      position: relative;
    }
    .table th[data-sortable]:hover {
      background-color: #f5f5f5;
    }
    .table th[data-sortable]:after {
      content: "\25B4";
      position: absolute;
      right: 0.5rem;
      top: 50%;
      transform: translateY(-50%);
      opacity: 0.5;
    }
    .table th[data-sortable][data-sort-direction="ascending"]:after {
      content: "\25B2";
      opacity: 1;
    }
    .table th[data-sortable][data-sort-direction="descending"]:after {
      content: "\25BC";
      opacity: 1;
    }

    /* Table Row Hover */
    .table tbody tr:hover {
      background-color: #f5f5f5;
    }

    /* 移动端响应式优化 */
    @media (max-width: 768px) {
      /* 顶部导航栏优化 - 保持单行 */
      .navbar {
        flex-wrap: nowrap !important;
        height: 56px;
        padding: 0 !important;
        align-items: center;
      }
      
      .navbar-brand {
        font-size: 1rem;
        padding: 0.5rem 0.75rem;
        flex: 1 1 auto;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .navbar-toggler {
        padding: 0.25rem 0.5rem;
        font-size: 1rem;
        margin-left: 0.5rem;
        margin-right: 0.5rem;
        border: none;
        flex-shrink: 0;
      }
      
      .navbar-nav {
        flex-direction: row;
        align-items: center;
        margin-left: 0;
        width: auto;
        flex-shrink: 0;
      }
      
      .navbar-nav .nav-item {
        display: flex;
        align-items: center;
        margin-right: 0.5rem;
      }
      
      .navbar-text {
        font-size: 0.875rem;
        white-space: nowrap;
        margin-right: 0.5rem !important;
        color: rgba(255,255,255,0.8);
      }
      
      #logout-btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        white-space: nowrap;
      }
      /* 侧边栏优化 */
      .sidebar {
        position: fixed;
        z-index: 1000;
        height: 100%;
        width: 250px;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
        background-color: #f8f9fa;
      }
      
      .sidebar.show {
        transform: translateX(0);
      }
      
      /* 主内容区域调整 */
      .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        padding: 15px;
      }
      
      /* 卡片布局优化 */
      .card {
        margin-bottom: 15px;
      }
      
      .card-body {
        padding: 15px;
      }
      
      /* 统计卡片优化 */
      .dashboard-metric-card {
        min-height: 120px;
      }
      
      .dashboard-metric-card h3 {
        font-size: 1.75rem;
      }
      
      /* 表格优化 - 水平滚动 */
      .table-responsive {
        -webkit-overflow-scrolling: touch;
        margin-bottom: 15px;
      }
      
      /* 表格字体大小优化 */
      .table {
        font-size: 0.875rem;
      }
      
      .table th,
      .table td {
        padding: 0.5rem;
        white-space: nowrap;
      }
      
      /* 按钮组优化 - 仅对表格内的操作按钮应用竖向布局 */
      .table .btn-group {
        display: inline-flex;
        flex-direction: row;
        gap: 2px;
      }
      
      .table .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
      }
      
      /* 工具栏布局优化 */
      .toolbar-buttons {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 5px;
        justify-content: flex-end;
        align-items: center;
        width: 100%;
      }
      
      .toolbar-buttons .btn-group {
        display: inline-flex;
        flex-direction: row;
        gap: 3px;
        margin: 0;
      }
      
      .toolbar-buttons .btn {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      /* 移动端优化按钮显示 */
      @media (max-width: 576px) {
        /* 小屏幕隐藏按钮文字，只显示图标 */
        .toolbar-buttons .btn i {
          margin-right: 0;
        }
        
        .toolbar-buttons .btn span {
          display: none;
        }
        
        /* 按钮使用固定宽度，避免大小不一 */
        .toolbar-buttons .btn {
          min-width: 44px;
          padding: 0.375rem 0.75rem;
        }
      }
      
      @media (min-width: 577px) and (max-width: 768px) {
        /* 中等屏幕显示简短文字 */
        .toolbar-buttons .btn {
          font-size: 0.875rem;
          padding: 0.375rem 0.5rem;
        }
      }
      
      /* 操作按钮优化 */
      .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
      }
      
      /* 搜索框和筛选器优化 */
      .input-group {
        margin-bottom: 10px;
      }
      
      .form-select,
      .form-control {
        font-size: 16px; /* 防止iOS缩放 */
      }
      
      /* 模态框优化 */
      .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
      }
      
      .modal-body {
        max-height: calc(100vh - 200px);
        overflow-y: auto;
      }
      
      /* 分页优化 */
      .pagination {
        flex-wrap: wrap;
        justify-content: center;
      }
      
      .pagination .page-link {
        padding: 0.5rem 0.75rem;
      }
      
      /* Tab标签优化 */
      .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        white-space: nowrap;
        margin-bottom: 15px;
      }
      
      .nav-tabs .nav-link {
        flex-shrink: 0;
      }
      
      /* 图表容器优化 */
      .chart-container {
        position: relative;
        height: 250px !important;
      }
      
      /* 状态标签优化 */
      .badge {
        font-size: 0.75rem;
        padding: 0.35em 0.65em;
      }
      
    }
    
    /* 小屏幕设备（手机竖屏） */
    @media (max-width: 576px) {
      /* 顶部导航进一步优化 */
      .navbar-brand {
        font-size: 0.875rem;
        padding: 0.375rem 0.5rem;
      }
      
      .navbar-text {
        font-size: 0.75rem;
      }
      
      #logout-btn {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
      }
      
      .navbar-toggler {
        padding: 0.2rem 0.4rem;
        font-size: 0.875rem;
      }
      
      /* 统计卡片进一步优化 */
      .col-xl-3.col-md-6 {
        padding: 0 5px;
      }
      
      /* 表格操作按钮在极小屏幕上的优化 */
      @media (max-width: 360px) {
        .table .btn-group {
          flex-direction: column;
          gap: 2px;
        }
        
        .table .btn-group .btn {
          width: 100%;
          text-align: center;
        }
      }
      
      /* 表单输入框优化 */
      .form-group {
        margin-bottom: 1rem;
      }
      
      .form-label {
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
      }
      
      /* 下拉菜单优化 */
      .dropdown-menu {
        position: static !important;
        width: 100%;
        margin-top: 0.25rem;
      }
      
      /* 工具栏按钮优化 */
      .toolbar-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        align-items: center;
      }
      
      .toolbar-buttons .btn {
        flex: 0 0 auto;
        white-space: nowrap;
        min-width: auto;
      }
      
      /* 下拉按钮组保持横向 */
      .toolbar-buttons .dropdown {
        display: inline-flex;
      }
    }
    
    /* 触摸设备优化 */
    @media (hover: none) {
      /* 增大可点击区域 */
      .btn {
        min-height: 44px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
      
      .form-control,
      .form-select {
        min-height: 44px;
      }
      
      /* 复选框和单选框优化 */
      .form-check-input {
        width: 20px;
        height: 20px;
        margin-top: 0.25rem;
      }
      
      /* 链接点击区域优化 */
      .nav-link {
        padding: 0.75rem 1rem;
      }
    }
    
    /* 极小屏幕优化（iPhone SE等） */
    @media (max-width: 375px) {
      .navbar-brand {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
      }
      
      .navbar-text {
        font-size: 0.7rem; /* 在极小屏幕使用更小的字体 */
      }
      
      #logout-btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.5rem;
      }
    }
    
    
    /* 遮罩层 */
    .sidebar-backdrop {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 999;
    }
    
    .sidebar-backdrop.show {
      display: block;
    }
    </style>
</head>
<body>
  <!-- 顶部导航栏（固定） -->
  <header
    class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow"
  >
    <!-- 移动端切换侧边栏的按钮 -->
    <button
      class="navbar-toggler d-md-none collapsed"
      type="button"
      data-bs-toggle="collapse"
      data-bs-target="#sidebarMenu"
      aria-controls="sidebarMenu"
      aria-expanded="false"
      aria-label="Toggle navigation"
    >
      <span class="navbar-toggler-icon"></span>
    </button>
    <!-- 品牌/标题 -->
    <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#" title="ChatGPTPro俱乐部管理后台">
      <span class="d-none d-sm-inline">ChatGPTPro俱乐部管理后台</span>
      <span class="d-inline d-sm-none">管理后台</span>
    </a>
    <!-- 右侧用户信息与退出按钮 -->
    <div class="navbar-nav">
      <div class="nav-item text-nowrap d-flex align-items-center">
        <span class="navbar-text me-3" id="user-display"></span>
        <button class="btn btn-outline-light me-2" id="logout-btn">
          <i class="bi bi-box-arrow-right d-inline d-sm-none"></i>
          <span class="d-none d-sm-inline">退出</span>
        </button>
      </div>
    </div>
  </header>

  <!-- 主体布局：左侧侧边栏 + 右侧内容区 -->
  <div class="container-fluid main-content">
    <div class="row">
      <!-- 左侧侧边栏 -->
      <nav
        id="sidebarMenu"
        class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse"
      >
        <div class="position-sticky pt-3">
          <ul class="nav flex-column">
                    <li class="nav-item">
              <a
                class="nav-link active"
                href="#dashboard"
              >
                <i class="bi bi-speedometer2"></i> <span data-i18n="admin.dashboard">系统总览</span>
              </a>
                    </li>
                    <li class="nav-item">
              <a
                class="nav-link"
                href="#devices"
              >
                <i class="bi bi-laptop"></i> <span data-i18n="admin.device_management">设备管理</span>
              </a>
                    </li>
                    <li class="nav-item">
              <a
                class="nav-link"
                href="#device-audits"
              >
                <i class="bi bi-shield-check"></i> <span data-i18n="admin.device_audits">设备审计</span>
              </a>
                    </li>
                    <li class="nav-item">
              <a
                class="nav-link"
                href="#users"
              >
                <i class="bi bi-people"></i> <span data-i18n="admin.user_management">用户管理</span>
              </a>
                    </li>
                    <li class="nav-item">
              <a
                class="nav-link"
                href="#adspower"
              >
                <i class="bi bi-gear"></i> <span data-i18n="admin.adspower_management">AdsPower账号管理</span>
              </a>
                    </li>
                    <li class="nav-item">
              <a
                class="nav-link"
                href="#subscription-types"
                id="subscription-types-nav"
              >
                <i class="bi bi-card-list"></i> <span data-i18n="admin.subscription_types">订阅类型管理</span>
              </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#subscriptions" id="subscriptions-nav">
                            <i class="bi bi-journal-check"></i> <span data-i18n="admin.subscription_management">订阅管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#subscription-instances" id="subscription-instances-nav">
                            <i class="bi bi-diagram-3"></i> <span data-i18n="admin.subscription_instances">订阅实例管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#orders" id="orders-nav">
                            <i class="bi bi-receipt"></i> <span data-i18n="admin.order_management">订单管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#redemption-codes" id="redemption-codes-nav">
                            <i class="bi bi-gift"></i> <span data-i18n="admin.redemption_management">兑换码管理</span>
                        </a>
                    </li>
                </ul>
        </div>
    </nav>

      <!-- 右侧主要内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 系统总览 -->
                <div id="section-dashboard" class="section-content">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2"><i class="bi bi-speedometer2 me-2"></i>系统总览</h1>
                        <div class="btn-toolbar toolbar-buttons mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-dashboard-btn">
                                    <i class="bi bi-arrow-clockwise"></i><span> 刷新数据</span>
                                </button>
                            </div>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="bi bi-calendar3"></i><span> 时间范围</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" data-range="today">今天</a></li>
                                    <li><a class="dropdown-item" href="#" data-range="week">本周</a></li>
                                    <li><a class="dropdown-item" href="#" data-range="month">本月</a></li>
                                    <li><a class="dropdown-item" href="#" data-range="year">本年</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 核心业务指标 -->
                    <h6 class="text-muted mb-3">核心业务指标</h6>
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card dashboard-metric-card bg-gradient-primary text-white">
                                <div class="card-body">
                                    <i class="bi bi-people-fill metric-icon"></i>
                                    <h6 class="card-subtitle mb-2">活跃用户</h6>
                                    <h3 class="card-title mb-0" id="metric-active-users">
                                        <span class="spinner-border spinner-border-sm" role="status"></span>
                                    </h3>
                                    <div class="metric-trend trend-up">
                                        <i class="bi bi-arrow-up-short"></i>
                                        <span id="active-users-trend">+0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card dashboard-metric-card bg-gradient-success text-white">
                                <div class="card-body">
                                    <i class="bi bi-currency-dollar metric-icon"></i>
                                    <h6 class="card-subtitle mb-2">本月收入</h6>
                                    <h3 class="card-title mb-0" id="metric-monthly-revenue">
                                        <span class="spinner-border spinner-border-sm" role="status"></span>
                                    </h3>
                                    <div class="metric-trend trend-up">
                                        <i class="bi bi-arrow-up-short"></i>
                                        <span id="revenue-trend">+0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card dashboard-metric-card bg-gradient-warning text-white">
                                <div class="card-body">
                                    <i class="bi bi-graph-up-arrow metric-icon"></i>
                                    <h6 class="card-subtitle mb-2">转化率</h6>
                                    <h3 class="card-title mb-0" id="metric-conversion-rate">
                                        <span class="spinner-border spinner-border-sm" role="status"></span>
                                    </h3>
                                    <div class="metric-trend trend-up">
                                        <i class="bi bi-arrow-up-short"></i>
                                        <span id="conversion-trend">+0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card dashboard-metric-card bg-gradient-info text-white">
                                <div class="card-body">
                                    <i class="bi bi-arrow-repeat metric-icon"></i>
                                    <h6 class="card-subtitle mb-2">留存率</h6>
                                    <h3 class="card-title mb-0" id="metric-retention-rate">
                                        <span class="spinner-border spinner-border-sm" role="status"></span>
                                    </h3>
                                    <div class="metric-trend trend-up">
                                        <i class="bi bi-arrow-up-short"></i>
                                        <span id="retention-trend">+0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表和分析区域 -->
                    <div class="row mb-4">
                        <!-- 用户增长趋势图 -->
                        <div class="col-xl-8 mb-4">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">用户增长趋势</h6>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-secondary active" data-chart="users">用户</button>
                                        <button type="button" class="btn btn-outline-secondary" data-chart="revenue">收入</button>
                                        <button type="button" class="btn btn-outline-secondary" data-chart="devices">设备</button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="growthChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 实时活动时间线 -->
                        <div class="col-xl-4 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">实时活动</h6>
                                </div>
                                <div class="card-body">
                                    <div class="activity-timeline" id="activity-timeline">
                                        <div class="text-center text-muted py-5">
                                            <i class="bi bi-clock-history fs-1"></i>
                                            <p class="mt-2">加载活动数据中...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 运营指标 -->
                    <h6 class="text-muted mb-3">运营指标</h6>
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-muted mb-2">总用户数</h6>
                                            <h4 class="mb-0" id="total-users">-</h4>
                                        </div>
                                        <div class="text-primary">
                                            <i class="bi bi-people fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-muted mb-2">活跃订阅</h6>
                                            <h4 class="mb-0" id="active-subscriptions">-</h4>
                                        </div>
                                        <div class="text-success">
                                            <i class="bi bi-journal-check fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-muted mb-2">设备使用率</h6>
                                            <h4 class="mb-0" id="device-usage-rate">-</h4>
                                        </div>
                                        <div class="text-info">
                                            <i class="bi bi-laptop fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-muted mb-2">实例容量</h6>
                                            <h4 class="mb-0" id="instance-capacity-rate">-</h4>
                                        </div>
                                        <div class="text-warning">
                                            <i class="bi bi-diagram-3 fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 订阅分布和智能洞察 -->
                    <div class="row mb-4">
                        <!-- 订阅类型分布 -->
                        <div class="col-xl-4 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">订阅类型分布</h6>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container" style="height: 250px;">
                                        <canvas id="subscriptionPieChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 智能洞察 -->
                        <div class="col-xl-4 mb-4">
                            <div class="insight-card">
                                <h6><i class="bi bi-lightbulb me-2"></i>智能洞察</h6>
                                <div id="insights-container">
                                    <div class="insight-item">
                                        <i class="bi bi-info-circle"></i>
                                        <span>加载洞察数据中...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 快捷操作 -->
                        <div class="col-xl-4 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">快捷操作</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary" onclick="showSection('subscription-instances')">
                                            <i class="bi bi-plus-circle me-2"></i>创建新订阅实例
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="showSection('users')">
                                            <i class="bi bi-people me-2"></i>管理用户
                                        </button>
                                        <button class="btn btn-outline-success" onclick="showSection('devices')">
                                            <i class="bi bi-laptop me-2"></i>管理设备
                                        </button>
                                        <button class="btn btn-outline-info" onclick="showSection('adspower')">
                                            <i class="bi bi-gear me-2"></i>AdsPower账号
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统健康状态 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">系统健康状态</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 text-center">
                                            <h6 class="text-muted">服务状态</h6>
                                            <div class="text-success fs-1">
                                                <i class="bi bi-check-circle-fill"></i>
                                            </div>
                                            <p class="mb-0">运行正常</p>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <h6 class="text-muted">响应时间</h6>
                                            <h4 class="mb-0" id="response-time">-ms</h4>
                                            <p class="mb-0 text-muted">平均响应</p>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <h6 class="text-muted">错误率</h6>
                                            <h4 class="mb-0" id="error-rate">0%</h4>
                                            <p class="mb-0 text-muted">最近24小时</p>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <h6 class="text-muted">最后更新</h6>
                                            <h4 class="mb-0" id="last-update">-</h4>
                                            <p class="mb-0 text-muted">数据刷新时间</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

        <!-- 设备管理 -->
        <div id="section-devices" class="section-content" style="display: none;">
          <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
          >
                        <h1 class="h2">设备管理</h1>
            <div class="btn-toolbar toolbar-buttons mb-2 mb-md-0">
              <button
                class="btn btn-sm btn-primary me-2"
                id="refresh-devices-btn"
              >
                <i class="bi bi-arrow-clockwise"></i><span> 刷新设备列表</span>
              </button>
              <!-- Removed "从AdsPower同步设备" button -->
            </div>
                    </div>
                    <div class="card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
                            <h5>所有设备</h5>
              <div class="input-group w-50">
                <input
                  type="text"
                  class="form-control"
                  id="device-search"
                  placeholder="搜索设备(名称/ID/IP/用户)"
                  aria-label="搜索设备"
                  aria-describedby="device-search-btn"
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  id="device-search-btn"
                  title="搜索"
                >
                  <i class="bi bi-search"></i>
                </button>
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  id="device-clear-btn"
                  title="清除"
                  style="display: none;"
                >
                  <i class="bi bi-x-lg"></i>
                </button>
              </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped table-sm" id="devices-table" aria-live="polite" aria-label="设备列表">
                                    <thead>
                                        <tr>
                                            <!-- Removed Checkbox Cell -->
                                            <!-- <th><input type="checkbox" id="select-all-devices"></th> -->
                                            <th scope="col" data-sortable data-sort-type="string" data-key="id">ID</th>
                                            <th scope="col" data-sortable data-sort-type="string" data-key="user_email">用户邮箱</th>
                                            <th scope="col" data-sortable data-sort-type="string" data-key="device_name">设备名称</th>
                                            <th scope="col" data-sortable data-sort-type="string" data-key="device_type">设备类型</th>
                                            <th scope="col" data-sortable data-sort-type="string" data-key="device_ip">IP 地址</th>
                                            <!-- Removed Status Header -->
                                            <th scope="col">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="devices-table-body">
                    <!-- 设备列表通过JS填充 -->
                                    </tbody>
                                </table>
                            </div>
                            <!-- 分页导航 -->
                            <nav aria-label="设备列表分页">
                                <ul class="pagination justify-content-center" id="devices-pagination">
                                    <!-- 分页按钮将通过JS动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>

        <!-- 设备审计 -->
        <div id="section-device-audits" class="section-content" style="display: none;">
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-shield-check me-2"></i>设备审计</h1>
            <div class="btn-toolbar toolbar-buttons mb-2 mb-md-0">
              <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-audits-btn">
                <i class="bi bi-arrow-clockwise"></i><span> 刷新</span>
              </button>
            </div>
          </div>

          <!-- 统计概览 -->
          <div class="row mb-4">
            <div class="col-md-8">
              <div class="card">
                <div class="card-body">
                  <h5 class="card-title"><i class="bi bi-activity me-2"></i>最近7天设备操作概览</h5>
                  <div class="row text-center">
                    <div class="col">
                      <h3 class="text-primary" id="total-device-changes">-</h3>
                      <small class="text-muted">总操作次数</small>
                    </div>
                    <div class="col">
                      <h3 class="text-success" id="total-device-adds">-</h3>
                      <small class="text-muted">添加设备</small>
                    </div>
                    <div class="col">
                      <h3 class="text-danger" id="total-device-removes">-</h3>
                      <small class="text-muted">移除设备</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card bg-primary text-white">
                <div class="card-body text-center">
                  <h5 class="card-title">设备更换排行榜</h5>
                  <p class="card-text">查看设备更换次数最多的用户</p>
                  <button type="button" class="btn btn-light btn-lg" id="device-changes-btn">
                    <i class="bi bi-bar-chart-line me-2"></i>查看完整报告
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 筛选器 -->
          <div class="card mb-3">
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-3">
                  <label class="form-label">用户</label>
                  <input type="text" class="form-control" id="audit-user-filter" placeholder="用户ID或邮箱">
                </div>
                <div class="col-md-3">
                  <label class="form-label">操作类型</label>
                  <select class="form-select" id="audit-action-filter">
                    <option value="">全部</option>
                    <option value="register">添加设备</option>
                    <option value="delete">移除设备</option>
                  </select>
                </div>
                <div class="col-md-2">
                  <label class="form-label">开始日期</label>
                  <input type="date" class="form-control" id="audit-start-date">
                </div>
                <div class="col-md-2">
                  <label class="form-label">结束日期</label>
                  <input type="date" class="form-control" id="audit-end-date">
                </div>
              </div>
              <div class="row mt-3">
                <div class="col-12">
                  <button class="btn btn-primary" id="apply-audit-filter">
                    <i class="bi bi-search"></i> 搜索
                  </button>
                  <button class="btn btn-secondary" id="reset-audit-filter">
                    <i class="bi bi-arrow-counterclockwise"></i> 重置
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 审计日志表格 -->
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">审计日志</h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-hover" id="audits-table">
                  <thead>
                    <tr>
                      <th>时间</th>
                      <th>用户</th>
                      <th>操作</th>
                      <th>来源</th>
                      <th>描述</th>
                      <th>IP地址</th>
                      <th>浏览器</th>
                      <th>详情</th>
                    </tr>
                  </thead>
                  <tbody id="audits-tbody">
                    <!-- 数据将通过JS动态加载 -->
                  </tbody>
                </table>
              </div>
              <nav aria-label="审计日志分页">
                <ul class="pagination justify-content-center" id="audits-pagination">
                  <!-- 分页按钮将通过JS动态生成 -->
                </ul>
              </nav>
            </div>
          </div>
        </div>

        <!-- 用户管理 -->
        <div id="section-users" class="section-content" style="display: none;">
          <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
          >
                        <h1 class="h2">用户管理</h1>
            <div class="btn-toolbar toolbar-buttons mb-2 mb-md-0">
              <button class="btn btn-primary" id="refresh-users-btn">
                <i class="bi bi-arrow-clockwise"></i><span> 刷新用户列表</span>
              </button>
            </div>
                    </div>
                    <div class="card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
                            <h5>所有用户</h5>
              <div class="input-group w-50">
                <input
                  type="text"
                  class="form-control"
                  id="user-search"
                  placeholder="搜索用户(ID/用户名/邮箱)"
                  aria-label="搜索用户"
                  aria-describedby="user-search-btn"
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  id="user-search-btn"
                  title="搜索"
                >
                  <i class="bi bi-search"></i>
                </button>
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  id="user-clear-btn"
                  title="清除"
                  style="display: none;"
                >
                  <i class="bi bi-x-lg"></i>
                </button>
              </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="users-table" aria-live="polite" aria-label="用户列表">
                                    <thead>
                                        <tr>
                                            <th scope="col" data-sortable data-sort-type="string" data-key="id">ID</th>
                                            <th scope="col" data-sortable data-sort-type="string" data-key="email">邮箱</th>
                                            <th scope="col" data-sortable data-sort-type="string" data-key="is_active">激活状态</th>
                                            <th scope="col" data-sortable data-sort-type="string" data-key="is_admin">管理员</th>
                                            <th scope="col" data-sortable data-sort-type="string" data-key="subscription_plan">订阅计划</th>
                                            <th scope="col" data-sortable data-sort-type="string" data-key="subscription_status">订阅状态</th>
                                            <th scope="col" data-sortable data-sort-type="date" data-key="subscription_end_date">订阅到期</th>
                                            <th scope="col" data-sortable data-sort-type="date" data-key="last_login">最后登录</th>
                                            <th scope="col" data-sortable data-sort-type="date" data-key="created_at">注册时间</th>
                                            <th scope="col" data-sortable data-sort-type="string" data-key="remark">备注</th>
                                            <th scope="col">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="users-table-body">
                    <!-- 用户列表通过JS填充 -->
                                    </tbody>
                                </table>
                            </div>
                            <!-- 分页导航 -->
                            <nav aria-label="用户列表分页">
                                <ul class="pagination justify-content-center" id="users-pagination">
                                    <!-- 分页按钮将通过JS动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>

        <!-- 订阅类型管理 -->
        <div
          id="section-subscription-types"
          class="section-content"
          style="display: none;"
        >
          <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
          >
            <h1 class="h2">订阅类型管理</h1>
            <div class="btn-toolbar toolbar-buttons mb-2 mb-md-0">
              <button
                class="btn btn-primary"
                data-bs-toggle="modal"
                data-bs-target="#addSubscriptionTypeModal"
              >
                <i class="bi bi-plus-circle"></i> 添加订阅类型
              </button>
            </div>
                    </div>
                    <div class="card">
            <div class="card-header">
              <h5>订阅类型列表</h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-striped table-hover">
                  <thead>
                    <tr>
                      <th scope="col" data-sortable data-sort-type="string" data-key="name">显示名称</th>
                      <th scope="col" data-sortable data-sort-type="number" data-key="max_devices">最大设备数</th>
                      <th scope="col" data-sortable data-sort-type="number" data-key="price">价格</th>
                      <th scope="col" data-sortable data-sort-type="number" data-key="days">有效期(天)</th>
                      <th scope="col" class="text-center">公开</th>
                      <th scope="col" class="text-center">同步</th>
                      <th scope="col">操作</th>
                    </tr>
                  </thead>
                  <tbody id="subscription-types-table-body">
                    <!-- 订阅类型列表通过JS填充 -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- 订阅管理 -->
        <div id="section-subscriptions" class="section-content" style="display: none;">
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-journal-check me-2"></i>订阅管理</h1>
            <div class="btn-toolbar toolbar-buttons mb-2 mb-md-0">
              <button class="btn btn-primary me-2" id="refresh-subscriptions-btn">
                <i class="bi bi-arrow-clockwise"></i><span> 刷新订阅列表</span>
              </button>
              <!-- 可选：添加 "创建订阅" 按钮 -->
            </div>
          </div>
          <div class="card shadow-sm mb-4"> <div class="card-header bg-light d-flex flex-wrap justify-content-between align-items-center py-3">
              <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>所有订阅</h5>
              <div class="input-group w-auto" style="max-width: 400px;">
                <input type="text" class="form-control" id="subscription-search" placeholder="搜索用户邮箱/计划/状态" aria-label="搜索订阅">
                <button class="btn btn-outline-primary" type="button" id="subscription-search-btn" title="搜索">
                  <i class="bi bi-search"></i>
                </button>
                <button class="btn btn-outline-secondary" type="button" id="subscription-clear-btn" title="清除" style="display: none;">
                  <i class="bi bi-x-lg"></i>
                </button>
              </div>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-striped table-hover table-sm" id="subscriptions-table" aria-live="polite" aria-label="订阅列表">
                  <thead>
                    <tr>
                      <th scope="col" data-sortable data-sort-type="string" data-key="user_email">用户邮箱</th>
                      <th scope="col" data-sortable data-sort-type="string" data-key="subscription_instance_name">订阅实例</th>
                      <th scope="col" data-sortable data-sort-type="string" data-key="status">状态</th>
                      <th scope="col" data-sortable data-sort-type="date" data-key="start_date">开始日期</th>
                      <th scope="col" data-sortable data-sort-type="date" data-key="end_date">结束日期</th>
                      <th scope="col" data-sortable data-sort-type="number" data-key="max_devices">最大设备数</th>
                      <th scope="col" data-sortable data-sort-type="string" data-key="remark">备注</th>
                      <th scope="col">操作</th>
                    </tr>
                  </thead>
                  <tbody id="subscriptions-table-body">
                    <!-- 订阅列表通过JS填充 -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- 订阅实例管理 -->
        <div id="section-subscription-instances" class="section-content" style="display: none;">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="bi bi-diagram-3-fill me-2"></i>订阅实例管理</h1>
                <div class="btn-toolbar toolbar-buttons mb-2 mb-md-0">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addInstanceModal">
                        <i class="bi bi-plus-circle"></i> 添加订阅实例
                    </button>
                </div>
            </div>
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex flex-wrap justify-content-between align-items-center py-3">
                    <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>所有订阅实例</h5>
                    <div class="input-group w-auto" style="max-width: 400px;">
                        <input type="text" class="form-control" id="instance-search" placeholder="搜索实例名称/类型/状态" aria-label="搜索订阅实例">
                        <button class="btn btn-outline-primary" type="button" id="instance-search-btn" title="搜索">
                            <i class="bi bi-search"></i>
                        </button>
                        <button class="btn btn-outline-secondary" type="button" id="instance-clear-btn" title="清除" style="display: none;">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover table-sm" id="subscription-instances-table" aria-live="polite" aria-label="订阅实例列表">
                            <thead>
                                <tr>
                                    <th scope="col" data-sortable data-sort-type="number" data-key="id">ID</th>
                                    <th scope="col" data-sortable data-sort-type="string" data-key="name">名称</th>
                                    <th scope="col" data-sortable data-sort-type="string" data-key="subscription_type_name">订阅类型</th>
                                    <th scope="col" data-sortable data-sort-type="number" data-key="capacity">总容量</th>
                                    <th scope="col" data-sortable data-sort-type="number" data-key="active_users_count">当前用户数</th>
                                    <th scope="col" data-sortable data-sort-type="number" data-key="adspower_accounts_count">AdsPower账号数</th>
                                    <th scope="col" data-sortable data-sort-type="boolean" data-key="is_active">状态</th>
                                    <th scope="col" data-sortable data-sort-type="string" data-key="description">描述</th>
                                    <th scope="col" data-sortable data-sort-type="date" data-key="created_at">创建时间</th>
                                    <th scope="col">操作</th>
                                </tr>
                            </thead>
                            <tbody id="subscription-instances-table-body">
                                <!-- 订阅实例列表通过JS填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- /#section-subscription-instances -->

        <!-- AdsPower账号管理 -->
        <div
          id="section-adspower"
          class="section-content"
          style="display: none;"
        >
          <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
          >
            <h1 class="h2">ADSpower账号管理</h1>
            <div class="btn-toolbar mb-2 mb-md-0" id="adspower-accounts-toolbar">
              <button class="btn btn-primary me-2" id="add-adspower-account-btn">
                <i class="bi bi-plus-circle"></i> 添加新账号
              </button>
              <!-- Removed "刷新所有账号设备数" button -->
              <!-- Removed "批量设置订阅类型" button -->
            </div>
          </div>
          <div class="card">
            <div class="card-header">
              <h5>所有ADSpower账号</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                      <th>用户名</th>
                                            <th>状态</th>
                      <th>健康状态</th>
                      <th>当前设备数</th>
                      <th>最大设备数</th>
                      <th>所属订阅实例</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                  <tbody id="accounts-table-body">
                    <!-- 账号列表将通过JavaScript填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

        <!-- 订单管理 -->
        <div id="section-orders" class="section-content" style="display: none;">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="bi bi-receipt-cutoff me-2"></i>订单管理</h1>
                <div class="btn-toolbar toolbar-buttons mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportOrders()">
                            <i class="bi bi-download"></i> 导出订单
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-muted">总订单数</h5>
                            <h3 class="mb-0" id="total-orders">0</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-muted">已支付订单</h5>
                            <h3 class="mb-0 text-success" id="paid-orders">0</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-muted">待支付订单</h5>
                            <h3 class="mb-0 text-warning" id="pending-orders">0</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-muted">总收入</h5>
                            <h3 class="mb-0 text-primary" id="total-revenue">¥0.00</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选条件 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-funnel"></i> 筛选条件</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">订单状态</label>
                            <select class="form-select" id="order-status-filter">
                                <option value="">全部状态</option>
                                <option value="pending">待支付</option>
                                <option value="paid">已支付</option>
                                <option value="cancelled">已取消</option>
                                <option value="refunded">已退款</option>
                                <option value="error">支付失败</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">支付方式</label>
                            <select class="form-select" id="payment-method-filter">
                                <option value="">全部方式</option>
                                <option value="epay">易支付</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="start-date-filter">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="end-date-filter">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">订单号</label>
                            <input type="text" class="form-control" id="order-id-filter" placeholder="输入订单号搜索">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">用户邮箱</label>
                            <input type="text" class="form-control" id="user-email-filter" placeholder="输入用户邮箱搜索">
                        </div>
                        <div class="col-12">
                            <button type="button" class="btn btn-primary" onclick="searchOrders()">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetOrderFilters()">
                                <i class="bi bi-arrow-clockwise"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-list-ul"></i> 订单列表</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="orders-table">
                            <thead>
                                <tr>
                                    <th scope="col" data-sortable data-sort-type="string" data-key="order_id">订单号</th>
                                    <th scope="col" data-sortable data-sort-type="string" data-key="user_email">用户</th>
                                    <th scope="col" data-sortable data-sort-type="number" data-key="amount">金额</th>
                                    <th scope="col" data-sortable data-sort-type="string" data-key="status">状态</th>
                                    <th scope="col" data-sortable data-sort-type="string" data-key="payment_method">支付方式</th>
                                    <th scope="col" data-sortable data-sort-type="string" data-key="subscription_type_name">订阅类型</th>
                                    <th scope="col" data-sortable data-sort-type="date" data-key="created_at">创建时间</th>
                                    <th scope="col" data-sortable data-sort-type="date" data-key="paid_at">支付时间</th>
                                    <th scope="col">操作</th>
                                </tr>
                            </thead>
                            <tbody id="orders-table-body">
                                <!-- 订单列表将通过JavaScript填充 -->
                            </tbody>
                        </table>
                    </div>
                    <!-- 分页 -->
                    <nav aria-label="订单分页">
                        <ul class="pagination justify-content-center" id="orders-pagination">
                            <!-- 分页将通过JavaScript填充 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 兑换码管理 -->
        <div id="section-redemption-codes" class="section-content" style="display: none;">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="bi bi-gift me-2"></i><span data-i18n="admin.redemption.title">兑换码管理</span></h1>
                <div class="btn-toolbar toolbar-buttons mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createRedemptionCodeModal">
                            <i class="bi bi-plus-circle"></i> <span data-i18n="admin.redemption.generate">生成兑换码</span>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="refresh-redemption-codes-btn">
                            <i class="bi bi-arrow-clockwise"></i> <span data-i18n="common.refresh">刷新</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-muted" data-i18n="admin.redemption.total_codes">总兑换码数</h5>
                            <h3 class="mb-0 text-primary" id="total-redemption-codes">0</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-muted" data-i18n="admin.redemption.used_codes">已使用</h5>
                            <h3 class="mb-0 text-success" id="used-redemption-codes">0</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-muted" data-i18n="admin.redemption.unused_codes">未使用</h5>
                            <h3 class="mb-0 text-info" id="unused-redemption-codes">0</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-muted" data-i18n="admin.redemption.total_value">总价值</h5>
                            <h3 class="mb-0 text-warning" id="total-redemption-value">¥0.00</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选条件 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-funnel"></i> <span data-i18n="admin.redemption.filter_conditions">筛选条件</span></h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="admin.redemption.usage_status">使用状态</label>
                            <select class="form-select" id="redemption-status-filter">
                                <option value="" data-i18n="admin.redemption.all_status">全部状态</option>
                                <option value="false" data-i18n="admin.redemption.unused">未使用</option>
                                <option value="true" data-i18n="admin.redemption.used">已使用</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="admin.redemption.creator">创建者</label>
                            <input type="text" class="form-control" id="creator-filter" data-i18n-placeholder="admin.redemption.creator_placeholder" placeholder="输入创建者邮箱">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="admin.redemption.code">兑换码</label>
                            <input type="text" class="form-control" id="code-filter" data-i18n-placeholder="admin.redemption.code_placeholder" placeholder="输入兑换码">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="common.actions">操作</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-primary" id="apply-redemption-filters">
                                    <i class="bi bi-search"></i> <span data-i18n="common.search">搜索</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 兑换码列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-list-ul"></i> <span data-i18n="admin.redemption.code_list">兑换码列表</span></h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="redemption-codes-table">
                            <thead>
                                <tr>
                                    <th scope="col" data-i18n="admin.redemption.code">兑换码</th>
                                    <th scope="col" data-i18n="admin.redemption.amount">金额</th>
                                    <th scope="col" data-i18n="common.status">状态</th>
                                    <th scope="col" data-i18n="admin.redemption.creator">创建者</th>
                                    <th scope="col" data-i18n="admin.redemption.user">使用者</th>
                                    <th scope="col" data-i18n="common.create_time">创建时间</th>
                                    <th scope="col" data-i18n="admin.redemption.used_time">使用时间</th>
                                    <th scope="col" data-i18n="admin.redemption.expire_time">过期时间</th>
                                    <th scope="col" data-i18n="common.actions">操作</th>
                                </tr>
                            </thead>
                            <tbody id="redemption-codes-table-body">
                                <!-- 兑换码列表将通过JavaScript填充 -->
                            </tbody>
                        </table>
                    </div>
                    <!-- 分页 -->
                    <nav aria-label="兑换码分页">
                        <ul class="pagination justify-content-center" id="redemption-codes-pagination">
                            <!-- 分页将通过JavaScript填充 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

                </div>
            </main>
        </div>
    </div>

  <!-- 添加订阅类型的模态框 -->
  <div class="modal fade" id="addSubscriptionTypeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">添加订阅类型</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="subscription-type-form" class="needs-validation" novalidate>
            <div class="mb-3">
              <label for="type-name" class="form-label">显示名称</label>
              <input type="text" class="form-control" id="type-name" placeholder="例如：月付会员, 学生会员" required>
              <div class="form-text">用户可见的名称</div>
              <div class="invalid-feedback">请输入显示名称。</div>
            </div>
            <div class="mb-3">
              <label for="type-max-devices" class="form-label">最大设备数</label>
              <input type="number" class="form-control" id="type-max-devices" min="1" value="1" required>
              <div class="form-text">此套餐允许的最大设备数量</div>
              <div class="invalid-feedback">请输入有效的最大设备数 (至少为 1)。</div>
            </div>
            <div class="mb-3">
              <label for="type-price" class="form-label">价格</label>
              <input type="number" class="form-control" id="type-price" min="0" step="0.01" value="0" required>
              <div class="form-text">套餐原价</div>
              <div class="invalid-feedback">请输入有效的价格 (不能为负数)。</div>
            </div>
            <div class="mb-3">
              <label for="type-days" class="form-label">有效期(天)</label>
              <input type="number" class="form-control" id="type-days" min="1" value="30" required>
              <div class="form-text">订阅有效期天数</div>
              <div class="invalid-feedback">请输入有效的有效期天数 (至少为 1)。</div>
            </div>
            <div class="mb-3">
              <label for="type-requirements" class="form-label">套餐特性/适用条件</label>
              <textarea class="form-control" id="type-requirements" rows="5" placeholder="每行输入一个特性，例如：
ChatGPT Pro 全功能
包含GPTs, SORA等
国内专线优化
需要提供学生证"></textarea>
              <div class="form-text">可选。每行将显示为一个特性项（带✓图标）。如留空，将显示默认特性。</div>
            </div>
            <div class="mb-3">
              <label for="type-default-instance-capacity" class="form-label">默认实例容量</label>
              <input type="number" class="form-control" id="type-default-instance-capacity" min="0" placeholder="选填，新建实例时的默认容量">
              <div class="form-text">可选。创建基于此类型的订阅实例时，其实例的默认用户容量。如果为0或未填，则无默认值。</div>
              <div class="invalid-feedback">请输入有效的默认实例容量 (非负整数)。</div>
            </div>
            <div class="mb-3 form-check">
              <input type="checkbox" class="form-check-input" id="type-is-public" checked>
              <label class="form-check-label" for="type-is-public">在购买页面公开显示</label>
            </div>
            <div class="mb-3 form-check">
              <input type="checkbox" class="form-check-input" id="type-sync-to-claude">
              <label class="form-check-label" for="type-sync-to-claude">同步到Claude系统</label>
              <div class="form-text">勾选后，购买此套餐的用户订阅将自动同步到Claude-Code系统</div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="save-subscription-type-btn">保存</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 编辑订阅类型的模态框 -->
  <div class="modal fade" id="editSubscriptionTypeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">编辑订阅类型</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="edit-subscription-type-form" class="needs-validation" novalidate>
            <input type="hidden" id="edit-type-id">
            <div class="mb-3">
              <label for="edit-type-name" class="form-label">显示名称</label>
              <input type="text" class="form-control" id="edit-type-name" placeholder="例如：月付会员, 学生会员" required>
              <div class="form-text">用户可见的名称</div>
              <div class="invalid-feedback">请输入显示名称。</div>
            </div>
            <div class="mb-3">
              <label for="edit-type-max-devices" class="form-label">最大设备数</label>
              <input type="number" class="form-control" id="edit-type-max-devices" min="1" value="1" required>
              <div class="form-text">此套餐允许的最大设备数量</div>
              <div class="invalid-feedback">请输入有效的最大设备数 (至少为 1)。</div>
            </div>
            <div class="mb-3">
              <label for="edit-type-price" class="form-label">价格</label>
              <input type="number" class="form-control" id="edit-type-price" min="0" step="0.01" value="0" required>
              <div class="form-text">套餐原价</div>
              <div class="invalid-feedback">请输入有效的价格 (不能为负数)。</div>
            </div>
            <div class="mb-3">
              <label for="edit-type-days" class="form-label">有效期(天)</label>
              <input type="number" class="form-control" id="edit-type-days" min="1" value="30" required>
              <div class="form-text">订阅有效期天数</div>
              <div class="invalid-feedback">请输入有效的有效期天数 (至少为 1)。</div>
            </div>
            <div class="mb-3">
              <label for="edit-type-requirements" class="form-label">套餐特性/适用条件</label>
              <textarea class="form-control" id="edit-type-requirements" rows="5" placeholder="每行输入一个特性，例如：
ChatGPT Pro 全功能
包含GPTs, SORA等
国内专线优化
需要提供学生证"></textarea>
              <div class="form-text">可选。每行将显示为一个特性项（带✓图标）。如留空，将显示默认特性。</div>
            </div>
            <div class="mb-3">
              <label for="edit-type-default-instance-capacity" class="form-label">默认实例容量</label>
              <input type="number" class="form-control" id="edit-type-default-instance-capacity" min="0" placeholder="选填，新建实例时的默认容量">
              <div class="form-text">可选。创建基于此类型的订阅实例时，其实例的默认用户容量。如果为0或未填，则无默认值。</div>
              <div class="invalid-feedback">请输入有效的默认实例容量 (非负整数)。</div>
            </div>
            <div class="mb-3 form-check">
              <input type="checkbox" class="form-check-input" id="edit-type-is-public" checked>
              <label class="form-check-label" for="edit-type-is-public">在购买页面公开显示</label>
            </div>
            <div class="mb-3 form-check">
              <input type="checkbox" class="form-check-input" id="edit-type-sync-to-claude">
              <label class="form-check-label" for="edit-type-sync-to-claude">同步到Claude系统</label>
              <div class="form-text">勾选后，购买此套餐的用户订阅将自动同步到Claude-Code系统</div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="save-edit-subscription-type-btn">更新</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加账号模态框 -->
  <div class="modal fade" id="addAccountModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">添加新ADSpower账号</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
        <div class="modal-body">
          <!-- Removed redundant error alert -->
          <!-- <div
            class="alert alert-danger"
            id="account-error-alert"
            style="display: none;"
          ></div> -->
          <form id="account-form" class="needs-validation" novalidate>
            <div class="mb-3">
              <label for="username" class="form-label">用户名</label>
              <input
                type="text"
                class="form-control"
                id="username"
                name="username"
                required
              />
              <div class="invalid-feedback">
                请输入 ADSpower 用户名。
              </div>
            </div>
            <div class="mb-3">
              <label for="password" class="form-label">密码</label>
              <input
                type="password"
                class="form-control"
                id="password"
                name="password"
                required
              />
              <div class="invalid-feedback">
                请输入 ADSpower 密码。
              </div>
            </div>
            <div class="mb-3">
              <label for="totp_secret" class="form-label">TOTP密钥</label>
              <input
                type="text"
                class="form-control"
                id="totp_secret"
                name="totp_secret"
              />
              <small class="text-muted"
                >用于生成2FA验证码</small
              >
              <!-- No specific validation needed for optional field -->
            </div>
            <div class="mb-3">
              <label for="max_devices" class="form-label">最大设备数</label>
              <input
                type="number"
                class="form-control"
                id="max_devices"
                name="max_devices"
                value="9"
                min="1"
                max="1000" <!-- Increased max slightly -->
                required
              />
              <small class="text-muted"
                >该账号最多允许多少台设备同时登录</small
              >
              <div class="invalid-feedback">
                请输入有效的最大设备数 (至少为 1)。
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">订阅实例关联</label>
              <p class="form-control-plaintext text-muted">
                创建账号后，可以在账号列表中使用"管理实例"功能来关联订阅实例
              </p>
            </div>
            <div class="mb-3">
              <label for="cookies" class="form-label"
                >Cookies (JSON格式)</label
              >
              <textarea
                class="form-control cookie-input"
                id="cookies"
                name="cookies"
                placeholder='[{"domain": ".adspower.net", "name": "cookie_name", "value": "cookie_value", "path": "/"}]'
              ></textarea>
              <small class="text-muted"
                >ADSpower网站的Cookies，JSON数组格式，包含domain、name、value、path等字段</small
              >
              <!-- Basic JSON structure check can be added in JS if complex validation is needed -->
               <div class="invalid-feedback" id="cookies-invalid-feedback">
                请输入有效的 JSON 数组格式的 Cookies。
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            取消
          </button>
          <button type="button" class="btn btn-primary" id="save-account-btn">
            保存
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 编辑账号模态框 -->
  <div
    class="modal fade"
    id="editAccountModal"
    tabindex="-1"
    aria-hidden="true"
  >
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">编辑ADSpower账号</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
        <div class="modal-body">
          <div
            class="alert alert-danger"
            id="edit-account-error-alert"
            style="display: none;"
          ></div>
          <form id="edit-account-form" class="needs-validation" novalidate>
            <input type="hidden" id="edit-account-id" />
            <div class="mb-3">
              <label for="edit-username" class="form-label">用户名</label>
              <input
                type="text"
                class="form-control"
                id="edit-username"
                name="username"
                required
              />
              <div class="invalid-feedback">
                请输入 ADSpower 用户名。
              </div>
            </div>
            <div class="mb-3">
              <label for="edit-password" class="form-label">密码</label>
              <input
                type="password"
                class="form-control"
                id="edit-password"
                name="password"
                placeholder="留空表示不修改密码"
              />
              <small class="text-muted"
                >如果不需要修改密码，请留空</small
              >
               <!-- No validation needed for optional password -->
            </div>
            <div class="mb-3">
              <label for="edit-totp_secret" class="form-label"
                >TOTP密钥</label
              >
              <input
                type="text"
                class="form-control"
                id="edit-totp_secret"
                name="totp_secret"
              />
              <small class="text-muted"
                >用于生成2FA验证码</small
              >
            </div>
            <div class="mb-3">
              <label for="edit-max_devices" class="form-label"
                >最大设备数</label
              >
              <input
                type="number"
                class="form-control"
                id="edit-max_devices"
                name="max_devices"
                min="1"
                max="50"
                required
              />
              <small class="text-muted"
                >该账号最多允许多少台设备同时登录</small
              >
            </div>
            <div class="mb-3">
              <label class="form-label">关联的订阅实例</label>
              <div id="edit-subscription-instances-info" class="form-control-plaintext">
                加载中...
              </div>
              <small class="text-muted">要管理账号与订阅实例的关联，请在账号列表中使用"管理实例"功能</small>
            </div>
            <div class="mb-3">
              <label for="edit-cookies" class="form-label"
                >Cookies (JSON格式)</label
              >
              <textarea
                class="form-control cookie-input"
                id="edit-cookies"
                name="cookies"
                placeholder='[{"domain": ".adspower.net", "name": "cookie_name", "value": "cookie_value", "path": "/"}]'
              ></textarea>
              <small class="text-muted"
                >ADSpower网站的Cookies，JSON数组格式，包含domain、name、value、path等字段</small
              >
              <!-- Basic JSON structure check can be added in JS if complex validation is needed -->
              <div class="invalid-feedback" id="edit-cookies-invalid-feedback">
                请输入有效的 JSON 数组格式的 Cookies。
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            取消
          </button>
          <button type="button" class="btn btn-primary" id="update-account-btn">
            更新
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 设备详情模态框 -->
  <div class="modal fade" id="deviceDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">设备详情</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <h6>基本信息</h6>
              <table class="table table-sm">
                <tr>
                  <th>ID</th>
                  <td id="device-detail-id"></td>
                </tr>
                <tr>
                  <th>设备名称</th>
                  <td id="device-detail-name"></td>
                </tr>
                <tr>
                  <th>IP地址</th>
                  <td id="device-detail-ip"></td>
                </tr>
                <tr>
                  <th>设备类型</th>
                  <td id="device-detail-type"></td> <!-- Added Type Row -->
                </tr>
                <!-- Removed Status Row -->
                <!-- <tr>
                  <th>状态</th>
                  <td id="device-detail-status"></td>
                </tr> -->
                <tr>
                  <th>创建时间</th>
                  <td id="device-detail-created-at"></td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <h6>关联信息</h6>
              <table class="table table-sm">
                <tr>
                  <th>用户</th>
                  <td id="device-detail-user"></td>
                </tr>
                <tr>
                  <th>AdsPower账号</th>
                  <td id="device-detail-adspower"></td>
                </tr>
              </table>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 编辑用户模态框 -->
  <div class="modal fade" id="editUserModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">编辑用户信息</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="alert alert-danger" id="edit-user-error-alert" style="display: none;"></div>
          <form id="edit-user-form" class="needs-validation" novalidate>
            <input type="hidden" id="edit-user-id">
            <!-- <div class="mb-3">
              <label for="edit-user-username" class="form-label">用户名</label>
              <input type="text" class="form-control" id="edit-user-username" required>
            </div> --> <!-- Remove username input -->
            <div class="mb-3">
              <label for="edit-user-email" class="form-label">邮箱</label>
              <input type="email" class="form-control" id="edit-user-email" required>
              <div class="invalid-feedback">
                请输入有效的邮箱地址。
              </div>
            </div>
            <div class="mb-3 form-check">
              <input type="checkbox" class="form-check-input" id="edit-user-is-admin">
              <label class="form-check-label" for="edit-user-is-admin">管理员</label>
            </div>
            <div class="mb-3 form-check">
              <input type="checkbox" class="form-check-input" id="edit-user-is-active">
              <label class="form-check-label" for="edit-user-is-active">启用账号</label>
            </div>
            <div class="mb-3">
              <label for="edit-user-remark" class="form-label">备注</label>
              <textarea class="form-control" id="edit-user-remark" rows="3" placeholder="可以在此记录关于该用户的备注信息"></textarea>
            </div>
            
            <!-- 订阅管理部分 -->
            <div class="mb-3 border-top pt-3">
              <h6>订阅管理</h6>
              
              <div id="manage-subscription-fields">
                <div class="mb-3">
                  <label for="edit-user-subscription-type" class="form-label">订阅类型</label>
                  <select class="form-select" id="edit-user-subscription-type">
                    <option value="">请选择订阅类型...</option>
                  </select>
                </div>
                
                <div class="mb-3">
                  <label for="edit-user-subscription-instance" class="form-label">订阅实例（车次）</label>
                  <select class="form-select" id="edit-user-subscription-instance">
                    <option value="">请先选择订阅类型...</option>
                  </select>
                  <div class="form-text">选择具体的订阅实例（可用容量将显示在选项中）</div>
                </div>
                
                <div class="mb-3">
                  <label for="edit-user-subscription-days" class="form-label">订阅天数</label>
                  <input type="number" class="form-control" id="edit-user-subscription-days" min="1" value="30">
                </div>
              </div>
            </div>

            <div class="mb-3 border-top pt-3">
              <h6>密码管理</h6>
              <div id="reset-password-fields">
                <div class="mb-3">
                  <label for="edit-user-new-password" class="form-label">新密码</label>
                  <input type="password" class="form-control" id="edit-user-new-password" minlength="8">
                  <div class="form-text">留空表示不修改密码。密码必须至少8位，包含大小写字母、数字和特殊符号</div>
                  <div class="invalid-feedback" id="edit-user-new-password-feedback">
                    请输入至少8位的新密码。
                  </div>
                </div>
                <div class="mb-3">
                  <label for="edit-user-confirm-password" class="form-label">确认密码</label>
                  <input type="password" class="form-control" id="edit-user-confirm-password">
                  <div class="invalid-feedback" id="edit-user-confirm-password-feedback">
                    两次输入的密码不一致或确认密码为空。
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <div class="d-flex w-100 justify-content-between">
            <button type="button" class="btn btn-outline-danger" id="delete-user-in-edit-btn">
              <i class="bi bi-trash me-1"></i>删除用户
            </button>
            <div>
              <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">取消</button>
              <button type="button" class="btn btn-primary" id="save-user-changes-btn">保存更改</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 用户详情模态框 -->
  <div class="modal fade" id="userDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">用户详情</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <h6>基本信息</h6>
              <table class="table table-sm">
                <tr>
                  <th>ID</th>
                  <td id="user-detail-id"></td>
                </tr>
                <tr>
                  <th>邮箱</th>
                  <td id="user-detail-email"></td>
                </tr>
                <tr>
                  <th>管理员</th>
                  <td id="user-detail-is-admin"></td>
                </tr>
                <tr>
                  <th>激活状态</th>
                  <td id="user-detail-is-active"></td>
                </tr>
                <tr>
                  <th>最后登录</th>
                  <td id="user-detail-last-login"></td>
                </tr>
                <tr>
                  <th>创建时间</th>
                  <td id="user-detail-created-at"></td>
                </tr>
                <!-- <tr>
                  <th>邮箱验证</th>
                  <td id="user-detail-is-email-verified"></td>
                </tr> -->
              </table>
            </div>
            <div class="col-md-6">
              <h6>订阅信息</h6>
              <table class="table table-sm" id="user-detail-subscription-table">
                <tr>
                  <th>订阅计划</th>
                  <td id="user-detail-subscription-plan"></td>
                </tr>
                <tr>
                  <th>订阅实例</th>
                  <td id="user-detail-subscription-instance"></td>
                </tr>
                <tr>
                  <th>状态</th>
                  <td id="user-detail-subscription-status"></td>
                </tr>
                <tr>
                  <th>开始日期</th>
                  <td id="user-detail-subscription-start-date"></td>
                </tr>
                <tr>
                  <th>结束日期</th>
                  <td id="user-detail-subscription-end-date"></td>
                </tr>
                <tr>
                  <th>设备上限</th>
                  <td id="user-detail-subscription-max-devices"></td>
                </tr>
              </table>
              <div id="user-detail-no-subscription" style="display: none;">
                <div class="alert alert-info">该用户无有效订阅</div>
              </div>
            </div>
          </div>
          
          <div class="mt-4">
            <h6>设备列表 (<span id="user-detail-device-count">0</span>)</h6>
            <div class="table-responsive">
              <table class="table table-striped table-sm">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>设备名称</th>
                    <th>设备类型</th>
                    <th>IP地址</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="user-detail-devices-table-body">
                  <!-- 设备列表将在此处动态填充 -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 编辑订阅模态框 -->
  <div class="modal fade" id="editSubscriptionModal" tabindex="-1" aria-labelledby="editSubscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="editSubscriptionModalLabel">编辑订阅信息</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="edit-subscription-form" class="needs-validation" novalidate>
            <input type="hidden" id="edit-subscription-id">
            <div class="mb-3">
              <label for="edit-sub-user-email" class="form-label">用户邮箱</label>
              <input type="email" class="form-control" id="edit-sub-user-email" readonly disabled>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="edit-sub-plan" class="form-label">订阅计划</label>
                <select class="form-select" id="edit-sub-plan" required>
                  <!-- 订阅类型选项将通过JavaScript动态加载 -->
                  <option value="">-- 选择订阅计划 --</option>
                </select>
                <div class="invalid-feedback">请选择一个订阅计划。</div>
              </div>
              <div class="col-md-6 mb-3">
                <label for="edit-sub-instance" class="form-label">订阅实例（车次）</label>
                <select class="form-select" id="edit-sub-instance" required>
                  <option value="">-- 请先选择订阅计划 --</option>
                </select>
                <div class="invalid-feedback">请选择一个订阅实例。</div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="edit-sub-max-devices" class="form-label">最大设备数</label>
                <input type="number" class="form-control" id="edit-sub-max-devices" min="0" placeholder="留空则使用计划默认值">
                <div class="form-text">该订阅允许的最大设备数量。</div>
                 <div class="invalid-feedback">请输入有效的最大设备数 (非负整数)。</div>
              </div>
              <div class="col-md-6 mb-3">
                <!-- 占位，保持布局对齐 -->
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="edit-sub-start-date" class="form-label">开始日期</label>
                <input type="datetime-local" class="form-control" id="edit-sub-start-date" required>
                <div class="invalid-feedback">请输入开始日期。</div>
              </div>
              <div class="col-md-6 mb-3">
                <label for="edit-sub-end-date" class="form-label">结束日期</label>
                <input type="datetime-local" class="form-control" id="edit-sub-end-date" required>
                <div class="invalid-feedback">请输入结束日期。</div>
              </div>
            </div>
            <div class="mb-3">
              <label for="edit-sub-remark" class="form-label">备注</label>
              <textarea class="form-control" id="edit-sub-remark" rows="3" placeholder="输入备注信息（可选）"></textarea>
              <div class="form-text">可以在此记录订阅相关的备注信息。</div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="save-subscription-changes-btn">保存更改</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 订阅详情模态框 -->
  <div class="modal fade" id="subscriptionDetailModal" tabindex="-1" aria-labelledby="subscriptionDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="subscriptionDetailModalLabel">订阅详情</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div id="subscription-detail-loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载订阅详情...</p>
          </div>
          <div id="subscription-detail-content" style="display: none;">
            <!-- 订阅基本信息 -->
            <div class="card mb-3">
              <div class="card-header bg-light">
                <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>基本信息</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">用户邮箱</label>
                    <div id="detail-user-email" class="fw-semibold">-</div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">订阅状态</label>
                    <div id="detail-status" class="fw-semibold">-</div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">开始时间</label>
                    <div id="detail-start-date" class="fw-semibold">-</div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">结束时间</label>
                    <div id="detail-end-date" class="fw-semibold">-</div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 订阅套餐信息 -->
            <div class="card mb-3">
              <div class="card-header bg-light">
                <h6 class="mb-0"><i class="bi bi-box-seam me-2"></i>套餐信息</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">套餐名称</label>
                    <div id="detail-plan-name" class="fw-semibold">-</div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">套餐价格</label>
                    <div id="detail-plan-price" class="fw-semibold">-</div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">订阅天数</label>
                    <div id="detail-plan-days" class="fw-semibold">-</div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">最大设备数</label>
                    <div id="detail-max-devices" class="fw-semibold">-</div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12 mb-3">
                    <label class="text-muted small">备注</label>
                    <div id="detail-remark" class="fw-semibold" style="white-space: pre-wrap;">-</div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 支付信息 -->
            <div class="card mb-3">
              <div class="card-header bg-light">
                <h6 class="mb-0"><i class="bi bi-credit-card me-2"></i>支付信息</h6>
              </div>
              <div class="card-body" id="payment-details-content">
                <!-- 支付信息将通过JavaScript动态填充 -->
              </div>
            </div>
          </div>
          <div id="subscription-detail-error" class="alert alert-danger" style="display: none;">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <span id="subscription-detail-error-message">加载订阅详情时出错</span>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加订阅实例模态框 -->
  <div class="modal fade" id="addInstanceModal" tabindex="-1" aria-labelledby="addInstanceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addInstanceModalLabel">添加订阅实例 (车次)</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="add-instance-form" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="add-instance-name" class="form-label">实例名称</label>
                        <input type="text" class="form-control" id="add-instance-name" placeholder="例如: 月付套餐1号车" required>
                        <div class="form-text">如果留空，系统将尝试根据订阅类型自动生成。</div>
                        <div class="invalid-feedback">请输入实例名称。</div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="add-instance-subscription-type" class="form-label">订阅类型</label>
                            <select class="form-select" id="add-instance-subscription-type" required>
                                <option value="">-- 请选择订阅类型 --</option>
                                <!-- 订阅类型选项将通过JavaScript动态加载 -->
                            </select>
                            <div class="invalid-feedback">请选择一个订阅类型。</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="add-instance-capacity" class="form-label">容量 (最大用户数)</label>
                            <input type="number" class="form-control" id="add-instance-capacity" min="1" value="10" required>
                            <div class="form-text">此车次能容纳的最大用户数量。</div>
                            <div class="invalid-feedback">请输入有效的容量 (至少为1)。</div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="add-instance-status" class="form-label">状态</label>
                        <select class="form-select" id="add-instance-status" required>
                            <option value="active" selected>活跃</option>
                            <option value="inactive">禁用</option>
                        </select>
                        <div class="invalid-feedback">请选择状态。</div>
                    </div>
                    <div class="mb-3">
                        <label for="add-instance-description" class="form-label">描述</label>
                        <textarea class="form-control" id="add-instance-description" rows="3" placeholder="例如: 专为月付用户准备的高速车次"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="save-instance-btn">保存实例</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑订阅实例模态框 -->
<div class="modal fade" id="editInstanceModal" tabindex="-1" aria-labelledby="editInstanceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editInstanceModalLabel">编辑订阅实例 (车次)</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="edit-instance-form" class="needs-validation" novalidate>
                    <input type="hidden" id="edit-instance-id">
                    <div class="mb-3">
                        <label for="edit-instance-name" class="form-label">实例名称 (车次名)</label>
                        <input type="text" class="form-control" id="edit-instance-name" required>
                        <div class="invalid-feedback">请输入实例名称。</div>
                    </div>
                     <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit-instance-subscription-type" class="form-label">订阅类型</label>
                            <select class="form-select" id="edit-instance-subscription-type" required>
                                <!-- 订阅类型选项将通过JavaScript动态加载 -->
                            </select>
                            <div class="invalid-feedback">请选择一个订阅类型。</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit-instance-capacity" class="form-label">容量</label>
                            <input type="number" class="form-control" id="edit-instance-capacity" required min="0">
                        </div>
                    </div>
                    <div class="mb-3 form-check">
                      <input type="checkbox" class="form-check-input" id="edit-instance-is-active">
                      <label class="form-check-label" for="edit-instance-is-active">是否激活</label>
                    </div>
                    <div class="mb-3">
                      <label for="edit-instance-description" class="form-label">描述</label>
                      <textarea class="form-control" id="edit-instance-description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="update-instance-btn">更新实例</button>
            </div>
        </div>
    </div>
</div>

  <!-- 查看订阅实例详情模态框 -->
  <div class="modal fade" id="viewInstanceDetailsModal" tabindex="-1" aria-labelledby="viewInstanceDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="viewInstanceDetailsModalLabel">订阅实例详情</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <h6>基本信息</h6>
              <table class="table table-sm table-bordered">
                <tbody>
                  <tr>
                    <th style="width: 30%;">ID</th>
                    <td id="view-instance-id"></td>
                  </tr>
                  <tr>
                    <th>实例名称</th>
                    <td id="view-instance-name"></td>
                  </tr>
                  <tr>
                    <th>订阅类型</th>
                    <td id="view-instance-type"></td>
                  </tr>
                  <tr>
                    <th>容量</th>
                    <td id="view-instance-capacity"></td>
                  </tr>
                  <tr>
                    <th>当前用户数</th>
                    <td id="view-instance-current-users"></td>
                  </tr>
                  <tr>
                    <th>状态</th>
                    <td id="view-instance-status"></td>
                  </tr>
                  <tr>
                    <th>描述</th>
                    <td id="view-instance-description" style="white-space: pre-wrap;"></td>
                  </tr>
                  <tr>
                    <th>创建时间</th>
                    <td id="view-instance-created-at"></td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="col-md-6">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h6>关联的AdsPower账号 (<span id="instance-adspower-count">0</span>)</h6>
                <button class="btn btn-sm btn-primary" id="manage-instance-adspower-btn">
                  <i class="bi bi-gear"></i> 管理账号
                </button>
              </div>
              <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                <table class="table table-sm table-striped table-hover">
                  <thead>
                    <tr>
                      <th>账号ID</th>
                      <th>用户名</th>
                      <th>设备数</th>
                      <th>状态</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody id="instance-adspower-table-body">
                    <!-- AdsPower账号列表将在此处动态填充 -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          
          <div class="mt-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <h6>当前用户列表 (<span id="instance-users-count">0</span>)</h6>
              <button class="btn btn-sm btn-warning" id="batch-modify-subscription-btn">
                <i class="bi bi-calendar-check"></i> 批量修改订阅时间
              </button>
            </div>
            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
              <table class="table table-sm table-striped table-hover">
                <thead>
                  <tr>
                    <th>
                      <input type="checkbox" id="select-all-users-checkbox">
                    </th>
                    <th>用户ID</th>
                    <th>邮箱</th>
                    <th>订阅开始</th>
                    <th>订阅结束</th>
                    <th>状态</th>
                  </tr>
                </thead>
                <tbody id="instance-users-table-body">
                  <!-- 用户列表将在此处动态填充 -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 管理实例AdsPower账号模态框 -->
  <div class="modal fade" id="manageInstanceAdspowerModal" tabindex="-1" aria-labelledby="manageInstanceAdspowerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="manageInstanceAdspowerModalLabel">管理订阅实例的AdsPower账号</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <h6>实例信息</h6>
            <table class="table table-sm">
              <tr>
                <th style="width: 30%;">实例名称</th>
                <td id="manage-instance-name"></td>
              </tr>
              <tr>
                <th>当前关联账号数</th>
                <td id="manage-instance-account-count"></td>
              </tr>
            </table>
          </div>
          
          <div class="mb-3">
            <h6>添加AdsPower账号到此实例</h6>
            <div class="input-group">
              <select class="form-select" id="add-adspower-to-instance-select" multiple style="height: 150px;">
                <!-- 可选的AdsPower账号将在此处动态填充 -->
              </select>
              <button class="btn btn-primary" id="add-adspower-to-instance-btn">
                <i class="bi bi-plus-circle"></i> 添加选中的账号
              </button>
            </div>
            <small class="text-muted">按住Ctrl/Cmd键可多选</small>
          </div>
          
          <div class="mb-3">
            <h6>当前关联的账号</h6>
            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
              <table class="table table-sm table-striped">
                <thead>
                  <tr>
                    <th>账号ID</th>
                    <th>用户名</th>
                    <th>状态</th>
                  </tr>
                </thead>
                <tbody id="manage-instance-adspower-table-body">
                  <!-- 当前关联的账号列表将在此处动态填充 -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 批量修改订阅时间模态框 -->
  <div class="modal fade" id="batchModifySubscriptionModal" tabindex="-1" aria-labelledby="batchModifySubscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="batchModifySubscriptionModalLabel">批量修改订阅时间</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <div class="alert alert-info">
              <i class="bi bi-info-circle"></i> 将批量修改 <span id="selected-users-count">0</span> 个用户的订阅时间
            </div>
          </div>
          <form id="batch-modify-subscription-form">
            <div class="alert alert-warning mb-3">
              <i class="bi bi-exclamation-triangle"></i> 您可以选择只修改开始时间或结束时间，留空的字段将保持原值不变
            </div>
            <div class="mb-3">
              <label for="batch-subscription-start" class="form-label">订阅开始时间</label>
              <input type="datetime-local" class="form-control" id="batch-subscription-start">
              <div class="form-text">留空则不修改开始时间</div>
            </div>
            <div class="mb-3">
              <label for="batch-subscription-end" class="form-label">订阅结束时间</label>
              <input type="datetime-local" class="form-control" id="batch-subscription-end">
              <div class="form-text">留空则不修改结束时间</div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="confirm-batch-modify-btn">确认修改</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast 容器 -->
  <div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 1100">
    <!-- Toasts 将会添加到这里 -->
  </div>

  <!-- 引入 Bootstrap、jQuery 及业务逻辑脚本 -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <!-- 通用工具函数库，必须在其他自定义JS之前加载 -->
  <script src="{{ url_for('static', filename='js/utils.js') }}"></script>
  <!-- 国际化支持 -->
  <script src="{{ url_for('static', filename='js/i18n.js') }}"></script>
  <script src="{{ url_for('static', filename='js/admin.js') }}"></script>
  <script src="{{ url_for('static', filename='js/admin_orders.js') }}"></script>

  <!-- 移动端侧边栏切换功能 -->
  <script>
    $(document).ready(function() {
      // 使用Bootstrap的原生collapse功能处理侧边栏
      // 点击遮罩层关闭侧边栏
      $('#sidebarBackdrop').on('click', function() {
        $('#sidebarMenu').removeClass('show');
        $(this).removeClass('show');
      });
      
      // 监听侧边栏的显示/隐藏事件
      $('#sidebarMenu').on('show.bs.collapse', function() {
        $('#sidebarBackdrop').addClass('show');
      });
      
      $('#sidebarMenu').on('hide.bs.collapse', function() {
        $('#sidebarBackdrop').removeClass('show');
      });
      
      // 点击侧边栏链接后自动关闭（移动端）
      if (window.innerWidth <= 768) {
        $('#sidebarMenu .nav-link').on('click', function() {
          $('#sidebarMenu').collapse('hide');
        });
      }
      
      // 窗口大小改变时的处理
      $(window).on('resize', function() {
        if (window.innerWidth > 768) {
          $('#sidebarBackdrop').removeClass('show');
        }
      });
    });
  </script>

  <!-- 查看订阅类型详情模态框 -->
  <div class="modal fade" id="viewSubscriptionTypeModal" tabindex="-1" aria-labelledby="viewSubscriptionTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="viewSubscriptionTypeModalLabel">订阅类型详情</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <h6>基本信息</h6>
              <table class="table table-sm table-bordered">
                <tbody>
                  <tr>
                    <th style="width: 30%;">ID</th>
                    <td id="view-type-id"></td>
                  </tr>
                  <tr>
                    <th>显示名称</th>
                    <td id="view-type-name"></td>
                  </tr>
                  <tr>
                    <th>最大设备数</th>
                    <td id="view-type-max-devices"></td>
                  </tr>
                  <tr>
                    <th>价格</th>
                    <td id="view-type-price"></td> <!-- 确保此行存在且ID正确 -->
                  </tr>
                  <tr>
                    <th>有效期(天)</th>
                    <td id="view-type-days"></td>
                  </tr>
                  <tr>
                    <th>默认实例容量</th>
                    <td id="view-type-default-instance-capacity"></td>
                  </tr>
                  <tr>
                    <th>是否公开</th>
                    <td id="view-type-is-public"></td>
                  </tr>
                  <tr>
                    <th>同步到Claude</th>
                    <td id="view-type-sync-to-claude"></td>
                  </tr>
                  <tr>
                    <th>适用条件</th>
                    <td id="view-type-requirements" style="white-space: pre-wrap;"></td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="col-md-6">
              <h6>关联的订阅实例 (<span id="associated-instances-count">0</span>)</h6>
              <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                <table class="table table-sm table-striped table-hover">
                  <thead>
                    <tr>
                      <th>实例ID</th>
                      <th>实例名称</th>
                      <th>容量</th>
                      <th>当前用户</th>
                      <th>状态</th>
                    </tr>
                  </thead>
                  <tbody id="associated-instances-table-body">
                    <!-- 关联实例将在此处动态填充 -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 查看AdsPower账号详情模态框 -->
  <div class="modal fade" id="viewAdspowerAccountModal" tabindex="-1" aria-labelledby="viewAdspowerAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="viewAdspowerAccountModalLabel">AdsPower账号详情</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div id="adspower-account-detail-loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载账号详情...</p>
          </div>
          <div id="adspower-account-detail-content" style="display: none;">
            <!-- 基本信息 -->
            <div class="card mb-3">
              <div class="card-header bg-light">
                <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>基本信息</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">账号ID</label>
                    <div id="adspower-detail-id" class="fw-semibold">-</div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">用户名</label>
                    <div id="adspower-detail-username" class="fw-semibold">-</div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">账号状态</label>
                    <div id="adspower-detail-status" class="fw-semibold">-</div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">健康状态</label>
                    <div id="adspower-detail-health-status" class="fw-semibold">-</div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">当前设备数</label>
                    <div id="adspower-detail-current-devices" class="fw-semibold">-</div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">最大设备数</label>
                    <div id="adspower-detail-max-devices" class="fw-semibold">-</div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">创建时间</label>
                    <div id="adspower-detail-created-at" class="fw-semibold">-</div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="text-muted small">更新时间</label>
                    <div id="adspower-detail-updated-at" class="fw-semibold">-</div>
                  </div>
                </div>
                <div class="row" id="adspower-detail-health-message-row" style="display: none;">
                  <div class="col-12 mb-3">
                    <label class="text-muted small">健康状态详情</label>
                    <div id="adspower-detail-health-message" class="fw-semibold">-</div>
                  </div>
                </div>
                <div class="row" id="adspower-detail-description-row">
                  <div class="col-12 mb-3">
                    <label class="text-muted small">描述信息</label>
                    <div id="adspower-detail-description" class="fw-semibold">-</div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 所属订阅实例 -->
            <div class="card mb-3">
              <div class="card-header bg-light">
                <h6 class="mb-0"><i class="bi bi-diagram-3 me-2"></i>所属订阅实例</h6>
              </div>
              <div class="card-body">
                <div id="adspower-detail-instances-content">
                  <!-- 订阅实例列表将在此处动态填充 -->
                </div>
              </div>
            </div>
            
            <!-- 关联设备信息 -->
            <div class="card mb-3">
              <div class="card-header bg-light">
                <h6 class="mb-0"><i class="bi bi-laptop me-2"></i>关联设备 (<span id="adspower-detail-device-count">0</span>)</h6>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-sm table-striped table-hover">
                    <thead>
                      <tr>
                        <th>设备ID</th>
                        <th>设备名称</th>
                        <th>设备类型</th>
                        <th>用户</th>
                      </tr>
                    </thead>
                    <tbody id="adspower-detail-devices-table">
                      <!-- 设备列表将在此处动态填充 -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            
            <!-- Cookies信息 -->
            <div class="card mb-3">
              <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="bi bi-cookie me-2"></i>Cookies信息</h6>
                <button type="button" class="btn btn-sm btn-primary" id="adspower-detail-view-cookies-btn">
                  <i class="bi bi-eye"></i> 查看Cookies
                </button>
              </div>
              <div class="card-body">
                <div id="adspower-detail-cookies-content" style="display: none;">
                  <pre id="adspower-detail-cookies-json" class="bg-light p-3" style="max-height: 300px; overflow-y: auto;"></pre>
                </div>
                <div id="adspower-detail-cookies-placeholder">
                  <p class="text-muted mb-0">点击"查看Cookies"按钮以加载Cookies信息</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 创建兑换码模态框 -->
  <div class="modal fade" id="createRedemptionCodeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title"><i class="bi bi-gift me-2"></i><span data-i18n="admin.redemption.generate_title">生成兑换码</span></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="create-redemption-code-form" class="needs-validation" novalidate>
            <div class="mb-3">
              <label for="redemption-value" class="form-label" data-i18n="admin.redemption.redemption_amount">兑换金额 (元)</label>
              <input type="number" class="form-control" id="redemption-value" min="0.01" step="0.01" required>
              <div class="form-text" data-i18n="admin.redemption.amount_desc">用户兑换后获得的余额金额</div>
              <div class="invalid-feedback">请输入有效的兑换金额</div>
            </div>
            <div class="mb-3">
              <label for="redemption-count" class="form-label" data-i18n="admin.redemption.generate_count">生成数量</label>
              <input type="number" class="form-control" id="redemption-count" min="1" max="100" value="1" required>
              <div class="form-text" data-i18n="admin.redemption.count_desc">一次最多生成100个兑换码</div>
              <div class="invalid-feedback">请输入有效的生成数量 (1-100)</div>
            </div>
            <div class="mb-3">
              <label for="redemption-expires-days" class="form-label" data-i18n="admin.redemption.validity_days">有效期 (天)</label>
              <input type="number" class="form-control" id="redemption-expires-days" min="1" data-i18n-placeholder="admin.redemption.validity_desc" placeholder="留空表示永不过期">
              <div class="form-text" data-i18n="admin.redemption.validity_desc">兑换码的有效期，留空表示永不过期</div>
            </div>
            <div class="mb-3">
              <label for="redemption-description" class="form-label" data-i18n="admin.redemption.description">备注</label>
              <textarea class="form-control" id="redemption-description" rows="3" data-i18n-placeholder="admin.redemption.description_placeholder" placeholder="可选的备注信息"></textarea>
              <div class="form-text" data-i18n="admin.redemption.description_desc">用于标识这批兑换码的用途</div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-i18n="common.cancel">取消</button>
          <button type="button" class="btn btn-primary" id="create-redemption-code-btn">
            <i class="bi bi-plus-circle"></i> <span data-i18n="admin.redemption.generate_btn">生成兑换码</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 兑换码详情模态框 -->
  <div class="modal fade" id="redemptionCodeDetailModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title"><i class="bi bi-info-circle me-2"></i><span data-i18n="admin.redemption.detail_title">兑换码详情</span></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div id="redemption-code-detail-content">
            <!-- 详情内容将通过JavaScript填充 -->
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-i18n="common.close">关闭</button>
          <button type="button" class="btn btn-danger" id="disable-redemption-code-btn" style="display: none;">
            <i class="bi bi-x-circle"></i> <span data-i18n="admin.redemption.disable">禁用兑换码</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 遮罩层 -->
  <div class="sidebar-backdrop" id="sidebarBackdrop"></div>

</body>
</html> 
