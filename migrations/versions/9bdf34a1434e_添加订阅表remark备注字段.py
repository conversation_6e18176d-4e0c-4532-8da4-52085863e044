"""添加订阅表remark备注字段

Revision ID: 9bdf34a1434e
Revises: 3dcf0a236407
Create Date: 2025-06-16 15:01:35.891041

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9bdf34a1434e'
down_revision = '3dcf0a236407'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('remark', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.drop_column('remark')

    # ### end Alembic commands ###
