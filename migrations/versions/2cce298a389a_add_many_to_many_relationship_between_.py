"""Add many-to-many relationship between SubscriptionInstance and AdspowerAccount

Revision ID: 2cce298a389a
Revises: 9bdf34a1434e
Create Date: 2025-06-20 19:15:13.827074

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2cce298a389a'
down_revision = '9bdf34a1434e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # 1. 检查并创建多对多关联表
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    # 检查表是否已存在
    if 'subscription_instance_adspower_account' not in inspector.get_table_names():
        op.create_table('subscription_instance_adspower_account',
        sa.Column('subscription_instance_id', sa.Integer(), nullable=False),
        sa.Column('adspower_account_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['adspower_account_id'], ['adspower_accounts.id'], ),
        sa.ForeignKeyConstraint(['subscription_instance_id'], ['subscription_instances.id'], ),
        sa.PrimaryKeyConstraint('subscription_instance_id', 'adspower_account_id')
        )
    
    # 2. 检查adspower_accounts表是否有subscription_instance_id列
    columns = [col['name'] for col in inspector.get_columns('adspower_accounts')]
    
    if 'subscription_instance_id' in columns:
        # 迁移现有数据到新的关联表
        result = conn.execute(sa.text("""
            SELECT id, subscription_instance_id 
            FROM adspower_accounts 
            WHERE subscription_instance_id IS NOT NULL
        """))
        
        for account_id, instance_id in result:
            # 检查是否已存在关联
            existing = conn.execute(sa.text("""
                SELECT 1 FROM subscription_instance_adspower_account 
                WHERE subscription_instance_id = :instance_id 
                AND adspower_account_id = :account_id
            """), {"instance_id": instance_id, "account_id": account_id}).fetchone()
            
            if not existing:
                conn.execute(sa.text("""
                    INSERT INTO subscription_instance_adspower_account 
                    (subscription_instance_id, adspower_account_id, created_at)
                    VALUES (:instance_id, :account_id, datetime('now'))
                """), {"instance_id": instance_id, "account_id": account_id})
        
        # 3. 删除旧列（SQLite不支持直接删除列，需要重建表）
        with op.batch_alter_table('adspower_accounts', schema=None) as batch_op:
            batch_op.drop_column('subscription_instance_id')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('adspower_accounts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_instance_id', sa.INTEGER(), nullable=True))
        batch_op.create_foreign_key(batch_op.f('fk_adspower_accounts_subscription_instance_id'), 'subscription_instances', ['subscription_instance_id'], ['id'])

    op.drop_table('subscription_instance_adspower_account')
    # ### end Alembic commands ###
