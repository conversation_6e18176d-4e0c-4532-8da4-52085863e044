"""允许subscription_instance_id为空并添加级联删除

Revision ID: 4c83387761f1
Revises: c33c2f8ddbcd
Create Date: 2025-07-15 19:00:02.981352

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4c83387761f1'
down_revision = 'c33c2f8ddbcd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    # 修改 subscription_instance_id 列为可空
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.alter_column('subscription_instance_id',
                   existing_type=sa.INTEGER(),
                   nullable=True)
    
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    # 恢复 subscription_instance_id 列为非空
    # 注意：如果有数据的 subscription_instance_id 为 NULL，需要先处理
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.alter_column('subscription_instance_id',
                   existing_type=sa.INTEGER(),
                   nullable=False)
    
    # ### end Alembic commands ###
