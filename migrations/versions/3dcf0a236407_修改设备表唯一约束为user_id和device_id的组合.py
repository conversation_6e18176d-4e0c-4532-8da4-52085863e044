"""修改设备表唯一约束为user_id和device_id的组合

Revision ID: 3dcf0a236407
Revises: ff29d8973caf
Create Date: 2025-06-15 01:39:51.829457

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3dcf0a236407'
down_revision = 'ff29d8973caf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('devices', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_devices_device_id'))
        batch_op.create_index(batch_op.f('ix_devices_device_id'), ['device_id'], unique=False)
        batch_op.create_unique_constraint('uq_user_device', ['user_id', 'device_id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('devices', schema=None) as batch_op:
        batch_op.drop_constraint('uq_user_device', type_='unique')
        batch_op.drop_index(batch_op.f('ix_devices_device_id'))
        batch_op.create_index(batch_op.f('ix_devices_device_id'), ['device_id'], unique=1)

    # ### end Alembic commands ###
