"""添加sync_to_claude字段到subscription_types表

Revision ID: c33c2f8ddbcd
Revises: 5a35daf8f4ad
Create Date: 2025-06-29 15:16:37.701988

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c33c2f8ddbcd'
down_revision = '5a35daf8f4ad'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription_types', sa.<PERSON>umn('sync_to_claude', sa.<PERSON>(), nullable=True))
    
    # 设置默认值为 False
    op.execute("UPDATE subscription_types SET sync_to_claude = 0 WHERE sync_to_claude IS NULL")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription_types', 'sync_to_claude')
    # ### end Alembic commands ###
