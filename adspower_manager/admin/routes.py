from flask import g, request, current_app, jsonify
import logging
import functools # for functools.wraps

# Import the blueprint from __init__.py
from . import admin_bp
from ..utils import get_real_ip

# Logger for this blueprint
logger = logging.getLogger(__name__) 

# Standard library imports
import json
import re
import time
from datetime import datetime, timedelta, timezone
from decimal import Decimal, InvalidOperation

# Third-party imports
from werkzeug.exceptions import NotFound
from sqlalchemy import func, and_, or_, not_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import joinedload, contains_eager

# Project-specific imports
# Models
from ..models import (
    db, User, Subscription, AdspowerAccount,
    Device, PaymentRecord, Payment, LoginSession,
    SubscriptionType, SubscriptionInstance, DeviceAudit,
    RedemptionCode, BalanceTransaction # 添加新的模型
)

# Import shared utilities from decorators and utils
# (These were already imported above)

# Functions
from ..adspower_api import get_adspower_api
from ..webdriver_pool import get_account_driver_manager

# Project-specific imports
from ..decorators import login_required, admin_required # Import from ..decorators
from ..utils import jsonify_response # Import from ..utils

@admin_bp.route('/devices', methods=['GET'])
@admin_required
def get_all_devices():
    """(Admin) 获取所有设备列表 (分页) """
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '', type=str).strip()
    
    try:
        logger.info(f"[管理员] 查询所有设备列表 (页码: {page}, 每页: {per_page}, 搜索: '{search}')")
        
        # 构建查询
        query = Device.query
        
        # 如果有搜索关键词，添加搜索条件
        if search:
            # 构建搜索条件列表
            search_conditions = [
                Device.device_name.ilike(f'%{search}%'),
                Device.device_type.ilike(f'%{search}%'),
                Device.device_ip.ilike(f'%{search}%'),
                Device.user.has(User.email.ilike(f'%{search}%'))
            ]
            
            # 如果搜索词是数字，也搜索设备ID
            if search.isdigit():
                search_conditions.append(Device.id == int(search))
            
            # 应用搜索过滤器
            query = query.filter(or_(*search_conditions))
        
        # 通过服务获取所有设备，按创建时间降序排序（最新的设备在最上面）
        devices_pagination = query.order_by(Device.created_at.desc()).paginate(page=page, per_page=per_page, error_out=False) # Renamed to avoid conflict with module name
        
        # 格式化结果
        result = []
        for device_item in devices_pagination.items: # Renamed device to device_item
            user = User.query.get(device_item.user_id) if device_item.user_id else None
            
            # 获取ADSpower账号信息
            adspower_account_info = None # Renamed to avoid conflict
            if device_item.adspower_account_id:
                account_db = AdspowerAccount.query.get(device_item.adspower_account_id)
                if account_db: 
                    # 只计算来自未过期订阅的设备数
                    current_devices_count = Device.query.join(User).join(Subscription).filter(
                        Device.adspower_account_id == account_db.id,
                        Subscription.end_date > datetime.utcnow()
                    ).count()
                    adspower_account_info = {
                        'id': account_db.id,
                        'username': account_db.username,
                        'is_active': account_db.is_active,
                        'current_devices': current_devices_count, 
                        'max_devices': account_db.max_devices
                    }
            
            ip_address = device_item.device_ip
            if ip_address:
                ip_match = re.search(r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|[0-9a-f:]+)', ip_address)
                if ip_match:
                    ip_address = ip_match.group(1)
                else:
                    ip_address = re.sub(r'<[^>]*>?', '', ip_address).strip()
            
            device_info_dict = {
                'id': device_item.id,
                'user_id': device_item.user_id,
                'user_email': user.email if user else '未知用户',
                'adspower_account_id': device_item.adspower_account_id,
                'adspower_username': adspower_account_info['username'] if adspower_account_info else '未知账号',
                'device_name': device_item.device_name,
                'device_ip': ip_address, 
                'device_type': device_item.device_type,
                'created_at': device_item.created_at.isoformat(),
                'user': {
                    'id': user.id if user else None,
                    'username': user.email if user else '暂无',
                    'email': user.email if user else None
                },
                'adspower_account': adspower_account_info or {
                    'id': None,
                    'username': '暂无',
                    'is_active': False,
                    'current_devices': 0,
                    'max_devices': 0
                }
            }
            result.append(device_info_dict)
        
        logger.info(f"[管理员] 已返回 {len(result)} 台设备信息，总计 {devices_pagination.total} 台")
        
        return jsonify_response(success=True,
                                message="所有设备列表获取成功",
                                data={
                                    "devices": result,
                                    "total": devices_pagination.total,
                                    "pages": devices_pagination.pages,
                                    "page": devices_pagination.page,
                                    "per_page": devices_pagination.per_page
                                },
                                status_code=200)
        
    except Exception as e:
        logger.exception(f"[管理员] 获取设备列表时出错: {str(e)}")
        return jsonify_response(success=False, message="服务器内部错误", data=None, status_code=500) 

@admin_bp.route('/devices/<int:device_id>', methods=['DELETE'])
@admin_required
def admin_delete_device(device_id):
    """管理员删除设备 - 尝试远程登出并删除本地记录"""
    admin_user = g.user
    logger.info(f"管理员 {admin_user.id} 请求删除设备 DB ID: {device_id}")
    
    try:
        device_to_delete = Device.query.get(device_id) # Renamed to avoid conflict
        if not device_to_delete:
            logger.warning(f"管理员 {admin_user.id} 尝试删除不存在的设备 DB ID: {device_id}")
            return jsonify_response(success=False, message="设备不存在", data=None, status_code=404)
        
        device_owner_id = device_to_delete.user_id
        logger.info(f"设备 DB ID: {device_id} 属于用户 ID: {device_owner_id}. 由管理员 {admin_user.id} 操作删除。")

        if not device_to_delete.adspower_account_id:
            logger.warning(f"设备 DB ID: {device_id} 未关联 AdsPower 账号，将直接删除本地记录 (管理员操作)")
            try:
                # 使用设备服务删除设备（包含审计记录）
                from adspower_manager.services.device_service import DeviceService
                device_service = DeviceService()
                
                # 获取请求信息用于审计
                ip_address = get_real_ip(request)
                user_agent = request.headers.get('User-Agent', '')
                
                success = device_service.delete_device(
                    device_id=device_id,
                    operator_id=admin_user.id,  # 记录操作者为管理员
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                
                if success:
                    return jsonify_response(success=True, message="设备未关联远程账号，本地记录已由管理员删除", data=None, status_code=200)
                else:
                    return jsonify_response(success=False, message="删除本地设备记录时出错", data=None, status_code=500)
            except Exception as e_db_del_no_acc:
                db.session.rollback()
                logger.error(f"管理员删除未关联账号的设备 {device_id} 记录时数据库出错: {e_db_del_no_acc}", exc_info=True)
                return jsonify_response(success=False, message="删除本地设备记录时出错", data=None, status_code=500)

        adspower_account = AdspowerAccount.query.get(device_to_delete.adspower_account_id)
        if not adspower_account:
            logger.error(f"设备 DB ID: {device_id} 关联的 AdsPower 账号 ID: {device_to_delete.adspower_account_id} 不存在，将删除本地设备记录 (管理员操作)")
            try:
                # 使用设备服务删除设备（包含审计记录）
                from adspower_manager.services.device_service import DeviceService
                device_service = DeviceService()
                
                # 获取请求信息用于审计
                ip_address = get_real_ip(request)
                user_agent = request.headers.get('User-Agent', '')
                
                success = device_service.delete_device(
                    device_id=device_id,
                    operator_id=admin_user.id,  # 记录操作者为管理员
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                
                if success:
                    return jsonify_response(success=True, message="关联的AdsPower账号不存在，本地设备记录已由管理员删除", data=None, status_code=200)
                else:
                    return jsonify_response(success=False, message="删除本地设备记录时出错", data=None, status_code=500)
            except Exception as e_db_del_no_adsp_acc:
                db.session.rollback()
                logger.error(f"管理员删除关联账号不存在的设备 {device_id} 记录时数据库出错: {e_db_del_no_adsp_acc}", exc_info=True)
                return jsonify_response(success=False, message="删除本地设备记录时出错", data=None, status_code=500)

        device_id_on_ads = device_to_delete.device_id
        
        logout_message = "未尝试远程退出 (设备缺少唯一标识符)"
        logout_success = False
        if device_id_on_ads:
            try:
                adspower_api_instance = get_adspower_api() # Renamed to avoid conflict
                logger.info(f"管理员操作：设备 DB ID: {device_id} 使用device_id: {device_id_on_ads} 进行退出")
                logout_success, message_from_api = adspower_api_instance.logout_device(adspower_account, device_id_on_ads)
                logout_message = message_from_api
                logger.info(f"管理员操作：adspower_api.logout_device(设备 DB ID: {device_id}) 返回: success={logout_success}, message='{message_from_api}'")
                if not logout_success:
                    logger.error(f"管理员操作：AdsPower远程退出设备 DB ID: {device_id} 失败，原因: {message_from_api}。将继续删除本地记录。")
            except Exception as e_logout_api:
                logger.exception(f"管理员调用 logout_device 时发生意外错误 (设备 DB ID: {device_id}): {e_logout_api}")
                logout_message = f"尝试远程退出时出错: {str(e_logout_api)}"
                logout_success = False
        else:
             logger.warning(f"设备 DB ID: {device_id} 缺少 device_id，将跳过远程退出，仅删除本地记录 (管理员操作)")

        # 使用设备服务删除设备（包含审计记录）
        from adspower_manager.services.device_service import DeviceService
        device_service = DeviceService()
        
        # 获取请求信息用于审计
        ip_address = get_real_ip(request)
        user_agent = request.headers.get('User-Agent', '')
        
        success = device_service.delete_device(
            device_id=device_id,
            operator_id=admin_user.id,  # 记录操作者为管理员
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        if success:
            logger.info(f"设备 DB ID: {device_id} 本地设备记录已由管理员 {admin_user.id} 删除")
            
            final_message = "设备记录已删除。"
            if device_id_on_ads:
                if logout_success:
                    final_message += " 已成功在 AdsPower 端退出登录。"
                else:
                    final_message += f" 但在 AdsPower 端退出登录失败: {logout_message}"
            else:
                final_message += " (因缺少设备唯一标识符，未尝试远程退出)"
            
            return jsonify_response(success=True, message=final_message, data=None, status_code=200)
        else:
            logger.error(f"管理员删除设备 {device_id} 失败")
            return jsonify_response(success=False, message="删除设备失败", data=None, status_code=500)

    except Exception as e_top:
        logger.exception(f"管理员删除设备 DB ID: {device_id} 时发生顶层错误: {str(e_top)}")
        db.session.rollback()
        return jsonify_response(success=False, message="服务器内部错误", data=None, status_code=500) 

@admin_bp.route('/users', methods=['GET'])
@admin_required
def admin_get_users():
    """管理员获取用户列表接口"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search_query = request.args.get('search', None)
    subscription_filter = request.args.get('subscription', 'all') # all, active, inactive, expired

    try:
        query = User.query.options(db.joinedload(User.subscriptions))

        if search_query:
            query = query.filter(User.email.ilike(f'%{search_query}%'))

        now = datetime.utcnow()
        if subscription_filter == 'active':
            query = query.join(User.subscriptions).filter(Subscription.end_date > now)
        elif subscription_filter == 'expired':
            subq_active = db.session.query(User.id).join(User.subscriptions).filter(Subscription.end_date > now).subquery()
            query = query.filter(User.id.notin_(subq_active))
            # query = query.join(User.subscriptions) # Ensure they have at least one subscription

        pagination = query.order_by(User.created_at.desc()).paginate(page=page, per_page=per_page, error_out=False)
        users_items = pagination.items # Renamed to avoid conflict

        users_list = []
        for user_item in users_items: # Renamed to avoid conflict
            latest_subscription = Subscription.query.filter_by(user_id=user_item.id).order_by(Subscription.end_date.desc()).first()
            
            subscription_status = '无订阅'
            subscription_name = None
            end_date_iso = None
            if latest_subscription:
                # 使用subscription_type_id而不是plan
                plan_type_obj = SubscriptionType.query.get(latest_subscription.subscription_type_id)
                subscription_name = plan_type_obj.name if plan_type_obj else f"订阅类型ID: {latest_subscription.subscription_type_id}"
                end_date_iso = latest_subscription.end_date.isoformat() + 'Z'
                if latest_subscription.end_date > now:
                    subscription_status = '活跃'
                else:
                    subscription_status = '已过期'

            users_list.append({
                'id': user_item.id,
                'email': user_item.email,
                'is_admin': user_item.is_admin,
                'is_active': user_item.is_active,
                'created_at': user_item.created_at.isoformat() if user_item.created_at else None,
                'last_login': user_item.last_login.isoformat() if user_item.last_login else None,
                'subscription_name': subscription_name, 
                'subscription_status': subscription_status, 
                'subscription_end_date': end_date_iso,
                'remark': user_item.remark
            })

        return jsonify_response(success=True,
                                message='用户列表获取成功',
                                data={
                                    'users': users_list,
                                    'pagination': {
                                        'page': pagination.page,
                                        'per_page': pagination.per_page,
                                        'total_pages': pagination.pages,
                                        'total_items': pagination.total
                                    }
                                },
                                status_code=200)

    except Exception as e:
        logger.exception(f"[Admin] 获取用户列表时出错: {e}")
        return jsonify_response(success=False, message=f'获取用户列表时出错: {str(e)}', status_code=500) 

@admin_bp.route('/users/<int:user_id>', methods=['PUT'])
@admin_required
def admin_update_user(user_id):
    """管理员更新用户信息"""
    data = request.json
    if not data:
        return jsonify_response(success=False, message='缺少请求数据', data=None, status_code=400)

    user_to_update = User.query.get(user_id) # Renamed
    if not user_to_update:
        return jsonify_response(success=False, message='用户不存在', data=None, status_code=404)

    admin_user = g.user
    if admin_user.id == user_to_update.id and 'is_admin' in data and data['is_admin'] != user_to_update.is_admin:
         return jsonify_response(success=False, message='无法修改自己的管理员权限', data=None, status_code=403)

    updated_fields_summary = []

    if 'email' in data and data['email'] != user_to_update.email:
        existing_user_with_email = User.query.filter(User.email == data['email'], User.id != user_id).first() # Renamed
        if existing_user_with_email:
            return jsonify_response(success=False, message='邮箱已被使用', data=None, status_code=409)
        original_email = user_to_update.email
        user_to_update.email = data['email']
        updated_fields_summary.append(f"邮箱从 {original_email} 更改为 {user_to_update.email}")

    if 'is_admin' in data and isinstance(data['is_admin'], bool) and data['is_admin'] != user_to_update.is_admin:
        user_to_update.is_admin = data['is_admin']
        updated_fields_summary.append(f"管理员权限更改为 {user_to_update.is_admin}")
        
    if 'is_active' in data and isinstance(data['is_active'], bool) and data['is_active'] != user_to_update.is_active:
        user_to_update.is_active = data['is_active']
        updated_fields_summary.append(f"激活状态更改为 {user_to_update.is_active}")
        
    if 'is_email_verified' in data and isinstance(data['is_email_verified'], bool) and data['is_email_verified'] != user_to_update.is_email_verified:
        user_to_update.is_email_verified = data['is_email_verified']
        if user_to_update.is_email_verified:
            user_to_update.email_verified_at = datetime.utcnow()
        updated_fields_summary.append(f"邮箱验证状态更改为 {user_to_update.is_email_verified}")
        
    if 'new_password' in data and data['new_password']:
        # 简单的密码验证
        password = data['new_password']
        min_length = current_app.config.get('PASSWORD_MIN_LENGTH', 8)
        
        if len(password) < min_length:
            return jsonify_response(success=False, message=f"密码长度不能少于{min_length}位", status_code=400)
        
        # 检查是否需要包含数字
        if current_app.config.get('PASSWORD_REQUIRE_NUMBER', True):
            if not any(char.isdigit() for char in password):
                return jsonify_response(success=False, message="密码必须包含至少一个数字", status_code=400)
        
        # 检查是否需要包含特殊字符
        if current_app.config.get('PASSWORD_REQUIRE_SPECIAL_CHAR', False):
            special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            if not any(char in special_chars for char in password):
                return jsonify_response(success=False, message="密码必须包含至少一个特殊字符", status_code=400)
        
        user_to_update.set_password(data['new_password'])
        updated_fields_summary.append("密码已重置")
    
    if 'remark' in data:
        user_to_update.remark = data['remark']
        updated_fields_summary.append(f"备注已更新")

    if 'subscription_type_id' in data:
        new_subscription_type_id_str = data.get('subscription_type_id')
        new_subscription_type_id = None
        if new_subscription_type_id_str not in [None, '']:
            try:
                new_subscription_type_id = int(new_subscription_type_id_str)
            except ValueError:
                return jsonify_response(success=False, message='无效的订阅计划ID格式 (subscription_type_id)', data=None, status_code=400)

        current_active_subscription = Subscription.query.filter(
            Subscription.user_id == user_to_update.id,
            Subscription.end_date > datetime.utcnow()
        ).first()

        if new_subscription_type_id is None:
            if current_active_subscription:
                current_sub_type_id_for_log = current_active_subscription.subscription_type_id
                current_active_subscription.end_date = datetime.utcnow() - timedelta(seconds=1)
                updated_fields_summary.append(f"取消了订阅 (原类型ID: {current_sub_type_id_for_log})")
                db.session.add(current_active_subscription)
        else:
            selected_subscription_type = SubscriptionType.query.get(new_subscription_type_id)
            if not selected_subscription_type:
                return jsonify_response(success=False, message=f'无效的订阅计划ID: {new_subscription_type_id}', data=None, status_code=400)

            plan_changed = True
            if current_active_subscription:
                if current_active_subscription.subscription_type_id == new_subscription_type_id:
                    plan_changed = False 
                    updated_fields_summary.append(f"订阅计划 (类型ID: {new_subscription_type_id}) 未发生变化")
                else:
                    current_sub_type_id_for_log = current_active_subscription.subscription_type_id
                    updated_fields_summary.append(f"旧订阅 (类型ID: {current_sub_type_id_for_log}) 已终止")
                    current_active_subscription.end_date = datetime.utcnow() - timedelta(seconds=1)
                    db.session.add(current_active_subscription)
            
            if plan_changed:
                payment_id_for_admin = f"admin_assigned_{user_to_update.id}_{int(time.time())}"
                sub, msg = subscription_service.create_or_extend_subscription(
                    user_id=user_to_update.id, 
                    subscription_type_id=new_subscription_type_id, 
                    payment_id=payment_id_for_admin
                )
                if sub:
                    updated_fields_summary.append(f"设置了新订阅 (类型ID: {new_subscription_type_id})，有效期至 {sub.end_date.strftime('%Y-%m-%d')}")
                else:
                    logger.error(f"管理员分配订阅失败 (用户ID: {user_to_update.id}, 类型ID: {new_subscription_type_id}): {msg}")
                    return jsonify_response(success=False, message=f'分配新订阅失败: {msg}', data=None, status_code=500)

    if updated_fields_summary:
        try:
            db.session.commit()
            logger.info(f"管理员 {admin_user.email} 更新了用户 {user_to_update.email} 的信息: {'; '.join(updated_fields_summary)}")
            return jsonify_response(success=True, message='用户信息更新成功', data={'user': user_to_update.to_dict()}, status_code=200)
        except Exception as e_commit:
            db.session.rollback()
            logger.error(f"更新用户 {user_id} 信息时数据库出错: {e_commit}", exc_info=True)
            return jsonify_response(success=False, message=f'数据库错误: {str(e_commit)}', data=None, status_code=500)
    else:
        return jsonify_response(success=True, message='没有需要更新的信息', data={'user': user_to_update.to_dict()}, status_code=200) 

@admin_bp.route('/users/<int:user_id>', methods=['DELETE'])
@admin_required
def admin_delete_user(user_id):
    """管理员删除用户接口"""
    admin_user = g.user
    
    if admin_user.id == user_id:
        logger.warning(f"管理员 {admin_user.email} (ID: {admin_user.id}) 尝试删除自己的账户")
        return jsonify_response(success=False, message='无法删除自己的账户', data=None, status_code=403)

    user_to_delete = User.query.get(user_id)
    if not user_to_delete:
        logger.warning(f"管理员 {admin_user.email} 尝试删除不存在的用户 ID: {user_id}")
        return jsonify_response(success=False, message='用户不存在', data=None, status_code=404)

    try:
        logger.info(f"管理员 {admin_user.email} 开始删除用户 {user_to_delete.email} (ID: {user_id}) 及其关联数据")

        # 使用正确的删除顺序以避免外键约束冲突
        # 1. 先删除设备审计记录（包括作为用户和操作者的记录）
        device_audit_count = DeviceAudit.query.filter(
            or_(DeviceAudit.user_id == user_id, 
                DeviceAudit.operator_id == user_id)
        ).delete(synchronize_session='fetch')
        logger.debug(f"已删除用户 {user_id} 的 {device_audit_count} 条 DeviceAudit 记录")
        
        # 2. 删除设备记录
        device_count = Device.query.filter_by(user_id=user_id).delete(synchronize_session='fetch')
        logger.debug(f"已删除用户 {user_id} 的 {device_count} 条 Device 记录")
        
        # 3. 删除登录会话
        session_count = LoginSession.query.filter_by(user_id=user_id).delete(synchronize_session='fetch')
        logger.debug(f"已删除用户 {user_id} 的 {session_count} 条 LoginSession 记录")
        
        # 4. 删除订阅记录
        subscription_count = Subscription.query.filter_by(user_id=user_id).delete(synchronize_session='fetch')
        logger.debug(f"已删除用户 {user_id} 的 {subscription_count} 条 Subscription 记录")
        
        # 5. 删除支付记录
        payment_count = Payment.query.filter_by(user_id=user_id).delete(synchronize_session='fetch')
        logger.debug(f"已删除用户 {user_id} 的 {payment_count} 条 Payment 记录")
        
        payment_record_count = PaymentRecord.query.filter_by(user_id=user_id).delete(synchronize_session='fetch')
        logger.debug(f"已删除用户 {user_id} 的 {payment_record_count} 条 PaymentRecord 记录")
        
        # 6. 最后删除用户记录
        db.session.delete(user_to_delete)
        
        # 提交事务
        db.session.commit()
        
        logger.info(f"管理员 {admin_user.email} 成功删除用户 {user_to_delete.email} (ID: {user_id}) 及其所有关联数据")
        return jsonify_response(success=True, message='用户及其关联数据已成功删除', data=None, status_code=200)

    except IntegrityError as e:
        db.session.rollback()
        logger.exception(f"管理员 {admin_user.email} 删除用户 ID: {user_id} 时发生数据库完整性错误")
        return jsonify_response(success=False, message='删除失败：存在未处理的关联数据', data=None, status_code=409)
    except Exception as e:
        db.session.rollback()
        logger.exception(f"管理员 {admin_user.email} 删除用户 ID: {user_id} 时发生错误")
        return jsonify_response(success=False, message='删除用户时发生服务器内部错误', data=None, status_code=500) 

@admin_bp.route('/users/<int:user_id>/details', methods=['GET'])
@admin_required
def admin_get_user_details(user_id):
    """管理员获取用户详细信息接口，包括订阅、设备和支付记录"""
    try:
        user_detail = User.query.get_or_404(user_id) # Renamed
        
        subscriptions = Subscription.query.filter_by(user_id=user_id).order_by(Subscription.start_date.desc()).all()
        subscriptions_data = []
        for sub_item in subscriptions: # Renamed
            status = '已过期' if sub_item.end_date < datetime.utcnow() else '活跃'
            subscription_type_obj = SubscriptionType.query.get(sub_item.subscription_type_id)
            subscription_instance_obj = None
            instance_name = None
            if sub_item.subscription_instance_id:
                subscription_instance_obj = SubscriptionInstance.query.get(sub_item.subscription_instance_id)
                instance_name = subscription_instance_obj.name if subscription_instance_obj else None
            
            subscriptions_data.append({
                'id': sub_item.id,
                'subscription': sub_item.subscription_type_id,
                'subscription_name': subscription_type_obj.name if subscription_type_obj else str(sub_item.subscription_type_id),
                'subscription_instance_id': sub_item.subscription_instance_id,
                'instance_name': instance_name,
                'start_date': sub_item.start_date.isoformat() + 'Z',
                'end_date': sub_item.end_date.isoformat() + 'Z',
                'price': sub_item.price,
                'max_devices': sub_item.max_devices,
                'status': status,
                'payment_id': sub_item.payment_id,
                'created_at': sub_item.created_at.isoformat() + 'Z'
            })

        devices_items = Device.query.filter_by(user_id=user_id).all() # Renamed
        devices_data = [{
            'id': d.id,
            'name': d.device_name,
            'ip_address': d.device_ip,
            'type': d.device_type,
            'created_at': d.created_at.isoformat() + 'Z',
            'adspower_account_id': d.adspower_account_id,
            'account_username': d.adspower_account.username if d.adspower_account else 'N/A'
        } for d in devices_items]

        payments_items = Payment.query.filter_by(user_id=user_id).order_by(Payment.created_at.desc()).limit(20).all() # Renamed
        payments_data = [{
            'id': p.id,
            'order_id': p.order_id,
            'amount': p.amount,
            'status': p.status,
            'payment_method': p.payment_method,
            'created_at': p.created_at.isoformat() + 'Z',
            'paid_at': p.paid_at.isoformat() + 'Z' if p.paid_at else None,
            'subscription_type_id': getattr(p, 'subscription_type_id', None),
            'subscription_days': p.subscription_days
        } for p in payments_items]

        login_sessions_items = LoginSession.query.filter_by(user_id=user_id).order_by(LoginSession.login_time.desc()).limit(10).all() # Renamed
        sessions_data = [{
            'id': s.id,
            'login_time': s.login_time.isoformat() + 'Z' if s.login_time else None,
            'ip_address': s.ip_address,
            'user_agent': s.user_agent,
            'completed_time': s.completed_time.isoformat() + 'Z' if s.completed_time else None,
            'adspower_account_id': s.adspower_account_id,
            'account_username': s.adspower_account.username if s.adspower_account else 'N/A'
        } for s in login_sessions_items]

        user_data = user_detail.to_dict()

        return jsonify_response(success=True, message='用户详细信息获取成功', data={
            'user': user_data,
            'subscriptions': subscriptions_data,
            'devices': devices_data,
            'payments': payments_data,
            'login_sessions': sessions_data
        }, status_code=200)

    except NotFound:
        logger.warning(f"[管理员] 尝试获取不存在的用户详情: {user_id}")
        return jsonify_response(success=False, message='用户未找到', data=None, status_code=404)
    except Exception as e:
        logger.exception(f"[Admin] 获取用户 {user_id} 详细信息时出错: {e}")
        return jsonify_response(success=False, message=f'获取用户详情时出错: {str(e)}', data=None, status_code=500) 

@admin_bp.route('/users/<int:user_id>/assign-subscription', methods=['POST'])
@admin_required
def admin_assign_subscription(user_id):
    """管理员手动为用户分配订阅"""
    try:
        admin_user = g.user
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['subscription_type_id', 'subscription_instance_id', 'days']
        for field in required_fields:
            if field not in data:
                return jsonify_response(success=False, message=f'缺少必填字段: {field}', data=None, status_code=400)
        
        # 获取用户
        user = User.query.get_or_404(user_id)
        
        # 获取订阅类型
        subscription_type = SubscriptionType.query.get(data['subscription_type_id'])
        if not subscription_type:
            return jsonify_response(success=False, message='订阅类型不存在', data=None, status_code=404)
        
        # 获取订阅实例
        subscription_instance = SubscriptionInstance.query.get(data['subscription_instance_id'])
        if not subscription_instance:
            return jsonify_response(success=False, message='订阅实例不存在', data=None, status_code=404)
        
        # 验证订阅实例属于该订阅类型
        if subscription_instance.subscription_type_id != subscription_type.id:
            return jsonify_response(success=False, message='订阅实例与订阅类型不匹配', data=None, status_code=400)
        
        # 检查订阅实例容量
        available_slots = subscription_instance.get_available_slots()
        if available_slots <= 0:
            return jsonify_response(success=False, message='订阅实例已满，无法分配', data=None, status_code=400)
        
        # 终止该用户所有活跃的订阅
        active_subscriptions = Subscription.query.filter(
            Subscription.user_id == user_id,
            Subscription.end_date > datetime.utcnow()
        ).all()
        
        terminated_count = 0
        if active_subscriptions:
            for sub in active_subscriptions:
                old_end_date = sub.end_date
                sub.end_date = datetime.utcnow() - timedelta(seconds=1)
                db.session.add(sub)
                terminated_count += 1
                logger.info(f"[管理员分配订阅] 终止旧订阅 - 用户ID: {user_id}, 订阅ID: {sub.id}, 类型ID: {sub.subscription_type_id}, 原结束时间: {old_end_date}")
            
            # 立即flush，确保旧订阅已被终止
            db.session.flush()
            logger.info(f"[管理员分配订阅] 共终止了用户 {user.email} 的 {terminated_count} 个活跃订阅")
        
        # 创建订阅记录
        start_date = datetime.utcnow()
        end_date = start_date + timedelta(days=int(data['days']))
        
        subscription = Subscription(
            user_id=user_id,
            subscription_type_id=subscription_type.id,
            subscription_instance_id=subscription_instance.id,
            start_date=start_date,
            end_date=end_date,
            max_devices=subscription_type.max_devices,
            payment_id=None  # 手动分配的订阅没有支付记录
        )
        
        db.session.add(subscription)
        db.session.commit()
        
        # 记录备注（如果有）
        remark = data.get('remark', '')
        log_message = f"管理员 {admin_user.email} 为用户 {user.email} (ID: {user_id}) 分配订阅"
        if terminated_count > 0:
            log_message += f"（已终止 {terminated_count} 个旧订阅）"
        if remark:
            log_message += f"，备注: {remark}"
        logger.info(log_message)
        
        message = '订阅分配成功'
        if terminated_count > 0:
            message += f'（已终止 {terminated_count} 个旧订阅）'
        
        return jsonify_response(
            success=True, 
            message=message, 
            data={
                'subscription_id': subscription.id,
                'user_email': user.email,
                'subscription_type': subscription_type.name,
                'subscription_instance': subscription_instance.name,
                'start_date': start_date.isoformat() + 'Z',
                'end_date': end_date.isoformat() + 'Z',
                'terminated_subscriptions_count': terminated_count
            }, 
            status_code=201
        )
        
    except NotFound:
        return jsonify_response(success=False, message='用户不存在', data=None, status_code=404)
    except Exception as e:
        db.session.rollback()
        logger.exception(f"管理员分配订阅时发生错误: {e}")
        return jsonify_response(success=False, message=f'分配订阅失败: {str(e)}', data=None, status_code=500)

@admin_bp.route('/accounts/adspower', methods=['GET'])
@admin_required
def admin_get_adspower_accounts():
    """[管理员API] 获取所有ADSpower账号"""
    try:
        admin_user = g.user
        logger.info(f"[管理员] 管理员 {admin_user.id} ({admin_user.email}) 获取AdsPower账号列表")
        
        # 使用账号信息聚合服务
        from ..services import AdsPowerAccountInfoService
        
        accounts_items = AdspowerAccount.query.all() # Renamed
        result = []
        
        for acc_item in accounts_items: # Renamed
            # 使用聚合服务获取账号完整信息
            account_info = AdsPowerAccountInfoService.get_account_full_info(acc_item.id)
            if not account_info:
                logger.warning(f"无法获取账号 {acc_item.id} 的信息")
                continue
            
            # 获取关联的订阅实例信息（多对多关系）
            subscription_instances_info = []
            for instance in acc_item.subscription_instances:
                subscription_instances_info.append({
                    'id': instance.id,
                    'name': instance.name,
                    'is_active': instance.is_active
                })
            
            # 构造返回数据
            runtime_status = account_info.get('runtime_status', {})
            result.append({
                "id": account_info['id'],
                "username": account_info['username'],
                "current_devices": account_info['current_devices'],
                "max_devices": account_info['max_devices'],
                "is_active": account_info['is_active'],
                "subscription_instances": subscription_instances_info,  # 改为复数，返回数组
                "created_at": account_info['created_at'],
                # 状态信息
                "health_status": account_info['health_status'],
                "health_message": account_info['health_message'],
                "last_check_ago": '刚刚' if runtime_status.get('managed') else '未管理',
                "webdriver_status": {
                    'total_instances': runtime_status.get('total_instances', 0),
                    'ready_count': runtime_status.get('ready_count', 0),
                    'in_use_count': runtime_status.get('in_use_count', 0),
                    'unhealthy_count': runtime_status.get('unhealthy_count', 0)
                },
                "error_count": runtime_status.get('unhealthy_count', 0),
                "last_error": None
            })
        
        logger.info(f"[管理员] 获取到 {len(result)} 个AdsPower账号")
        
        return jsonify_response(success=True, message="AdsPower账号列表获取成功", data={"accounts": result}, status_code=200)
        
    except Exception as e:
        logger.exception(f"[管理员] 获取AdsPower账号列表时出错: {str(e)}")
        return jsonify_response(success=False, message=f"服务器内部错误: {str(e)}", data=None, status_code=500) 

@admin_bp.route('/accounts/adspower', methods=['POST'])
@admin_required
def admin_add_adspower_account():
    """[管理员API] 添加ADSpower账号"""
    try:
        admin_user = g.user
        data = request.json
        
        logger.info(f"[管理员] 管理员 {admin_user.id} 正在添加新的AdsPower账号: {data.get('username')}")
        
        if not data.get('username') or not data.get('password'):
            logger.warning(f"[管理员] 添加AdsPower账号请求缺少必填字段: username={bool(data.get('username'))}, password={bool(data.get('password'))}")
            return jsonify_response(success=False, message="用户名和密码为必填项", data=None, status_code=400)
        
        if AdspowerAccount.query.filter_by(username=data['username']).first():
            logger.warning(f"[管理员] 尝试添加的AdsPower账号已存在: {data['username']}")
            return jsonify_response(success=False, message="账号已存在", data=None, status_code=400)
            
        cookies_str = None
        if 'cookies' in data:
            cookies_value = data['cookies']
            if isinstance(cookies_value, str):
                if cookies_value == "":
                    cookies_str = ""
                else:
                    try:
                        json.loads(cookies_value)
                        cookies_str = cookies_value
                    except json.JSONDecodeError:
                        logger.error(f"[管理员] 添加账号 {data['username']} 时提供的 Cookies 不是有效的 JSON 字符串", exc_info=True)
                        return jsonify_response(success=False, message="提供的 Cookie 数据不是有效的 JSON 格式", data=None, status_code=400)
                    except Exception as parse_err:
                         logger.error(f"[管理员] 添加账号 {data['username']} 时解析 Cookies 字符串出错: {parse_err}", exc_info=True)
                         return jsonify_response(success=False, message=f"处理 Cookie 字符串时出错: {parse_err}", data=None, status_code=400)
            elif isinstance(cookies_value, (list, dict)):
                try:
                    cookies_str = json.dumps(cookies_value)
                except Exception as json_err:
                    logger.error(f"[管理员] 添加账号 {data['username']} 时序列化 Cookies 出错: {json_err}", exc_info=True)
                    return jsonify_response(success=False, message=f"处理 Cookie 数据时出错: {json_err}", data=None, status_code=400)
            else:
                logger.warning(f"[管理员] 添加账号 {data['username']} 时收到非预期的 Cookies 类型: {type(cookies_value)}，将忽略 Cookies")
        
        new_account = AdspowerAccount( # Renamed
            username=data['username'],
            password=data['password'],
            totp_secret=data.get('totp_secret'),
            max_devices=data.get('max_devices', 10),
            # subscription_instance_id已移除，使用多对多关系
            cookies=cookies_str,
            description=data.get('remarks'),  # 使用description字段
            is_active=True,
            created_at=datetime.utcnow()
        )
        
        db.session.add(new_account)
        db.session.commit()
        
        # 如果指定了订阅实例ID，建立关联
        if data.get('subscription_instance_id'):
            instance = SubscriptionInstance.query.get(data['subscription_instance_id'])
            if instance:
                instance.adspower_accounts.append(new_account)
                db.session.commit()
                logger.info(f"[管理员] 已将账号 {new_account.id} 关联到订阅实例 {instance.id}")
        
        logger.info(f"[管理员] 管理员 {admin_user.id} 成功创建AdsPower账号: {new_account.username}, ID: {new_account.id}")
        
        try:
            manager = get_account_driver_manager()
            if manager:
                logger.info(f"[管理员] 准备通知 AccountWebDriverManager 添加新账号 ID: {new_account.id}")
                manager.add_managed_account(
                    account_id=str(new_account.id),
                    username=new_account.username,
                    password=new_account.password,
                    totp_secret=new_account.totp_secret,
                    cookies=new_account.cookies
                )
                logger.info(f"[管理员] AccountWebDriverManager 已接收账号 {new_account.id} 的添加请求")
            else:
                logger.warning(f"[管理员] AccountWebDriverManager 未运行，无法添加新账号 {new_account.id} 进行管理")
        except Exception as e_manager_add:
            logger.error(f"[管理员] 通知 AccountWebDriverManager 添加新账号 {new_account.id} 时出错: {e_manager_add}", exc_info=True)

        return jsonify_response(success=True, message="ADSpower账号创建成功", data={
            "account": {
                "id": new_account.id,
                "username": new_account.username,
                "is_active": new_account.is_active,
                "max_devices": new_account.max_devices,
                # subscription_type字段已移除，通过subscription_instance关联
                "description": new_account.description,
                "created_at": new_account.created_at.isoformat()
            }
        }, status_code=201)
        
    except Exception as e:
        logger.exception(f"[管理员] 创建AdsPower账号时出错: {str(e)}")
        db.session.rollback()
        return jsonify_response(success=False, message=f"服务器内部错误: {str(e)}", data=None, status_code=500) 

@admin_bp.route('/accounts/adspower/<int:account_id>/subscription-instances', methods=['GET'])
@admin_required
def admin_get_account_subscription_instances(account_id):
    """获取AdsPower账号关联的所有订阅实例"""
    try:
        account = AdspowerAccount.query.get(account_id)
        if not account:
            return jsonify_response(success=False, message="账号不存在", status_code=404)
        
        instances = []
        for instance in account.subscription_instances:
            instances.append({
                'id': instance.id,
                'name': instance.name,
                'capacity': instance.capacity,
                'is_active': instance.is_active,
                'active_users_count': instance.get_active_users_count(),
                'available_slots': instance.get_available_slots()
            })
        
        return jsonify_response(success=True, 
                              message="获取成功", 
                              data={'account_id': account_id, 'instances': instances})
        
    except Exception as e:
        logger.error(f"获取账号订阅实例时出错: {str(e)}", exc_info=True)
        return jsonify_response(success=False, message=f"获取失败: {str(e)}", status_code=500)

@admin_bp.route('/accounts/adspower/<int:account_id>/subscription-instances', methods=['POST'])
@admin_required  
def admin_add_account_to_subscription_instance(account_id):
    """将AdsPower账号添加到订阅实例"""
    try:
        account = AdspowerAccount.query.get(account_id)
        if not account:
            return jsonify_response(success=False, message="账号不存在", status_code=404)
        
        data = request.get_json()
        instance_id = data.get('instance_id')
        if not instance_id:
            return jsonify_response(success=False, message="缺少instance_id参数", status_code=400)
        
        instance = SubscriptionInstance.query.get(instance_id)
        if not instance:
            return jsonify_response(success=False, message="订阅实例不存在", status_code=404)
        
        # 检查是否已关联
        if account in instance.adspower_accounts:
            return jsonify_response(success=False, message="账号已关联到此订阅实例", status_code=400)
        
        # 建立关联
        instance.adspower_accounts.append(account)
        db.session.commit()
        
        logger.info(f"[管理员] 将账号 {account.username} (ID: {account_id}) 添加到订阅实例 {instance.name} (ID: {instance_id})")
        
        return jsonify_response(success=True, 
                              message=f"账号已成功关联到订阅实例 {instance.name}")
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"添加账号到订阅实例时出错: {str(e)}", exc_info=True)
        return jsonify_response(success=False, message=f"操作失败: {str(e)}", status_code=500)

@admin_bp.route('/accounts/adspower/<int:account_id>/subscription-instances/<int:instance_id>', methods=['DELETE'])
@admin_required
def admin_remove_account_from_subscription_instance(account_id, instance_id):
    """从订阅实例中移除AdsPower账号（从AdsPower账号管理页面）"""
    try:
        account = AdspowerAccount.query.get(account_id)
        if not account:
            return jsonify_response(success=False, message="账号不存在", status_code=404)
        
        instance = SubscriptionInstance.query.get(instance_id)
        if not instance:
            return jsonify_response(success=False, message="订阅实例不存在", status_code=404)
        
        # 检查是否已关联
        if account not in instance.adspower_accounts:
            return jsonify_response(success=False, message="账号未关联到此订阅实例", status_code=400)
        
        # 使用统一的辅助函数处理设备下线和关联移除
        success, message, logout_details = logout_devices_and_remove_association(instance, account, force_logout=True)
        
        if success:
            logger.info(f"[管理员] 从订阅实例 {instance.name} (ID: {instance_id}) 中移除账号 {account.username} (ID: {account_id})")
            
            return jsonify_response(
                success=True, 
                message=message,
                data={
                    'logout_details': logout_details,
                    'devices_logged_out': len([d for d in logout_details if d['success']]),
                    'instance_name': instance.name
                }
            )
        else:
            return jsonify_response(
                success=False, 
                message=message,
                data={
                    'logout_details': logout_details
                },
                status_code=400
            )
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"从订阅实例移除账号时出错: {str(e)}", exc_info=True)
        return jsonify_response(success=False, message=f"操作失败: {str(e)}", status_code=500)

@admin_bp.route('/accounts/adspower/<int:account_id>', methods=['DELETE'])
@admin_required
def admin_delete_adspower_account(account_id):
    """管理员删除一个ADSpower账号（放弃托管）"""
    # 导入必要的服务
    from adspower_manager.services.device_audit_service import DeviceAuditService
    
    account_to_delete = AdspowerAccount.query.get(account_id)
    if not account_to_delete:
        return jsonify_response(success=False, message="账号未找到", data=None, status_code=404)

    username_deleted = account_to_delete.username
    
    # 获取关联的设备
    associated_devices = Device.query.filter_by(adspower_account_id=account_id).all()
    associated_devices_count = len(associated_devices)
    
    # 获取关联的订阅实例（多对多关系）
    associated_instances = list(account_to_delete.subscription_instances)
    associated_instances_count = len(associated_instances)
    
    # 记录关联信息用于日志
    logger.info(f"[管理员] 准备删除账号 {username_deleted} (ID: {account_id})，"
                f"关联设备: {associated_devices_count} 个，关联订阅实例: {associated_instances_count} 个")
    
    # 尝试退出所有设备（失败也不影响删除）
    logout_results = []
    if associated_devices_count > 0:
        logger.info(f"[管理员] 尝试退出 {associated_devices_count} 个设备")
        adspower_api = get_adspower_api()
        
        for device in associated_devices:
            try:
                # 尝试下线设备，使用device_id
                if device.device_id:
                    logout_success, message = adspower_api.logout_device(
                        account_to_delete,
                        device.device_id
                    )
                else:
                    logout_success = False
                    message = "设备缺少唯一标识符"
                
                logout_results.append({
                    'device_id': device.device_id,
                    'device_name': device.device_name,
                    'success': logout_success,
                    'message': message
                })
                
                if logout_success:
                    logger.info(f"[管理员] 成功下线设备 {device.device_name}")
                else:
                    logger.warning(f"[管理员] 下线设备失败 {device.device_name}: {message}")
                    
                # 记录审计日志
                DeviceAuditService.log_device_action(
                    device_id=device.id,
                    user_id=device.user_id,
                    action=DeviceAuditService.ACTION_DELETE,
                    action_source='admin',
                    description=f"删除AdsPower账号时{'成功' if logout_success else '尝试'}下线设备",
                    device_snapshot={
                        'device_id': device.device_id,
                        'device_name': device.device_name,
                        'device_type': device.device_type,
                        'adspower_account_id': device.adspower_account_id,
                        'adspower_username': username_deleted
                    }
                )
                    
            except Exception as e:
                logger.error(f"[管理员] 下线设备时发生错误 {device.device_name}: {str(e)}")
                logout_results.append({
                    'device_id': device.device_id,
                    'device_name': device.device_name,
                    'success': False,
                    'message': str(e)
                })
    
    # 协议模式下不需要通知WebDriver管理器（默认未启用）

    # 执行删除操作
    try:
        # 删除账号前先手动删除设备记录（避免外键约束问题）
        for device in associated_devices:
            db.session.delete(device)
        
        # 删除账号（多对多关系会自动清理）
        db.session.delete(account_to_delete)
        db.session.commit()
        
        admin_user_id_for_log = g.user.id if hasattr(g, 'user') and g.user else '未知'
        logger.info(f"[管理员] 管理员 {admin_user_id_for_log} 成功删除AdsPower账号: {username_deleted} (ID: {account_id})")
        
        # 构建返回消息
        success_logout_count = len([r for r in logout_results if r['success']])
        failed_logout_count = len([r for r in logout_results if not r['success']])
        
        message = f"AdsPower账号已删除。"
        if associated_devices_count > 0:
            if failed_logout_count > 0:
                message += f"尝试下线 {associated_devices_count} 个设备，成功 {success_logout_count} 个，失败 {failed_logout_count} 个。"
            else:
                message += f"成功下线所有 {associated_devices_count} 个设备。"
        if associated_instances_count > 0:
            message += f"已解除 {associated_instances_count} 个订阅实例关联。"
        
        return jsonify_response(
            success=True, 
            message=message,
            data={
                'logout_results': logout_results,
                'devices_removed': associated_devices_count,
                'instances_unlinked': associated_instances_count,
                'logout_summary': {
                    'total': associated_devices_count,
                    'success': success_logout_count,
                    'failed': failed_logout_count
                }
            },
            status_code=200
        )
    except Exception as e_db_delete:
        db.session.rollback()
        logger.error(f"[管理员] 删除AdsPower账号 {account_id} 时数据库出错: {e_db_delete}", exc_info=True)
        return jsonify_response(success=False, message=f"删除AdsPower账号时发生内部错误: {str(e_db_delete)}", data=None, status_code=500) 

@admin_bp.route('/accounts/adspower/<int:account_id>/cookies', methods=['PUT'])
@admin_required
def admin_update_adspower_account_cookies(account_id):
    """管理员更新ADSpower账号的cookies"""
    data = request.json
    if not data or 'cookies' not in data:
        return jsonify_response(success=False, message="请求中缺少 'cookies'", data=None, status_code=400)

    account = AdspowerAccount.query.get(account_id)
    if not account:
        return jsonify_response(success=False, message="账号未找到", data=None, status_code=404)

    new_cookies = data['cookies']
    if not isinstance(new_cookies, (str, list, dict)):
        return jsonify_response(success=False, message="'cookies' 格式无效，应为字符串、列表或字典", data=None, status_code=400)

    try:
        # 根据 new_cookies 的类型进行处理
        if isinstance(new_cookies, str):
            try:
                # 尝试解析 JSON 字符串
                parsed_cookies = json.loads(new_cookies)
                if not isinstance(parsed_cookies, (list, dict)):
                    raise ValueError("Cookies JSON 字符串解析后不是列表或字典")
                account.set_cookies(parsed_cookies)
            except json.JSONDecodeError:
                # 如果不是有效的 JSON 字符串，则直接将其作为普通字符串处理（如果业务逻辑允许）
                # 或者返回错误，具体取决于需求。此处假设可能是一个简单字符串cookie
                # logger.warning(f"[管理员] 账号 {account_id} 的cookies字符串不是有效的JSON，将尝试直接使用。")
                # account.set_cookies(new_cookies) # 假设 AdspowerAccount.set_cookies 可以处理str
                return jsonify_response(success=False, message="Cookies 字符串不是有效的JSON格式", data=None, status_code=400)
            except ValueError as ve:
                 return jsonify_response(success=False, message=str(ve), data=None, status_code=400)
        else: # list or dict
            account.set_cookies(new_cookies)

        account.last_cookie_update_at = datetime.utcnow()
        db.session.commit()
        logger.info(f"[管理员] 管理员 {g.user.id} 成功更新了AdsPower账号 {account.username} (ID: {account_id}) 的cookies")

        # 通知 AccountWebDriverManager 更新 cookies
        try:
            manager = get_account_driver_manager()
            if manager:
                logger.info(f"[管理员] 准备通知 AccountWebDriverManager 更新账号 ID: {account_id} 的 cookies")
                manager.update_account_cookies(str(account_id), account.get_cookies())
                logger.info(f"[管理员] AccountWebDriverManager 已处理账号 {account_id} 的 cookies 更新请求")
            else:
                logger.warning(f"[管理员] AccountWebDriverManager 未运行，无法通知其更新账号 {account_id} 的 cookies")
        except Exception as e_manager_update:
            logger.error(f"[管理员] 通知 AccountWebDriverManager 更新账号 {account_id} 的 cookies 时出错: {e_manager_update}", exc_info=True)
            # 不因此次失败而回滚主操作，但记录错误

        return jsonify_response(success=True, message="Cookies更新成功", data=account.to_dict(), status_code=200)
    except ValueError as ve:
        db.session.rollback()
        logger.warning(f"[管理员] 更新账号 {account_id} cookies时值错误: {str(ve)}")
        return jsonify_response(success=False, message=str(ve), data=None, status_code=400)
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 更新AdsPower账号 {account_id} cookies时发生内部错误: {e}", exc_info=True)
        return jsonify_response(success=False, message=f"更新cookies时发生内部错误: {str(e)}", data=None, status_code=500) 

@admin_bp.route('/payments', methods=['GET'])
@admin_required
def admin_get_payments():
    """管理员获取所有支付记录，支持分页和筛选"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    user_id_filter = request.args.get('user_id', type=int)
    status_filter = request.args.get('status')
    payment_method_filter = request.args.get('payment_method')
    order_id_filter = request.args.get('order_id')
    user_email_filter = request.args.get('user_email')

    query = Payment.query.join(User).options(contains_eager(Payment.user))

    if user_id_filter:
        query = query.filter(Payment.user_id == user_id_filter)
    if status_filter:
        query = query.filter(Payment.status == status_filter)
    if payment_method_filter:
        query = query.filter(Payment.payment_method == payment_method_filter)
    if order_id_filter:
        query = query.filter(Payment.order_id.ilike(f"%{order_id_filter}%"))
    if user_email_filter:
        query = query.filter(User.email.ilike(f"%{user_email_filter}%"))

    query = query.order_by(Payment.created_at.desc())
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    payments = pagination.items

    payments_data = []
    for payment in payments:
        payment_dict = payment.to_dict()
        payment_dict['user_email'] = payment.user.email if payment.user else 'N/A'
        
        # 获取订阅类型名称
        if payment.subscription_type_id:
            subscription_type = SubscriptionType.query.get(payment.subscription_type_id)
            payment_dict['subscription_type_name'] = subscription_type.name if subscription_type else None
        else:
            payment_dict['subscription_type_name'] = None
            
        payments_data.append(payment_dict)
    
    logger.info(f"[管理员] 管理员 {g.user.id} 查询了支付记录：第{page}页，每页{per_page}条，筛选条件：user_id={user_id_filter}, status={status_filter}, payment_method={payment_method_filter}, order_id={order_id_filter}")

    return jsonify_response(success=True, data={
        'payments': payments_data,
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': pagination.page,
        'per_page': pagination.per_page,
        'has_next': pagination.has_next,
        'has_prev': pagination.has_prev
    }, status_code=200)

@admin_bp.route('/payments/<int:payment_id>', methods=['GET'])
@admin_required
def admin_get_payment_detail(payment_id):
    """管理员获取单个支付记录的详细信息"""
    payment = Payment.query.options(
        joinedload(Payment.user)
    ).get(payment_id)

    if not payment:
        logger.warning(f"[管理员] 管理员 {g.user.id} 查询支付详情失败，支付记录 {payment_id} 未找到")
        return jsonify_response(success=False, message="支付记录未找到", status_code=404)

    payment_data = payment.to_dict()
    if payment.user:
        payment_data['user_email'] = payment.user.email
        payment_data['user_id'] = payment.user.id
    else:
        payment_data['user_email'] = 'N/A'
        payment_data['user_id'] = None

    # 获取关联的订阅信息
    if payment.subscription:
        payment_data['subscription_id'] = payment.subscription.id
        payment_data['subscription_status'] = '有效' if not payment.subscription.is_expired() else '已过期'
        payment_data['subscription_start_date'] = payment.subscription.start_date.isoformat() if payment.subscription.start_date else None
        payment_data['subscription_end_date'] = payment.subscription.end_date.isoformat() if payment.subscription.end_date else None
        payment_data['subscription_max_devices'] = payment.subscription.max_devices  # 购买时确定的设备数（快照）
        payment_data['subscription_remark'] = payment.subscription.remark
        
        # 获取订阅实例信息
        if payment.subscription.subscription_instance:
            payment_data['subscription_instance_name'] = payment.subscription.subscription_instance.name
    
    # 获取订阅类型信息
    if payment.subscription_type_id:
        subscription_type = SubscriptionType.query.get(payment.subscription_type_id)
        if subscription_type:
            payment_data['subscription_type_name'] = subscription_type.name
            payment_data['subscription_type_price'] = subscription_type.price
    else:
        payment_data['subscription_type_name'] = None
        payment_data['subscription_type_price'] = None
        
    logger.info(f"[管理员] 管理员 {g.user.id} 成功查询了支付记录 {payment_id} 的详情")
    return jsonify_response(success=True, data=payment_data, status_code=200)

@admin_bp.route('/payments/<int:payment_id>/status', methods=['PUT'])
@admin_required
def admin_update_payment_status(payment_id):
    """管理员更新支付记录的状态，例如将 'pending' 更新为 'completed' 或 'failed'"""
    data = request.json
    new_status = data.get('status')

    if not new_status:
        return jsonify_response(success=False, message="请求中缺少 'status'", status_code=400)

    valid_statuses = ['pending', 'paid', 'failed', 'cancelled', 'refunded', 'error']
    if new_status not in valid_statuses:
        return jsonify_response(success=False, message=f"无效的状态值，允许的状态为: {', '.join(valid_statuses)}", status_code=400)

    payment = Payment.query.get(payment_id)
    if not payment:
        logger.warning(f"[管理员] 管理员 {g.user.id} 更新支付状态失败，支付记录 {payment_id} 未找到")
        return jsonify_response(success=False, message="支付记录未找到", status_code=404)

    original_status = payment.status
    if original_status == new_status:
        return jsonify_response(success=True, message="状态未改变", data=payment.to_dict(), status_code=200)

    try:
        # 特殊处理：如果要将状态更新为已支付，调用核心支付处理逻辑
        if new_status == 'paid' and original_status != 'paid':
            # 使用 PaymentService 的核心处理逻辑
            from adspower_manager.services.payment_service import PaymentService
            payment_service = PaymentService()
            
            # 调用核心支付成功处理逻辑（会自动创建/延长订阅）
            success, message = payment_service.process_payment_success(payment.payment_id)
            
            if success:
                logger.info(f"[管理员] 管理员 {g.user.id} 手动确认支付 {payment_id} 成功，已创建/延长订阅")
                return jsonify_response(success=True, message="支付确认成功，已创建/延长订阅", data=payment.to_dict(), status_code=200)
            else:
                logger.error(f"[管理员] 管理员 {g.user.id} 手动确认支付 {payment_id} 时处理失败: {message}")
                return jsonify_response(success=False, message=f"支付确认失败: {message}", status_code=500)
        else:
            # 其他状态更新（如取消、退款等）仅更新状态
            payment.status = new_status
            db.session.commit()
            logger.info(f"[管理员] 管理员 {g.user.id} 成功将支付记录 {payment_id} 的状态从 '{original_status}' 更新为 '{new_status}'")
            return jsonify_response(success=True, message="支付状态更新成功", data=payment.to_dict(), status_code=200)
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 更新支付记录 {payment_id} 状态时发生内部错误: {e}", exc_info=True)
        return jsonify_response(success=False, message=f"更新支付状态时发生内部错误: {str(e)}", status_code=500)

@admin_bp.route('/payments/statistics', methods=['GET'])
@admin_required
def admin_get_payment_statistics():
    """获取支付统计数据"""
    # 获取时间范围参数
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # 构建查询
    query = Payment.query
    
    if start_date:
        try:
            start_datetime = datetime.fromisoformat(start_date)
            query = query.filter(Payment.created_at >= start_datetime)
        except ValueError:
            return jsonify_response(success=False, message="开始日期格式错误", status_code=400)
    
    if end_date:
        try:
            end_datetime = datetime.fromisoformat(end_date)
            # 如果只提供日期，设置为当天结束时间
            if len(end_date) == 10:  # YYYY-MM-DD format
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
            query = query.filter(Payment.created_at <= end_datetime)
        except ValueError:
            return jsonify_response(success=False, message="结束日期格式错误", status_code=400)
    
    # 统计各状态订单数量
    status_stats = db.session.query(
        Payment.status,
        func.count(Payment.id).label('count'),
        func.sum(Payment.amount).label('total_amount')
    ).filter(query.whereclause if query.whereclause is not None else True)\
     .group_by(Payment.status).all()
    
    # 统计支付方式
    payment_method_stats = db.session.query(
        Payment.payment_method,
        func.count(Payment.id).label('count'),
        func.sum(Payment.amount).label('total_amount')
    ).filter(
        query.whereclause if query.whereclause is not None else True,
        Payment.status == 'paid'
    ).group_by(Payment.payment_method).all()
    
    # 计算总计
    total_orders = sum(stat.count for stat in status_stats)
    total_amount = sum(stat.total_amount or 0 for stat in status_stats)
    paid_amount = sum(stat.total_amount or 0 for stat in status_stats if stat.status == 'paid')
    
    # 构建返回数据
    statistics = {
        'summary': {
            'total_orders': total_orders,
            'total_amount': float(total_amount),
            'paid_amount': float(paid_amount),
            'paid_orders': next((stat.count for stat in status_stats if stat.status == 'paid'), 0),
            'pending_orders': next((stat.count for stat in status_stats if stat.status == 'pending'), 0),
            'failed_orders': next((stat.count for stat in status_stats if stat.status in ['failed', 'error', 'cancelled']), 0)
        },
        'status_breakdown': [
            {
                'status': stat.status,
                'count': stat.count,
                'amount': float(stat.total_amount or 0)
            } for stat in status_stats
        ],
        'payment_method_breakdown': [
            {
                'method': stat.payment_method or '未知',
                'count': stat.count,
                'amount': float(stat.total_amount or 0)
            } for stat in payment_method_stats
        ]
    }
    
    logger.info(f"[管理员] 管理员 {g.user.id} 查询了支付统计数据")
    return jsonify_response(success=True, data=statistics, status_code=200)

@admin_bp.route('/subscriptions', methods=['GET'])
@admin_required
def admin_get_subscriptions():
    """管理员获取所有订阅记录 (不再分页)"""
    # page = request.args.get('page', 1, type=int) # Removed pagination
    # per_page = request.args.get('per_page', 20, type=int) # Removed pagination
    user_id_filter = request.args.get('user_id', type=int)
    plan_id_filter = request.args.get('plan_id', type=int) 
    status_filter = request.args.get('status')
    
    query = Subscription.query.join(User, Subscription.user_id == User.id)\
                              .join(SubscriptionType, Subscription.subscription_type_id == SubscriptionType.id)\
                              .outerjoin(SubscriptionInstance, Subscription.subscription_instance_id == SubscriptionInstance.id)\
                              .options(
                                  contains_eager(Subscription.user),
                                  contains_eager(Subscription.subscription_type_details),
                                  contains_eager(Subscription.subscription_instance)
                              )

    if user_id_filter:
        query = query.filter(Subscription.user_id == user_id_filter)
    if plan_id_filter:
        query = query.filter(Subscription.subscription_type_id == plan_id_filter)
    
    # 状态过滤逻辑 (如果需要，可以根据明确的业务需求添加)
    # if status_filter:
    #     if status_filter == 'active':
    #         query = query.filter(Subscription.end_date > datetime.utcnow())
    #     elif status_filter == 'expired':
    #         query = query.filter(Subscription.end_date <= datetime.utcnow())
        # Add other status conditions as needed

    query = query.order_by(Subscription.created_at.desc())
    subscriptions = query.all() # Fetch all records instead of paginating

    subscriptions_data = []
    for sub in subscriptions:
        sub_dict = sub.to_dict() 
        sub_dict['user_email'] = sub.user.email if sub.user else 'N/A'
        sub_dict['subscription_name'] = sub.subscription_type_details.name if sub.subscription_type_details else 'N/A'
        
        # 添加订阅实例信息
        if sub.subscription_instance:
            sub_dict['subscription_instance_name'] = sub.subscription_instance.name
            sub_dict['subscription_instance_id'] = sub.subscription_instance.id
        else:
            sub_dict['subscription_instance_name'] = None
            sub_dict['subscription_instance_id'] = None
        
        if sub.is_expired():
            sub_dict['status'] = "已过期"
            sub_dict['status_display'] = "已过期"
        else:
            sub_dict['status'] = "活跃"
            sub_dict['status_display'] = "活跃"

        subscriptions_data.append(sub_dict)
        
    logger.info(f"[管理员] 管理员 {g.user.id} 查询了所有订阅记录，筛选条件：user_id={user_id_filter}, plan_id(type_id)={plan_id_filter}, status={status_filter}")

    return jsonify_response(success=True, data={
        'subscriptions': subscriptions_data
        # Removed pagination fields: total, pages, current_page, per_page, has_next, has_prev
    }, status_code=200)

@admin_bp.route('/subscriptions/<int:subscription_id>', methods=['GET'])
@admin_required
def admin_get_subscription_detail(subscription_id):
    """管理员获取单个订阅记录的详细信息"""
    subscription = Subscription.query.options(
        joinedload(Subscription.user),
        joinedload(Subscription.subscription_type_details), # Changed from Subscription.plan
        joinedload(Subscription.payment).raiseload('*') # Corrected to Subscription.payment, assuming 'payments' was a typo for the single payment relationship
    ).get(subscription_id)

    if not subscription:
        logger.warning(f"[管理员] 管理员 {g.user.id} 查询订阅详情失败，订阅 {subscription_id} 未找到")
        return jsonify_response(success=False, message="订阅未找到", status_code=404)

    sub_data = subscription.to_dict()
    if subscription.user:
        sub_data['user_email'] = subscription.user.email
    
    # 计算订阅状态
    if subscription.is_expired():
        sub_data['status'] = 'expired'
        sub_data['status_display'] = '已过期'
    else:
        sub_data['status'] = 'active'
        sub_data['status_display'] = '活跃'
    
    # Use subscription_type_details for plan information
    if subscription.subscription_type_details:
        sub_data['plan_name'] = subscription.subscription_type_details.name
        # Assuming SubscriptionType has a to_dict() method or you want specific fields
        # For now, let's add a few common fields for plan_details
        sub_data['plan_details'] = {
            'id': subscription.subscription_type_details.id,
            'name': subscription.subscription_type_details.name,
            'price': subscription.subscription_type_details.price,
            'days': subscription.subscription_type_details.days,
            'max_devices': subscription.subscription_type_details.max_devices
        }
    else:
        sub_data['plan_name'] = 'N/A'
        sub_data['plan_details'] = None
    
    # Handle payment information (assuming a single payment is linked, or you want the latest)
    # The original code had joinedload(Subscription.payments) but Subscription model has 'payment' (singular)
    # that links to the Payment table. If there can be multiple payments for one subscription record,
    # the model relationship needs to be 'payments' (plural) and a list.
    # For now, using the existing 'payment' relationship.
    if subscription.payment: # Access the single related payment object
        sub_data['payment_id'] = subscription.payment.id # This was already in to_dict usually
        sub_data['last_payment_status'] = subscription.payment.status
        # sub_data['payment_count'] = 1 # Since it's a one-to-one/many-to-one to Payment for this sub record
        
        # 添加更详细的支付信息
        sub_data['payment_details'] = {
            'id': subscription.payment.id,
            'order_id': subscription.payment.order_id,
            'payment_id': subscription.payment.payment_id,
            'amount': subscription.payment.amount,
            'currency': subscription.payment.currency,
            'payment_method': subscription.payment.payment_method,
            'status': subscription.payment.status,
            'transaction_id': subscription.payment.transaction_id,
            'created_at': subscription.payment.created_at.isoformat() if subscription.payment.created_at else None,
            'paid_at': subscription.payment.paid_at.isoformat() if subscription.payment.paid_at else None,
            'remarks': subscription.payment.remarks
        }
    else:
        sub_data['last_payment_status'] = 'N/A'
        # sub_data['payment_count'] = 0
        sub_data['payment_details'] = None

    logger.info(f"[管理员] 管理员 {g.user.id} 成功查询了订阅 {subscription_id} 的详情")
    return jsonify_response(success=True, data=sub_data, status_code=200)

@admin_bp.route('/subscriptions/<int:subscription_id>', methods=['PUT'])
@admin_required
def admin_update_subscription(subscription_id):
    """管理员更新订阅信息，如套餐、有效期、最大设备数等"""
    data = request.json
    if not data:
        return jsonify_response(success=False, message="请求数据不能为空", status_code=400)
        
    subscription = Subscription.query.get(subscription_id)

    if not subscription:
        return jsonify_response(success=False, message="订阅未找到", status_code=404)

    try:
        updated_fields = []

        # 更新 subscription_type_id (原 plan_id)
        if 'subscription_type_id' in data:
            new_type_id_str = data['subscription_type_id']
            try:
                new_type_id = int(new_type_id_str)
                # 验证新的订阅类型是否存在
                sub_type = SubscriptionType.query.get(new_type_id)
                if not sub_type:
                    return jsonify_response(success=False, message=f"订阅类型 ID {new_type_id} 未找到", status_code=400)
                if subscription.subscription_type_id != new_type_id:
                    old_type_id = subscription.subscription_type_id
                    subscription.subscription_type_id = new_type_id
                    updated_fields.append(f"subscription_type_id from {old_type_id} to {new_type_id}")
                    
                    # 同步更新关联的支付记录中的订阅类型ID
                    if subscription.payment:
                        subscription.payment.subscription_type_id = new_type_id
                        updated_fields.append(f"payment.subscription_type_id synced to {new_type_id}")
                        logger.info(f"[管理员] 同步更新支付记录 {subscription.payment.id} 的订阅类型ID为 {new_type_id}")
            except ValueError:
                return jsonify_response(success=False, message="subscription_type_id 格式无效，应为整数", status_code=400)
        
        # 更新 subscription_instance_id
        if 'subscription_instance_id' in data:
            new_instance_id = data['subscription_instance_id']
            if new_instance_id is not None:
                try:
                    new_instance_id = int(new_instance_id)
                    # 验证新的订阅实例是否存在
                    sub_instance = SubscriptionInstance.query.get(new_instance_id)
                    if not sub_instance:
                        return jsonify_response(success=False, message=f"订阅实例 ID {new_instance_id} 未找到", status_code=400)
                    # 验证订阅实例是否属于正确的订阅类型
                    if sub_instance.subscription_type_id != subscription.subscription_type_id:
                        return jsonify_response(success=False, message=f"订阅实例 {new_instance_id} 不属于当前订阅类型", status_code=400)
                    if subscription.subscription_instance_id != new_instance_id:
                        subscription.subscription_instance_id = new_instance_id
                        updated_fields.append(f"subscription_instance_id to {new_instance_id}")
                except ValueError:
                    return jsonify_response(success=False, message="subscription_instance_id 格式无效，应为整数", status_code=400)
        
        # 更新 start_date (原 current_period_start)
        if 'start_date' in data:
            try:
                # 处理以 'Z' 结尾的 ISO 格式日期字符串
                start_date_str = data['start_date']
                if isinstance(start_date_str, str) and start_date_str.endswith('Z'):
                    start_date_str = start_date_str[:-1] + '+00:00'
                new_start_date = datetime.fromisoformat(start_date_str)
                # 确保是 timezone-aware (UTC) for comparison if original is also UTC
                if subscription.start_date.tzinfo is None and new_start_date.tzinfo is not None:
                    subscription.start_date = subscription.start_date.replace(tzinfo=timezone.utc) # Assume existing is UTC if naive
                
                if subscription.start_date != new_start_date:
                    subscription.start_date = new_start_date
                    updated_fields.append(f"start_date to {new_start_date.isoformat()}")
            except ValueError:
                return jsonify_response(success=False, message="start_date 日期格式无效，请使用 ISO 格式 (YYYY-MM-DDTHH:MM:SSZ 或 YYYY-MM-DDTHH:MM:SS+00:00)", status_code=400)

        # 更新 end_date (原 current_period_end)
        if 'end_date' in data:
            try:
                end_date_str = data['end_date']
                if isinstance(end_date_str, str) and end_date_str.endswith('Z'):
                    end_date_str = end_date_str[:-1] + '+00:00'
                new_end_date = datetime.fromisoformat(end_date_str)
                # Ensure is timezone-aware (UTC) for comparison
                if subscription.end_date.tzinfo is None and new_end_date.tzinfo is not None:
                    subscription.end_date = subscription.end_date.replace(tzinfo=timezone.utc)

                if subscription.end_date != new_end_date:
                    subscription.end_date = new_end_date
                    updated_fields.append(f"end_date to {new_end_date.isoformat()}")
            except ValueError:
                return jsonify_response(success=False, message="end_date 日期格式无效，请使用 ISO 格式 (YYYY-MM-DDTHH:MM:SSZ 或 YYYY-MM-DDTHH:MM:SS+00:00)", status_code=400)

        # 更新 max_devices
        if 'max_devices' in data:
            try:
                new_max_devices = int(data['max_devices'])
                if new_max_devices < 0:
                     return jsonify_response(success=False, message="max_devices 不能为负数", status_code=400)
                if subscription.max_devices != new_max_devices:
                    subscription.max_devices = new_max_devices
                    updated_fields.append(f"max_devices to {new_max_devices}")
            except ValueError:
                return jsonify_response(success=False, message="max_devices 格式无效，应为整数", status_code=400)

        # 更新备注
        if 'remark' in data:
            new_remark = data['remark']
            if new_remark is not None:
                new_remark = str(new_remark).strip()
            if subscription.remark != new_remark:
                subscription.remark = new_remark
                updated_fields.append(f"remark to '{new_remark[:50]}{'...' if new_remark and len(new_remark) > 50 else ''}'")

        # 移除 status, notes, auto_renew 的直接更新，这些通常有更复杂的业务逻辑

        if updated_fields:
            subscription.updated_at = datetime.utcnow()
            db.session.commit()
            logger.info(f"[管理员] 管理员 {g.user.id} 更新了订阅 {subscription_id} 的以下字段: {', '.join(updated_fields)}")
            return jsonify_response(success=True, message="订阅更新成功", data=subscription.to_dict(), status_code=200)
        else:
            return jsonify_response(success=True, message="没有提供需要更新的字段或值未改变", data=subscription.to_dict(), status_code=200)

    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 更新订阅 {subscription_id} 时发生内部错误: {e}", exc_info=True)
        return jsonify_response(success=False, message=f"更新订阅时发生内部错误: {str(e)}", status_code=500)

@admin_bp.route('/subscriptions/<int:subscription_id>/cancel', methods=['POST'])
@admin_required
def admin_cancel_subscription(subscription_id):
    """管理员取消一个订阅"""
    subscription = Subscription.query.get(subscription_id)

    if not subscription:
        return jsonify_response(success=False, message="订阅未找到", status_code=404)

    if subscription.status == 'cancelled':
        return jsonify_response(success=True, message="订阅已经是取消状态", data=subscription.to_dict(), status_code=200)

    try:
        original_status = subscription.status
        subscription.status = 'cancelled'
        # 通常取消订阅意味着在当前周期结束后不再续订，而不是立即停止服务
        # 如果需要立即停止，可能需要设置 current_period_end = datetime.utcnow()
        # subscription.current_period_end = datetime.utcnow() # 取决于业务逻辑
        subscription.auto_renew = False # 确保取消后不会自动续订
        subscription.updated_at = datetime.utcnow()
        subscription.cancellation_date = datetime.utcnow() # 记录取消日期
        
        # 可以添加一个字段到 Subscription 模型来存储取消原因，如果需要的话
        # cancellation_reason = request.json.get('reason')
        # if cancellation_reason:
        #     subscription.cancellation_reason = cancellation_reason

        db.session.commit()
        logger.info(f"[管理员] 管理员 {g.user.id} 取消了订阅 {subscription_id} (原状态: {original_status})")
        return jsonify_response(success=True, message="订阅取消成功", data=subscription.to_dict(), status_code=200)
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 取消订阅 {subscription_id} 时发生内部错误: {e}", exc_info=True)
        return jsonify_response(success=False, message=f"取消订阅时发生内部错误: {str(e)}", status_code=500)

@admin_bp.route('/subscriptions/<int:subscription_id>/reactivate', methods=['POST'])
@admin_required
def admin_reactivate_subscription(subscription_id):
    """管理员重新激活一个之前取消的订阅"""
    subscription = Subscription.query.get(subscription_id)

    if not subscription:
        return jsonify_response(success=False, message="订阅未找到", status_code=404)

    # 只有特定状态的订阅才能被重新激活，例如 'cancelled' 或 'expired'
    if subscription.status in ['active', 'pending', 'trial']:
        return jsonify_response(success=True, message=f"订阅已经是 {subscription.status} 状态，无需重新激活", data=subscription.to_dict(), status_code=200)

    try:
        original_status = subscription.status
        subscription.status = 'active'  # 重新激活
        
        duration_days = 30  # 默认
        if subscription.plan_id:
            plan = Plan.query.get(subscription.plan_id)
            if plan and plan.duration_days:
                duration_days = plan.duration_days
        
        subscription.current_period_start = datetime.utcnow()
        subscription.current_period_end = datetime.utcnow() + timedelta(days=duration_days)
        subscription.auto_renew = True  # 重新激活后通常期望自动续订
        subscription.cancellation_date = None  # 清除之前的取消日期
        # subscription.cancellation_reason = None # 清除原因 (如果模型中有此字段)
        subscription.updated_at = datetime.utcnow()

        db.session.commit()
        logger.info(f"[管理员] 管理员 {g.user.id} 重新激活了订阅 {subscription_id} (原状态: {original_status}), 新有效期至 {subscription.current_period_end.strftime('%Y-%m-%d')}")
        return jsonify_response(success=True, message="订阅重新激活成功", data=subscription.to_dict(), status_code=200)
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 重新激活订阅 {subscription_id} 时发生内部错误: {e}", exc_info=True)
        return jsonify_response(success=False, message=f"重新激活订阅时发生内部错误: {str(e)}", status_code=500)

@admin_bp.route('/subscription-instances', methods=['GET'])
@admin_required
def admin_get_subscription_instances():
    """管理员获取所有订阅实例 (车次) 列表"""
    try:
        instances = SubscriptionInstance.query.order_by(SubscriptionInstance.id.desc()).all()
        instances_data = []
        for instance in instances:
            # 使用新方法获取活跃和过期用户数
            active_users_count = instance.get_active_users_count()
            expired_users_count = instance.get_expired_users_count()
            adspower_accounts_count = instance.adspower_accounts.count()
            available_slots = instance.get_available_slots()
            
            instances_data.append({
                'id': instance.id,
                'name': instance.name,
                'subscription_type_id': instance.subscription_type_id,
                'subscription_type_name': instance.subscription_type.name if instance.subscription_type else 'N/A',
                'subscription_type_code': instance.subscription_type.code if instance.subscription_type else 'N/A',
                'capacity': instance.capacity,
                'active_users_count': active_users_count,  # 活跃用户数
                'expired_users_count': expired_users_count,  # 过期用户数
                'available_slots': available_slots,  # 可用槽位
                'adspower_accounts_count': adspower_accounts_count,
                'is_active': instance.is_active,
                'status_display': '活跃' if instance.is_active else '禁用',
                'description': instance.description,
                'created_at': instance.created_at.isoformat() if instance.created_at else None,
                'updated_at': instance.updated_at.isoformat() if instance.updated_at else None,
            })
        logger.info(f"[管理员] 管理员 {g.user.id} 获取了 {len(instances_data)} 个订阅实例 (车次)")
        return jsonify_response(success=True,
                                message="订阅实例列表获取成功",
                                data={'subscription_instances': instances_data},
                                status_code=200)
    except Exception as e:
        logger.error(f"[管理员] 获取订阅实例列表失败: {str(e)}", exc_info=True)
        return jsonify_response(success=False,
                                message=f"获取订阅实例列表失败: {str(e)}",
                                data=None,
                                status_code=500)

@admin_bp.route('/subscription-instances', methods=['POST'])
@admin_required
def admin_create_subscription_instance():
    """管理员创建一个新的订阅实例 (车次)"""
    data = request.get_json()
    if not data:
        return jsonify_response(success=False, message="请求数据不能为空", status_code=400)

    name = data.get('name')
    subscription_type_id_str = data.get('subscription_type_id')
    capacity_str = data.get('capacity')
    is_active = data.get('is_active', True)  # 默认为 True
    description = data.get('description')

    if not name or subscription_type_id_str is None or capacity_str is None:
        return jsonify_response(success=False, message="缺少必要参数: 名称、订阅类型ID和容量", status_code=400)

    try:
        subscription_type_id = int(subscription_type_id_str)
        capacity = int(capacity_str)
    except ValueError:
        return jsonify_response(success=False, message="订阅类型ID和容量必须是整数", status_code=400)

    if capacity < 0:
        return jsonify_response(success=False, message="容量必须是一个非负整数", status_code=400)
    
    sub_type = SubscriptionType.query.get(subscription_type_id)
    if not sub_type:
        return jsonify_response(success=False, message=f"订阅类型ID '{subscription_type_id}' 不存在", status_code=404)

    if SubscriptionInstance.query.filter_by(name=name).first():
        return jsonify_response(success=False, message=f"订阅实例名称 '{name}' 已存在", status_code=409)

    try:
        new_instance = SubscriptionInstance(
            name=name,
            subscription_type_id=subscription_type_id,
            capacity=capacity,
            is_active=is_active,
            description=description
        )
        db.session.add(new_instance)
        db.session.commit()
        logger.info(f"[管理员] 管理员 {g.user.id} 创建了新的订阅实例: {name} (ID: {new_instance.id})")
        # 返回新创建实例的完整信息，以便前端更新列表
        instance_data = {
            'id': new_instance.id,
            'name': new_instance.name,
            'subscription_type_id': new_instance.subscription_type_id,
            'subscription_type_name': new_instance.subscription_type.name if new_instance.subscription_type else 'N/A',
            'subscription_type_code': new_instance.subscription_type.code if new_instance.subscription_type else 'N/A',
            'capacity': new_instance.capacity,
            'active_users_count': 0, # 新建实例用户数为0
            'is_active': new_instance.is_active,
            'status_display': '活跃' if new_instance.is_active else '禁用',
            'description': new_instance.description,
            'created_at': new_instance.created_at.isoformat() if new_instance.created_at else None,
            'updated_at': new_instance.updated_at.isoformat() if new_instance.updated_at else None,
        }
        return jsonify_response(success=True, message='订阅实例创建成功', data={'instance': instance_data}, status_code=201)
    except IntegrityError as ie: # Catch specific IntegrityError for unique constraints
        db.session.rollback()
        logger.error(f"[管理员] 创建订阅实例时发生数据库完整性错误 (可能名称重复): {str(ie)}", exc_info=True)
        return jsonify_response(success=False, message=f"创建订阅实例失败，可能名称已存在: {str(ie)}", status_code=409)
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 创建订阅实例时发生未知错误: {str(e)}", exc_info=True)
        return jsonify_response(success=False, message=f"创建订阅实例失败: {str(e)}", status_code=500)

@admin_bp.route('/subscription-instances/<int:instance_id>', methods=['GET'])
@admin_required
def admin_get_subscription_instance_detail(instance_id):
    """管理员获取单个订阅实例 (车次) 的详细信息"""
    try:
        instance = SubscriptionInstance.query.get_or_404(instance_id)
        # 使用新方法获取活跃和过期用户数
        active_users_count = instance.get_active_users_count()
        expired_users_count = instance.get_expired_users_count()
        available_slots = instance.get_available_slots()
        
        # 获取关联的AdsPower账号信息
        adspower_accounts = []
        for account in instance.adspower_accounts:
            # 只计算来自未过期订阅的设备
            active_devices_count = Device.query.join(User).join(Subscription).filter(
                Device.adspower_account_id == account.id,
                Subscription.end_date > datetime.utcnow()
            ).count()
            # 计算来自过期订阅的设备
            expired_devices_count = Device.query.join(User).join(Subscription).filter(
                Device.adspower_account_id == account.id,
                Subscription.end_date <= datetime.utcnow()
            ).count()
            adspower_accounts.append({
                'id': account.id,
                'username': account.username,
                'is_active': account.is_active,
                'current_devices': active_devices_count,  # 只显示活跃设备
                'active_devices': active_devices_count,  # 明确标记
                'expired_devices': expired_devices_count,  # 过期设备
                'max_devices': account.max_devices,
            })
        
        # 获取使用此实例的用户列表
        users = []
        for subscription in instance.user_subscriptions:
            if subscription.user:
                users.append({
                    'user_id': subscription.user.id,
                    'user_email': subscription.user.email,
                    'start_date': subscription.start_date.isoformat() if subscription.start_date else None,
                    'end_date': subscription.end_date.isoformat() if subscription.end_date else None,
                    'is_active': not subscription.is_expired()
                })
        
        instance_data = {
            'id': instance.id,
            'name': instance.name,
            'subscription_type_id': instance.subscription_type_id,
            'subscription_type_name': instance.subscription_type.name if instance.subscription_type else 'N/A',
            'subscription_type_code': instance.subscription_type.code if instance.subscription_type else 'N/A',
            'capacity': instance.capacity,
            'active_users_count': active_users_count,  # 活跃用户数
            'expired_users_count': expired_users_count,  # 过期用户数
            'available_slots': available_slots,  # 可用槽位
            'is_active': instance.is_active,
            'status_display': '活跃' if instance.is_active else '禁用',
            'description': instance.description,
            'created_at': instance.created_at.isoformat() if instance.created_at else None,
            'updated_at': instance.updated_at.isoformat() if instance.updated_at else None,
            'adspower_accounts': adspower_accounts,
            'adspower_accounts_count': len(adspower_accounts),
            'users': users
        }
        logger.info(f"[管理员] 管理员 {g.user.id} 获取了订阅实例 {instance_id} 的详情")
        return jsonify_response(success=True,
                                message="订阅实例详情获取成功",
                                data={'instance': instance_data},
                                status_code=200)
    except NotFound:
        logger.warning(f"[管理员] 尝试获取不存在的订阅实例详情: {instance_id}")
        return jsonify_response(success=False,
                                message=f"订阅实例ID {instance_id} 未找到",
                                data=None,
                                status_code=404)
    except Exception as e:
        logger.error(f"[管理员] 获取订阅实例 {instance_id} 详情失败: {str(e)}", exc_info=True)
        return jsonify_response(success=False,
                                message=f"获取实例详情失败: {str(e)}",
                                data=None,
                                status_code=500)

@admin_bp.route('/subscription-instances/<int:instance_id>', methods=['PUT'])
@admin_required
def admin_update_subscription_instance(instance_id):
    """管理员更新一个已存在的订阅实例 (车次) 的信息"""
    try:
        instance = SubscriptionInstance.query.get_or_404(instance_id)
        data = request.get_json()
        if not data:
            return jsonify_response(success=False, message="请求数据不能为空", data=None, status_code=400)

        name = data.get('name', instance.name)
        subscription_type_id_str = data.get('subscription_type_id')
        capacity_str = data.get('capacity')
        is_active = data.get('is_active', instance.is_active)
        description = data.get('description', instance.description)

        new_subscription_type_id = instance.subscription_type_id
        if subscription_type_id_str is not None:
            try:
                new_subscription_type_id = int(subscription_type_id_str)
            except ValueError:
                return jsonify_response(success=False, message="订阅类型ID必须是整数", data=None, status_code=400)
        
        new_capacity = instance.capacity
        if capacity_str is not None:
            try:
                new_capacity = int(capacity_str)
            except ValueError:
                return jsonify_response(success=False, message="容量必须是整数", data=None, status_code=400)

        if not name or new_subscription_type_id is None or new_capacity is None:
            return jsonify_response(success=False, message="名称、订阅类型ID和容量不能为空", data=None, status_code=400)

        if not isinstance(new_capacity, int) or new_capacity < 0:
            return jsonify_response(success=False, message="容量必须是一个非负整数", data=None, status_code=400)

        sub_type = SubscriptionType.query.get(new_subscription_type_id)
        if not sub_type:
            return jsonify_response(success=False, message=f"订阅类型ID '{new_subscription_type_id}' 不存在", data=None, status_code=404)

        existing_instance_with_name = SubscriptionInstance.query.filter(SubscriptionInstance.name == name, SubscriptionInstance.id != instance_id).first()
        if existing_instance_with_name:
            return jsonify_response(success=False, message=f"订阅实例名称 '{name}' 已被其他实例使用", data=None, status_code=409)

        instance.name = name
        instance.subscription_type_id = new_subscription_type_id
        instance.capacity = new_capacity
        instance.is_active = is_active
        instance.description = description
        instance.updated_at = datetime.utcnow()
        
        db.session.commit()
        logger.info(f"[管理员] 管理员 {g.user.id} 更新了订阅实例: {name} (ID: {instance_id})")
        
        # 返回更新后的实例的完整信息
        updated_instance_data = {
            'id': instance.id,
            'name': instance.name,
            'subscription_type_id': instance.subscription_type_id,
            'subscription_type_name': instance.subscription_type.name if instance.subscription_type else 'N/A',
            'subscription_type_code': instance.subscription_type.code if instance.subscription_type else 'N/A',
            'capacity': instance.capacity,
            'active_users_count': instance.get_active_users_count(), # 重新计算活跃用户数
            'is_active': instance.is_active,
            'status_display': '活跃' if instance.is_active else '禁用',
            'description': instance.description,
            'created_at': instance.created_at.isoformat() if instance.created_at else None,
            'updated_at': instance.updated_at.isoformat() if instance.updated_at else None,
        }
        return jsonify_response(success=True, message='订阅实例更新成功', data={'instance': updated_instance_data}, status_code=200)
    except NotFound:
        logger.warning(f"[管理员] 尝试更新不存在的订阅实例: {instance_id}")
        return jsonify_response(success=False, message=f"订阅实例ID {instance_id} 未找到", data=None, status_code=404)
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 更新订阅实例 {instance_id} 失败: {str(e)}", exc_info=True)
        return jsonify_response(success=False, message=f"更新订阅实例失败: {str(e)}", status_code=500)

@admin_bp.route('/subscription-instances/<int:instance_id>', methods=['DELETE'])
@admin_required
def admin_delete_subscription_instance(instance_id):
    """管理员删除一个订阅实例 (车次)"""
    try:
        instance = SubscriptionInstance.query.get_or_404(instance_id)
        
        # 检查活跃订阅
        active_user_subscriptions_count = instance.user_subscriptions.filter(Subscription.end_date > datetime.utcnow()).count()
        
        if active_user_subscriptions_count > 0:
            return jsonify_response(success=False, 
                                    message=f"无法删除实例 '{instance.name}'，因为它仍有 {active_user_subscriptions_count} 个活跃的用户订阅。请先处理这些订阅。", 
                                    data=None, 
                                    status_code=400)
        
        if instance.adspower_accounts.count() > 0:
            return jsonify_response(success=False, 
                                    message=f"无法删除实例 '{instance.name}'，因为它仍有 {instance.adspower_accounts.count()} 个关联的AdsPower账号。请先解除关联。", 
                                    data=None, 
                                    status_code=400)

        # 记录过期订阅数量（仅供日志记录）
        expired_user_subscriptions_count = instance.user_subscriptions.filter(Subscription.end_date <= datetime.utcnow()).count()
        
        instance_name = instance.name 
        db.session.delete(instance)
        db.session.commit()
        
        if expired_user_subscriptions_count > 0:
            logger.info(f"[管理员] 管理员 {g.user.id} 删除了订阅实例: {instance_name} (ID: {instance_id})，该实例有 {expired_user_subscriptions_count} 个过期订阅记录的关联已被清除")
        else:
            logger.info(f"[管理员] 管理员 {g.user.id} 删除了订阅实例: {instance_name} (ID: {instance_id})")
            
        return jsonify_response(success=True, 
                              message=f'订阅实例删除成功' + (f'，{expired_user_subscriptions_count} 个过期订阅记录已解除关联' if expired_user_subscriptions_count > 0 else ''), 
                              data=None, 
                              status_code=200)
    except NotFound:
        logger.warning(f"[管理员] 尝试删除不存在的订阅实例: {instance_id}")
        return jsonify_response(success=False, message=f"订阅实例ID {instance_id} 未找到", data=None, status_code=404)
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 删除订阅实例 {instance_id} 失败: {str(e)}", exc_info=True)
        return jsonify_response(success=False, message=f"删除订阅实例失败: {str(e)}", data=None, status_code=500) 

@admin_bp.route('/accounts/adspower/<int:account_id>/toggle-status', methods=['POST'])
@admin_required
def admin_toggle_adspower_account_status(account_id):
    """切换ADSpower账号状态"""
    try:
        admin_user = g.user
        data = request.json
        logger.info(f"[管理员] 管理员 {admin_user.id} 正在切换AdsPower账号 {account_id} 状态")
        
        if data is None or 'is_active' not in data:
            logger.warning(f"[管理员] 切换AdsPower账号状态请求缺少必要参数: {data}")
            return jsonify_response(success=False, message="缺少必要参数", data=None, status_code=400)
        
        account = AdspowerAccount.query.get(account_id)
        if not account:
            logger.warning(f"[管理员] 尝试切换不存在的AdsPower账号状态: {account_id}")
            return jsonify_response(success=False, message="账号不存在", status_code=404)
        
        # 更新状态
        old_status = account.is_active
        account.is_active = data['is_active']
        db.session.commit()
        
        logger.info(f"[管理员] 管理员 {admin_user.id} 将AdsPower账号 {account.username} (ID: {account_id}) 的状态从 {old_status} 更改为 {account.is_active}")
        
        # 通知 AccountWebDriverManager 更新状态
        try:
            manager = get_account_driver_manager()
            if manager:
                if account.is_active:
                    logger.info(f"[管理员] 准备通知 AccountWebDriverManager 重新开始管理账号 ID: {account_id}")
                    # 重新激活时，调用 add_managed_account
                    manager.add_managed_account(
                        account_id=str(account.id),
                        username=account.username,
                        password=account.password,
                        totp_secret=account.totp_secret,
                        cookies=account.cookies
                    )
                    logger.info(f"[管理员] AccountWebDriverManager 已接收账号 {account_id} 的重新管理请求")
                else:
                    logger.info(f"[管理员] 准备通知 AccountWebDriverManager 停止管理账号 ID: {account_id}")
                    # 禁用时，调用 remove_managed_account
                    manager.remove_managed_account(str(account_id))
                    logger.info(f"[管理员] AccountWebDriverManager 已处理账号 {account_id} 的移除请求")
            else:
                logger.warning(f"[管理员] AccountWebDriverManager 未运行，无法通知其更新账号 {account_id} 的状态")
        except Exception as e:
            # 记录错误，但不影响API成功返回，因为数据库已成功更新
            logger.error(f"[管理员] 通知 AccountWebDriverManager 更新账号 {account_id} 状态时出错: {e}", exc_info=True)

        return jsonify_response(success=True, 
                                message=f"账号状态已更新为: {'启用' if account.is_active else '禁用'}",
                                data=None)
        
    except Exception as e:
        logger.exception(f"[管理员] 更新AdsPower账号 {account_id} 状态时出错: {str(e)}")
        db.session.rollback()
        return jsonify_response(success=False, message=f"服务器内部错误: {str(e)}", status_code=500)

@admin_bp.route('/accounts/adspower/<int:account_id>/cookies', methods=['GET'])
@admin_required
def admin_get_adspower_account_cookies(account_id):
    """获取ADSpower账号的Cookies"""
    try:
        admin_user = g.user
        logger.info(f"[管理员] 管理员 {admin_user.id} 获取AdsPower账号 {account_id} 的Cookies")
        
        account = AdspowerAccount.query.get(account_id)
        if not account:
            logger.warning(f"[管理员] 尝试获取不存在的AdsPower账号Cookies: {account_id}")
            return jsonify_response(success=False, message="账号不存在", status_code=404)
        
        # 获取Cookies
        cookies = []
        if account.cookies: 
            try:
                cookies = json.loads(account.cookies) 
                logger.info(f"[管理员] 成功解析AdsPower账号 {account.username} (ID: {account_id}) 的Cookies")
            except Exception as e:
                logger.error(f"[管理员] 解析AdsPower账号 {account.username} (ID: {account_id}) 的Cookies失败: {str(e)}", exc_info=True)
                pass
        
        return jsonify_response(success=True, 
                                message="Cookies获取成功", 
                                data={"cookies": cookies})
        
    except Exception as e:
        logger.exception(f"[管理员] 获取AdsPower账号 {account_id} Cookies时出错: {str(e)}")
        return jsonify_response(success=False, message=f"服务器内部错误: {str(e)}", status_code=500)

@admin_bp.route('/accounts/adspower/<int:account_id>', methods=['PUT'])
@admin_required
def admin_update_adspower_account(account_id):
    """更新ADSpower账号信息"""
    try:
        admin_user = g.user
        data = request.json
        logger.info(f"[管理员] 管理员 {admin_user.id} 正在更新AdsPower账号 #{account_id}，接收数据: {data}")
        
        account = AdspowerAccount.query.get(account_id)
        if not account:
            logger.warning(f"[管理员] 尝试更新不存在的AdsPower账号: {account_id}")
            return jsonify_response(success=False, message="账号不存在", status_code=404)
        
        # 更新账号信息
        if 'username' in data:
            account.username = data['username']
            logger.info(f"[管理员] 更新账号 #{account_id} 用户名: {data['username']}")
        if 'password' in data and data['password']:
            account.password = data['password'] # Storing raw password, assuming setter handles hashing if needed
            logger.info(f"[管理员] 更新账号 #{account_id} 密码已提供")
        # API密钥字段已移除，跳过处理
        if 'totp_secret' in data and data['totp_secret']:
            account.totp_secret = data['totp_secret']
            logger.info(f"[管理员] 更新账号 #{account_id} TOTP密钥已提供")
        if 'max_devices' in data:
            account.max_devices = data['max_devices']
            logger.info(f"[管理员] 更新账号 #{account_id} 最大设备数: {data['max_devices']}")
        if 'subscription_type_id' in data:
            # 订阅类型通过订阅实例管理，此处仅记录日志
            logger.info(f"[管理员] 接收到订阅类型ID: {data['subscription_type_id']} (已忽略，通过订阅实例管理)")
        
        # subscription_instance_id已弃用，通过专门的接口管理多对多关系
        if 'subscription_instance_id' in data:
            logger.warning(f"[管理员] 收到已弃用的subscription_instance_id参数，请使用专门的关联管理接口")
        
        if 'cookies' in data:
            cookies_value = data['cookies']
            if isinstance(cookies_value, str):
                if cookies_value == "":
                    if account.cookies != cookies_value:
                        account.cookies = cookies_value
                        logger.info(f"[管理员] 更新账号 #{account_id} Cookies 已清空")
                else:
                    try:
                        json.loads(cookies_value)
                        if account.cookies != cookies_value:
                            account.cookies = cookies_value
                            logger.info(f"[管理员] 更新账号 #{account_id} Cookies - 字符串: {cookies_value[:100]}...")
                    except json.JSONDecodeError:
                        logger.error(f"[管理员] 更新账号 #{account_id} 失败：提供的 Cookies 不是有效的 JSON 字符串", exc_info=True)
                        return jsonify_response(success=False, message="提供的 Cookie 数据不是有效的 JSON 格式", status_code=400)
                    except Exception as parse_err:
                        logger.error(f"[管理员] 更新账号 #{account_id} 时解析 Cookies 字符串出错: {parse_err}", exc_info=True)
                        return jsonify_response(success=False, message=f"处理 Cookie 字符串时出错: {parse_err}", status_code=400)
            elif isinstance(cookies_value, (list, dict)):
                try:
                    new_cookies_json = json.dumps(cookies_value)
                    if account.cookies != new_cookies_json:
                        account.cookies = new_cookies_json
                        logger.info(f"[管理员] 更新账号 #{account_id} Cookies - 结构化数据: {account.cookies[:100]}...")
                except Exception as json_err:
                    logger.error(f"[管理员] 更新账号 #{account_id} 时序列化 Cookies 出错: {json_err}", exc_info=True)
                    return jsonify_response(success=False, message=f"处理 Cookie 数据时出错: {json_err}", status_code=400)
            else:
                logger.warning(f"[管理员] 更新账号 #{account_id} 时收到非预期的 Cookies 类型: {type(cookies_value)}，将忽略此字段更新")

        if 'remarks' in data:
            # 使用description字段替代remarks
            if account.description != data['remarks']:
                account.description = data['remarks']
                logger.info(f"[管理员] 更新账号 #{account_id} 描述: {account.description[:100] if account.description else '空'}...")
        
        db.session.commit()
        logger.info(f"[管理员] AdsPower账号 #{account.id} ({account.username}) 数据库更新成功")
        
        try:
            logger.info(f"[管理员] 账号 #{account_id} 信息已更新，正在通知 WebDriver 管理器...")
            manager = get_account_driver_manager()
            if manager:
                manager.add_managed_account(
                    account_id=str(account.id),
                    username=account.username,
                    password=account.password, 
                    totp_secret=account.totp_secret,
                    cookies=account.cookies
                )
                logger.info(f"[管理员] 账号 #{account_id} 的 WebDriver 管理器信息已同步")
            else:
                logger.warning(f"[管理员] 更新账号 #{account_id} 后，AccountWebDriverManager 未运行，无法同步信息")
        except Exception as manager_e:
            logger.error(f"[管理员] 更新账号 #{account_id} 后同步 WebDriver 管理器状态时出错: {manager_e}", exc_info=True)

        updated_account = AdspowerAccount.query.get(account_id) 
        # 获取账号关联的订阅实例ID列表
        instance_ids = [instance.id for instance in updated_account.subscription_instances]
        
        return jsonify_response(success=True,
                                message="账号信息更新成功",
                                data={
                                    "account": {
                                        "id": updated_account.id,
                                        "username": updated_account.username,
                                        "subscription_instance_ids": instance_ids,  # 改为返回实例ID列表
                                        "max_devices": updated_account.max_devices
                                    }
                                })
    except Exception as e:
        logger.exception(f"[管理员] 更新AdsPower账号 #{account_id} 时出错: {str(e)}")
        db.session.rollback()
        return jsonify_response(success=False, message=f"更新账号失败: {str(e)}", status_code=500)

@admin_bp.route('/accounts/adspower/<int:account_id>', methods=['GET'])
@admin_required
def admin_get_adspower_account_detail(account_id):
    """获取指定AdsPower账号的详细信息"""
    # 使用账号信息聚合服务获取完整信息
    from ..services import AdsPowerAccountInfoService
    
    account_info = AdsPowerAccountInfoService.get_account_full_info(account_id)
    if not account_info:
        logger.warning(f"[管理员] 尝试获取不存在的AdsPower账号详情: {account_id}")
        return jsonify_response(success=False, message=f"账号ID {account_id} 不存在", data=None, status_code=404)
    
    # 获取该账号关联的设备列表
    devices = Device.query.filter_by(adspower_account_id=account_id).options(
        joinedload(Device.user)
    ).all()
    
    # 格式化设备列表
    devices_data = []
    for device in devices:
        devices_data.append({
            "id": device.id,
            "device_name": device.device_name,
            "device_type": device.device_type,
            "user": {
                "id": device.user.id if device.user else None,
                "email": device.user.email if device.user else None
            } if device.user else None
        })
    
    # 获取订阅实例信息（多对多关系）
    subscription_instances_data = []
    account_obj = AdspowerAccount.query.get(account_id)
    if account_obj:
        for instance in account_obj.subscription_instances:
            subscription_instances_data.append({
                "id": instance.id,
                "name": instance.name,
                "is_active": instance.is_active,
                "subscription_type": {
                    "id": instance.subscription_type.id,
                    "name": instance.subscription_type.name
                } if instance.subscription_type else None
            })
    
    logger.info(f"[管理员] 获取AdsPower账号 {account_info['username']} (ID: {account_id}) 详情，关联设备数: {len(devices)}")
    
    return jsonify_response(success=True,
                            message="账号详情获取成功",
                            data={
                                "id": account_info['id'],
                                "username": account_info['username'],
                                "max_devices": account_info['max_devices'],
                                "current_devices": account_info['current_devices'],
                                "subscription_instances": subscription_instances_data,
                                "is_active": account_info['is_active'],
                                "description": account_info['description'],
                                "created_at": account_info['created_at'],
                                "updated_at": account_info['updated_at'],
                                "devices": devices_data,
                                # 健康状态信息
                                "health_status": account_info['health_status'],
                                "health_message": account_info['health_message'],
                                "health_details": account_info.get('health_details', {}),
                                "runtime_status": account_info.get('runtime_status', {})
                            })

@admin_bp.route('/accounts/adspower/<int:account_id>', methods=['DELETE'])
@admin_required
def admin_delete_adspower_account_permanently(account_id):
    """永久删除AdsPower账号"""
    try:
        admin_user = g.user
        logger.info(f"[管理员] 管理员 {admin_user.id} 请求永久删除AdsPower账号 ID: {account_id}")
        
        account = AdspowerAccount.query.get(account_id)
        if not account:
            logger.warning(f"[管理员] 尝试删除不存在的AdsPower账号: {account_id}")
            return jsonify_response(success=False, message=f"账号ID {account_id} 不存在", data=None, status_code=404)
        
        # 检查是否有关联的设备
        devices = Device.query.filter_by(adspower_account_id=account_id).all()
        if devices:
            # 将设备的adspower_account_id设为NULL 或根据业务逻辑处理 (例如，禁止删除或一并删除设备记录)
            # 当前逻辑：解除关联
            for device_in_account in devices: # Renamed to avoid conflict
                device_in_account.adspower_account_id = None
                logger.info(f"[管理员] 设备 #{device_in_account.id} 的AdsPower账号关联已移除 (账号删除操作)")
        
        # 从 AccountWebDriverManager 移除（如果正在管理）
        try:
            manager = get_account_driver_manager()
            if manager:
                manager.remove_managed_account(str(account.id))
                logger.info(f"[管理员] 已通知 AccountWebDriverManager 停止管理账号 ID: {account.id} (删除前)")
        except Exception as manager_e:
            logger.error(f"[管理员] 从 AccountWebDriverManager 移除账号 ID: {account.id} 时出错 (删除前): {manager_e}", exc_info=True)
            # 非致命错误，继续删除数据库记录

        # 删除账号
        db.session.delete(account)
        db.session.commit()
        
        logger.info(f"[管理员] AdsPower账号 #{account_id} ({account.username if account else 'N/A'}) 已被管理员 {admin_user.id} 永久删除")
        return jsonify_response(success=True,
                                message="账号删除成功",
                                data=None)
            
    except Exception as e:
        logger.exception(f"[管理员] 永久删除AdsPower账号 #{account_id} 时出错: {str(e)}")
        db.session.rollback()
        return jsonify_response(success=False, message=f"删除账号失败: {str(e)}", data=None, status_code=500)

@admin_bp.route('/accounts/adspower/batch-set-subscription', methods=['POST'])
@admin_required
def batch_set_adspower_subscription():
    """[已废弃] 批量设置AdsPower账号的订阅类型
    
    注意：subscription_type 字段已从 AdspowerAccount 模型中移除。
    请使用 /accounts/adspower/batch-associate-instances 接口来管理账号与订阅实例的关联。
    
    请求体:
    {
        "account_ids": [1, 2, 3],  // 账号ID列表
        "subscription_type": "monthly"  // 要设置的订阅类型
    }
    
    返回:
    {
        "success": true/false,
        "message": "操作结果信息",
        "success_count": 3,  // 成功更新的账号数量
        "failed_count": 0    // 更新失败的账号数量
    }
    """
    try:
        data = request.json
        if not data or 'account_ids' not in data or 'subscription_type' not in data:
            return jsonify_response(success=False,
                                    message="缺少必要参数",
                                    data=None, # Added data=None
                                    status_code=400)
        
        account_ids = data['account_ids']
        subscription_type = data['subscription_type']
        
        # 检查账号ID列表是否为空
        if not account_ids:
            return jsonify_response(success=False,
                                    message="账号ID列表不能为空",
                                    data=None, # Added data=None
                                    status_code=400)
        
        # 检查订阅类型是否存在
        if subscription_type:
            subscription_type_obj = SubscriptionType.query.filter_by(code=subscription_type).first()
            if not subscription_type_obj and subscription_type not in ['monthly', 'student', 'trial', 'basic']:
                return jsonify_response(success=False,
                                        message=f"无效的订阅类型: {subscription_type}",
                                        data=None, # Added data=None
                                        status_code=400)
                
        # 查询所有指定的账号
        accounts = AdspowerAccount.query.filter(AdspowerAccount.id.in_(account_ids)).all()
        
        # 记录更新结果
        success_count = 0
        failed_ids = []
        
        # 更新每个账号的订阅类型
        for account in accounts:
            try:
                # subscription_type 字段已从 AdspowerAccount 模型中移除
                # 此功能已废弃，请使用批量关联到订阅实例的功能
                logger.warning(f"尝试设置已移除的字段 subscription_type，账号ID: {account.id}")
                failed_ids.append(account.id)
                success_count += 1
            except Exception as e:
                logger.error(f"更新账号 {account.id} 订阅类型失败: {e}", exc_info=True)
                failed_ids.append(account.id)
        
        # 提交更改
        db.session.commit()
        
        failed_count = len(account_ids) - success_count
        
        return jsonify_response(success=True,
                                message=f"批量更新完成: {success_count} 成功, {failed_count} 失败",
                                data={
                                    "success_count": success_count,
                                    "failed_count": failed_count,
                                    "failed_ids": failed_ids if failed_ids else None
                                })
    except Exception as e:
        db.session.rollback()
        logger.error(f"批量设置AdsPower账号订阅类型失败: {e}", exc_info=True)
        return jsonify_response(success=False,
                                message=f"操作失败: {str(e)}",
                                data=None, # Added data=None
                                status_code=500)

@admin_bp.route('/accounts/adspower/batch-associate-instances', methods=['POST'])
@admin_required
def batch_associate_adspower_to_instances():
    """批量关联AdsPower账号到订阅实例
    
    请求体:
    {
        "account_ids": [1, 2, 3],  // 账号ID列表
        "instance_id": 1,  // 要关联到的订阅实例ID
        "action": "add"  // 操作类型: "add"(添加关联) 或 "remove"(移除关联)
    }
    """
    try:
        data = request.json
        if not data or 'account_ids' not in data or 'instance_id' not in data:
            return jsonify_response(success=False,
                                    message="缺少必要参数: account_ids 和 instance_id",
                                    data=None,
                                    status_code=400)
        
        account_ids = data['account_ids']
        instance_id = data['instance_id']
        action = data.get('action', 'add')
        
        # 检查订阅实例
        instance = SubscriptionInstance.query.get(instance_id)
        if not instance:
            return jsonify_response(success=False,
                                    message=f"订阅实例ID {instance_id} 不存在",
                                    data=None,
                                    status_code=404)
        
        # 查询账号
        accounts = AdspowerAccount.query.filter(AdspowerAccount.id.in_(account_ids)).all()
        
        success_count = 0
        failed_accounts = []
        
        for account in accounts:
            try:
                if action == 'add':
                    if account not in instance.adspower_accounts:
                        instance.adspower_accounts.append(account)
                        success_count += 1
                    else:
                        failed_accounts.append({
                            "id": account.id,
                            "username": account.username,
                            "reason": "已关联"
                        })
                elif action == 'remove':
                    if account in instance.adspower_accounts:
                        instance.adspower_accounts.remove(account)
                        success_count += 1
                    else:
                        failed_accounts.append({
                            "id": account.id,
                            "username": account.username,
                            "reason": "未关联"
                        })
            except Exception as e:
                failed_accounts.append({
                    "id": account.id,
                    "username": account.username,
                    "reason": str(e)
                })
        
        db.session.commit()
        
        return jsonify_response(
            success=True if success_count > 0 else False,
            message=f"成功处理 {success_count} 个账号",
            data={
                "success_count": success_count,
                "failed_accounts": failed_accounts
            }
        )
    except Exception as e:
        db.session.rollback()
        logger.error(f"批量关联失败: {e}", exc_info=True)
        return jsonify_response(success=False,
                                message=f"操作失败: {str(e)}",
                                data=None,
                                status_code=500)

@admin_bp.route('/devices/<device_id>', methods=['GET'])
@admin_required
def admin_get_device(device_id):
    """管理员获取单个设备的详细信息"""
    try:
        # 验证设备存在
        device = Device.query.get(device_id)
        if not device:
            return jsonify_response(success=False, message="设备不存在", data=None, status_code=404)
        
        # 获取关联用户
        user = User.query.get(device.user_id) if device.user_id else None
        
        # 获取关联的AdsPower账号
        adspower_account_dict = None # Renamed to avoid confusion with the SQLAlchemy object
        if device.adspower_account_id:
            adspower_account_obj = AdspowerAccount.query.get(device.adspower_account_id) # SQLAlchemy object
            if adspower_account_obj:
                # 动态计算当前设备数量
                current_devices_count = Device.query.filter_by(adspower_account_id=adspower_account_obj.id).count()
                adspower_account_dict = {
                    'id': adspower_account_obj.id,
                    'username': adspower_account_obj.username,
                    'is_active': adspower_account_obj.is_active,
                    'current_devices': current_devices_count, # 使用动态计算的值
                    'max_devices': adspower_account_obj.max_devices
                }
        
        # 处理IP地址 - 确保格式化正确
        ip_address = device.device_ip
        if ip_address:
            # 清理IP地址 - 移除HTML标签和特殊字符
            import re
            # 尝试从字符串中提取有效的IP地址
            ip_match = re.search(r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|[0-9a-f:]+)', ip_address)
            if ip_match:
                ip_address = ip_match.group(1)
            else:
                # 移除HTML标签
                ip_address = re.sub(r'<[^>]*>?', '', ip_address).strip()
        
        # 构建设备详情数据（优化结构）
        device_info = {
            'id': device.id,
            'device_name': device.device_name,
            'device_ip': ip_address,
            'device_type': device.device_type,
            'created_at': device.created_at.isoformat() if device.created_at else None,
            'user': {
                'id': user.id if user else None,
                'email': user.email if user else None
            } if user else None, # 如果用户不存在，整个user对象为None
            'adspower_account': adspower_account_dict # adspower_account本身就是字典或None
            # 移除顶层冗余字段: user_id, user_email, adspower_account_id, adspower_username
            # 移除 user['username']
            # 移除 'extra' 字段
        }
        
        # 如果 adspower_account 为 None，也明确设置为 None 而不是默认字典
        if not adspower_account_dict:
            device_info['adspower_account'] = None
            
        return jsonify_response(success=True, data=device_info, message="设备详情获取成功") # Added message
        
    except Exception as e:
        logger.exception(f"获取设备详情时出错: {str(e)}")
        return jsonify_response(success=False, message="服务器内部错误", data=None, status_code=500)

@admin_bp.route('/subscriptions/<int:subscription_id>', methods=['DELETE'])
@admin_required
def admin_delete_subscription(subscription_id):
    subscription = db.session.get(Subscription, subscription_id)
    if not subscription:
        return jsonify_response(success=False, message="未找到订阅", data=None, status_code=404)
    try:
        db.session.delete(subscription)
        db.session.commit()
        return jsonify_response(success=True, message="订阅已成功删除", data=None, status_code=200) # Added data=None and status_code
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除订阅失败 subscription_id={subscription_id}: {str(e)}", exc_info=True)
        return jsonify_response(success=False, message=f"删除订阅失败: {str(e)}", data=None, status_code=500)

@admin_bp.route('/subscription-types', methods=['GET'])
@admin_required
def admin_get_subscription_types():
    """管理员获取所有订阅类型"""
    try:
        subscription_types = SubscriptionType.query.all()
        
        types_data = []
        for sub_type in subscription_types:
            # 计算该类型下的实例数和用户数
            instances_count = SubscriptionInstance.query.filter_by(subscription_type_id=sub_type.id).count()
            active_subscriptions_count = Subscription.query.filter_by(
                subscription_type_id=sub_type.id
            ).filter(Subscription.end_date > datetime.utcnow()).count()
            
            types_data.append({
                'id': sub_type.id,
                'code': sub_type.code,
                'name': sub_type.name,
                'price': sub_type.price,
                'days': sub_type.days,
                'max_devices': sub_type.max_devices,
                'default_subscription_instance_capacity': sub_type.default_subscription_instance_capacity,
                'description': sub_type.description,
                'is_public': sub_type.is_public,
                'requirements': sub_type.requirements,
                'sync_to_claude': sub_type.sync_to_claude,  # 添加同步标记
                'instances_count': instances_count,
                'active_subscriptions_count': active_subscriptions_count,
                'created_at': sub_type.created_at.isoformat() if sub_type.created_at else None,
                'updated_at': sub_type.updated_at.isoformat() if sub_type.updated_at else None
            })
        
        logger.info(f"[管理员] 管理员 {g.user.id} 获取了 {len(types_data)} 个订阅类型")
        return jsonify_response(
            success=True,
            message="订阅类型列表获取成功",
            data={'subscription_types': types_data},
            status_code=200
        )
    except Exception as e:
        logger.error(f"[管理员] 获取订阅类型列表失败: {str(e)}", exc_info=True)
        return jsonify_response(
            success=False,
            message=f"获取订阅类型列表失败: {str(e)}",
            status_code=500
        )

@admin_bp.route('/subscription-types/<int:type_id>/instances', methods=['GET'])
@admin_required # Ensure this decorator is correctly defined/imported
def admin_get_subscription_type_instances(type_id):
    """管理员获取特定订阅类型关联的所有订阅实例"""
    try:
        subscription_type = SubscriptionType.query.get(type_id)
        if not subscription_type:
            logger.warning(f"[管理员] 查询订阅类型 {type_id} 的关联实例失败：订阅类型未找到")
            return jsonify_response(success=False, message='订阅类型未找到', status_code=404)

        instances = SubscriptionInstance.query.filter_by(subscription_type_id=type_id).all()
        
        instances_data = []
        for instance in instances:
            # 使用新方法获取活跃用户数
            active_users_count = instance.get_active_users_count()
            expired_users_count = instance.get_expired_users_count()
            available_slots = instance.get_available_slots()
            
            instances_data.append({
                'id': instance.id,
                'name': instance.name,
                'capacity': instance.capacity,
                'current_users': active_users_count,  # 只显示活跃用户数
                'active_users_count': active_users_count,  # 活跃用户数
                'expired_users_count': expired_users_count,  # 过期用户数
                'available_slots': available_slots,  # 可用槽位
                'is_active': instance.is_active,
                'description': instance.description,
                'created_at': instance.created_at.isoformat() if instance.created_at else None,
            })
        
        logger.info(f"[管理员] 管理员 {g.user.id} 获取了订阅类型 {type_id} ({subscription_type.name}) 的 {len(instances_data)} 个关联实例")
        return jsonify_response(success=True,
                                message="关联的订阅实例列表获取成功",
                                data={'instances': instances_data},
                                status_code=200)
    except Exception as e:
        logger.error(f"[管理员] 获取订阅类型 {type_id} 的关联实例失败: {str(e)}", exc_info=True)
        return jsonify_response(success=False,
                                message=f"获取关联实例列表失败: {str(e)}",
                                status_code=500)
    
@admin_bp.route('/subscription-instances/<int:instance_id>/adspower-accounts', methods=['POST'])
@admin_required
def admin_assign_adspower_account_to_instance(instance_id):
    """管理员将AdsPower账号关联到订阅实例"""
    try:
        data = request.get_json()
        if not data:
            return jsonify_response(success=False, message="请求数据不能为空", status_code=400)
        
        # 支持单个账号ID或账号ID列表
        account_ids = []
        if 'adspower_account_id' in data:
            # 兼容旧版单个账号ID
            account_ids = [data['adspower_account_id']]
        elif 'account_ids' in data:
            # 新版批量账号ID
            account_ids = data['account_ids']
            if not isinstance(account_ids, list):
                return jsonify_response(success=False, message="account_ids必须是数组", status_code=400)
        else:
            return jsonify_response(success=False, message="缺少必要参数: account_ids 或 adspower_account_id", status_code=400)
        
        if not account_ids:
            return jsonify_response(success=False, message="账号ID列表不能为空", status_code=400)
        
        # 验证订阅实例存在
        instance = SubscriptionInstance.query.get(instance_id)
        if not instance:
            return jsonify_response(success=False, message=f"订阅实例ID {instance_id} 未找到", status_code=404)
        
        success_count = 0
        failed_accounts = []
        
        for account_id in account_ids:
            try:
                # 验证AdsPower账号存在
                adspower_account = AdspowerAccount.query.get(account_id)
                if not adspower_account:
                    failed_accounts.append({"id": account_id, "reason": "账号不存在"})
                    continue
                
                # 检查账号是否已关联到此实例（多对多关系）
                if adspower_account in instance.adspower_accounts:
                    failed_accounts.append({
                        "id": account_id, 
                        "username": adspower_account.username,
                        "reason": "账号已关联到此实例"
                    })
                    continue
                
                # 执行关联（多对多关系）
                instance.adspower_accounts.append(adspower_account)
                success_count += 1
                logger.info(f"[管理员] 管理员 {g.user.id} 将AdsPower账号 {adspower_account.username} (ID: {account_id}) 关联到订阅实例 {instance.name} (ID: {instance_id})")
                
            except Exception as e:
                failed_accounts.append({"id": account_id, "reason": str(e)})
                logger.error(f"关联账号ID {account_id} 时出错: {str(e)}", exc_info=True)
        
        db.session.commit()
        
        # 构建响应消息
        message_parts = []
        if success_count > 0:
            message_parts.append(f"成功关联 {success_count} 个账号")
        if failed_accounts:
            message_parts.append(f"失败 {len(failed_accounts)} 个")
        
        return jsonify_response(
            success=True if success_count > 0 else False,
            message="。".join(message_parts) if message_parts else "没有账号被关联",
            data={
                'success_count': success_count,
                'failed_accounts': failed_accounts,
                'instance_id': instance_id
            }
        )
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 关联AdsPower账号到订阅实例时发生错误: {str(e)}", exc_info=True)
        return jsonify_response(success=False, message=f"关联失败: {str(e)}", status_code=500)

def logout_devices_and_remove_association(instance, account, force_logout=True):
    """
    统一的设备下线和关联移除辅助函数
    
    Args:
        instance: SubscriptionInstance对象
        account: AdspowerAccount对象
        force_logout: 是否强制下线设备（默认True）
        
    Returns:
        tuple: (success, message, logout_details)
    """
    # 导入必要的服务
    from adspower_manager.services.device_audit_service import DeviceAuditService
    
    # 获取该实例下使用此账号的所有设备
    instance_user_ids = [sub.user_id for sub in instance.user_subscriptions.filter(
        Subscription.end_date > datetime.utcnow()
    )]
    
    devices_to_logout = Device.query.filter(
        Device.adspower_account_id == account.id,
        Device.user_id.in_(instance_user_ids) if instance_user_ids else False
    ).all()
    
    if not force_logout and devices_to_logout:
        return False, f"无法解除关联，该实例下仍有 {len(devices_to_logout)} 个设备在使用此账号", []
    
    # 逐个下线设备
    logout_results = []
    adspower_api = get_adspower_api()
    
    for device in devices_to_logout:
        try:
            # 调用AdsPower API下线设备
            if device.device_id:
                logout_success, message = adspower_api.logout_device(
                    account,
                    device.device_id
                )
            else:
                logout_success = False
                message = "设备缺少唯一标识符"
            
            if logout_success:
                # 记录审计日志
                DeviceAuditService.log_device_action(
                    device_id=device.id,
                    user_id=device.user_id,
                    action=DeviceAuditService.ACTION_DELETE,
                    action_source='admin',
                    description="移除账号关联时自动下线设备",
                    device_snapshot={
                        'device_id': device.device_id,
                        'device_name': device.device_name,
                        'device_type': device.device_type,
                        'adspower_account_id': device.adspower_account_id,
                        'adspower_username': account.username
                    }
                )
                
                # 删除设备记录
                db.session.delete(device)
                logout_results.append({
                    'device_id': device.device_id,
                    'device_name': device.device_name,
                    'user_id': device.user_id,
                    'user_email': device.user.email if device.user else 'N/A',
                    'success': True,
                    'message': message
                })
                
                logger.info(f"[管理员] 成功下线设备 {device.device_name} (用户: {device.user.email if device.user else 'N/A'})")
            else:
                logout_results.append({
                    'device_id': device.device_id,
                    'device_name': device.device_name,
                    'user_id': device.user_id,
                    'user_email': device.user.email if device.user else 'N/A',
                    'success': False,
                    'message': message
                })
                logger.warning(f"[管理员] 下线设备失败 {device.device_name}: {message}")
                
        except Exception as e:
            logout_results.append({
                'device_id': device.device_id,
                'device_name': device.device_name,
                'user_id': device.user_id,
                'user_email': device.user.email if device.user else 'N/A',
                'success': False,
                'message': str(e)
            })
            logger.error(f"[管理员] 下线设备时发生错误 {device.device_name}: {str(e)}", exc_info=True)
    
    # 处理设备下线结果
    failed_logouts = [r for r in logout_results if not r['success']]
    success_count = len([r for r in logout_results if r['success']])
    
    # 方案A：部分成功也继续执行
    # 删除成功下线的设备记录
    for device in devices_to_logout:
        device_result = next((r for r in logout_results if r['device_id'] == device.device_id), None)
        if device_result and device_result['success']:
            # 设备下线成功，删除记录
            db.session.delete(device)
            logger.info(f"[管理员] 删除已成功下线的设备记录: {device.device_name}")
        else:
            # 设备下线失败，保留记录
            logger.warning(f"[管理员] 设备下线失败，保留记录: {device.device_name}")
    
    # 解除关联
    try:
        instance.adspower_accounts.remove(account)
        db.session.commit()
        
        # 构建返回消息
        if devices_to_logout:
            if failed_logouts:
                message = f"已解除关联。成功下线 {success_count}/{len(devices_to_logout)} 个设备"
                if len(failed_logouts) > 0:
                    failed_names = [d['device_name'] for d in failed_logouts]
                    message += f"，失败的设备: {', '.join(failed_names[:3])}"
                    if len(failed_names) > 3:
                        message += f" 等{len(failed_names)}个"
            else:
                message = f"已成功下线所有 {len(devices_to_logout)} 个设备并解除关联"
        else:
            message = "已成功解除关联"
            
        return True, message, logout_results
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 解除关联时发生错误: {str(e)}", exc_info=True)
        return False, f"解除关联失败: {str(e)}", logout_results

@admin_bp.route('/subscription-instances/<int:instance_id>/adspower-accounts/<int:account_id>', methods=['DELETE'])
@admin_required
def admin_remove_adspower_account_from_instance(instance_id, account_id):
    """管理员解除AdsPower账号与订阅实例的关联（从订阅实例管理页面）"""
    try:
        # 验证订阅实例存在
        instance = SubscriptionInstance.query.get(instance_id)
        if not instance:
            return jsonify_response(success=False, message=f"订阅实例ID {instance_id} 未找到", status_code=404)
        
        # 验证AdsPower账号存在
        adspower_account = AdspowerAccount.query.get(account_id)
        if not adspower_account:
            return jsonify_response(success=False, message=f"AdsPower账号ID {account_id} 未找到", status_code=404)
        
        # 验证账号确实关联到此实例（多对多关系）
        if adspower_account not in instance.adspower_accounts:
            return jsonify_response(success=False, 
                                    message="该账号未关联到此订阅实例", 
                                    status_code=400)
        
        # 使用统一的辅助函数处理设备下线和关联移除
        success, message, logout_details = logout_devices_and_remove_association(instance, adspower_account, force_logout=True)
        
        if success:
            logger.info(f"[管理员] 管理员 {g.user.id} 解除了AdsPower账号 {adspower_account.username} (ID: {account_id}) 与订阅实例 {instance.name} (ID: {instance_id}) 的关联")
            
            return jsonify_response(
                success=True, 
                message=message,
                data={
                    'logout_details': logout_details,
                    'devices_logged_out': len([d for d in logout_details if d['success']])
                }
            )
        else:
            return jsonify_response(
                success=False, 
                message=message,
                data={
                    'logout_details': logout_details
                },
                status_code=400
            )
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 解除AdsPower账号与订阅实例关联时发生错误: {str(e)}", exc_info=True)
        return jsonify_response(success=False, message=f"解除关联失败: {str(e)}", status_code=500)

@admin_bp.route('/subscription-instances/<int:instance_id>/adspower-accounts/<int:account_id>/affected-devices', methods=['GET'])
@admin_required
def get_affected_devices_count(instance_id, account_id):
    """获取解除关联时将受影响的设备数量"""
    try:
        # 验证订阅实例存在
        instance = SubscriptionInstance.query.get(instance_id)
        if not instance:
            return jsonify_response(success=False, message=f"订阅实例ID {instance_id} 未找到", status_code=404)
        
        # 验证AdsPower账号存在
        adspower_account = AdspowerAccount.query.get(account_id)
        if not adspower_account:
            return jsonify_response(success=False, message=f"AdsPower账号ID {account_id} 未找到", status_code=404)
        
        # 获取该实例下使用此账号的设备数量
        instance_user_ids = [sub.user_id for sub in instance.user_subscriptions.filter(
            Subscription.end_date > datetime.utcnow()
        )]
        
        affected_devices = Device.query.filter(
            Device.adspower_account_id == account_id,
            Device.user_id.in_(instance_user_ids) if instance_user_ids else False
        ).all()
        
        device_details = []
        for device in affected_devices:
            device_details.append({
                'device_id': device.device_id,
                'device_name': device.device_name,
                'device_type': device.device_type,
                'user_email': device.user.email if device.user else 'N/A'
            })
        
        return jsonify_response(
            success=True,
            data={
                'affected_count': len(affected_devices),
                'devices': device_details
            }
        )
        
    except Exception as e:
        logger.error(f"[管理员] 获取受影响设备数量时发生错误: {str(e)}", exc_info=True)
        return jsonify_response(success=False, message=f"获取失败: {str(e)}", status_code=500)

@admin_bp.route('/subscription-instances/<int:instance_id>/batch-update-users', methods=['POST'])
@admin_required
def admin_batch_update_user_subscriptions(instance_id):
    """管理员批量修改订阅实例中用户的订阅时间"""
    try:
        # 验证订阅实例存在
        instance = SubscriptionInstance.query.get(instance_id)
        if not instance:
            return jsonify_response(success=False, message=f"订阅实例ID {instance_id} 未找到", status_code=404)
        
        # 获取请求数据
        data = request.get_json()
        user_ids = data.get('user_ids', [])
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        # 验证参数
        if not user_ids:
            return jsonify_response(success=False, message="未提供用户ID列表", status_code=400)
        
        # 至少需要提供一个时间参数
        if not start_date and not end_date:
            return jsonify_response(success=False, message="请至少提供订阅开始或结束时间之一", status_code=400)
        
        # 转换日期格式
        parsed_start_date = None
        parsed_end_date = None
        
        if start_date:
            try:
                parsed_start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            except Exception as e:
                return jsonify_response(success=False, message=f"开始日期格式错误: {str(e)}", status_code=400)
        
        if end_date:
            try:
                parsed_end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            except Exception as e:
                return jsonify_response(success=False, message=f"结束日期格式错误: {str(e)}", status_code=400)
        
        # 如果两个时间都提供了，验证日期合法性
        if parsed_start_date and parsed_end_date and parsed_end_date <= parsed_start_date:
            return jsonify_response(success=False, message="订阅结束时间必须大于开始时间", status_code=400)
        
        # 批量更新用户订阅时间
        updated_count = 0
        for user_id in user_ids:
            # 查找该用户在此实例中的订阅
            for subscription in instance.user_subscriptions:
                if subscription.user_id == int(user_id):
                    # 只更新提供的字段
                    if parsed_start_date:
                        subscription.start_date = parsed_start_date
                    if parsed_end_date:
                        subscription.end_date = parsed_end_date
                    
                    # 验证更新后的时间逻辑
                    # 确保都转换为时区无感知的日期时间进行比较
                    start_dt = subscription.start_date
                    end_dt = subscription.end_date
                    
                    # 移除时区信息进行比较
                    if start_dt.tzinfo is not None:
                        start_dt = start_dt.replace(tzinfo=None)
                    if end_dt.tzinfo is not None:
                        end_dt = end_dt.replace(tzinfo=None)
                    
                    if end_dt <= start_dt:
                        db.session.rollback()
                        return jsonify_response(
                            success=False, 
                            message=f"用户ID {user_id} 的更新会导致结束时间不晚于开始时间", 
                            status_code=400
                        )
                    
                    updated_count += 1
                    break
        
        db.session.commit()
        
        logger.info(f"[管理员] 批量修改订阅实例 {instance_id} 中 {updated_count} 个用户的订阅时间")
        
        return jsonify_response(
            success=True,
            message=f"成功修改 {updated_count} 个用户的订阅时间",
            data={
                'updated_count': updated_count,
                'instance_id': instance_id
            }
        )
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 批量修改用户订阅时间时发生错误: {str(e)}", exc_info=True)
        return jsonify_response(success=False, message=f"批量修改失败: {str(e)}", status_code=500)

@admin_bp.route('/subscription-types/<int:type_id>', methods=['DELETE'])
@admin_required
def delete_subscription_type(type_id):
    """删除订阅类型
    
    返回:
    {
        "success": true,
        "message": "订阅类型删除成功"
    }
    """
    try:
        admin_user = g.user
        logger.info(f"[管理员] 管理员 {admin_user.id} 请求删除订阅类型 ID: {type_id}")
        
        # 查找订阅类型
        subscription_type = SubscriptionType.query.get(type_id)
        if not subscription_type:
            logger.warning(f"[管理员] 删除不存在的订阅类型 ID: {type_id}")
            return jsonify_response(success=False,
                                    message="未找到指定的订阅类型",
                                    data=None,
                                    status_code=404)
        
        # 检查是否有任何订阅实例关联到此订阅类型
        associated_instances_count = subscription_type.subscription_instances.count()
        if associated_instances_count > 0:
            logger.warning(f"[管理员] 无法删除订阅类型 ID: {type_id}，有 {associated_instances_count} 个订阅实例关联")
            return jsonify_response(success=False,
                                    message=f"无法删除，当前有 {associated_instances_count} 个订阅实例属于此类型",
                                    data=None,
                                    status_code=400)

        # 检查是否有任何用户订阅记录关联到此订阅类型
        associated_subscriptions_count = subscription_type.subscriptions.count()
        if associated_subscriptions_count > 0:
            logger.warning(f"[管理员] 无法删除订阅类型 ID: {type_id}，有 {associated_subscriptions_count} 个用户订阅记录关联")
            return jsonify_response(success=False,
                                    message=f"无法删除，当前有 {associated_subscriptions_count} 个用户订阅记录属于此类型",
                                    data=None,
                                    status_code=400)
        
        # 删除订阅类型
        # code = subscription_type.code # Code is optional and might not be relevant for logging anymore if not used for checks
        db.session.delete(subscription_type)
        db.session.commit()
        
        logger.info(f"[管理员] 管理员 {admin_user.id} 成功删除订阅类型 ID: {type_id}") # Removed code from log
        
        return jsonify_response(success=True,
                                message="订阅类型删除成功",
                                data=None)
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除订阅类型失败: {e}", exc_info=True)
        return jsonify_response(success=False,
                                message=f"删除订阅类型失败: {str(e)}",
                                data=None, # Added data=None
                                status_code=500)

@admin_bp.route('/dashboard/stats', methods=['GET'])
@admin_required
def get_dashboard_stats():
    """获取仪表板综合统计数据
    
    查询参数:
        - range: 时间范围 (today, week, month, year)
        
    返回:
        - 核心业务指标
        - 运营指标
        - 系统健康状态
        - 图表数据
        - 活动日志
        - 智能洞察
    """
    try:
        logger.info(f"[管理员] 管理员 {g.user.id} 请求仪表板统计数据")
        
        # 时间范围参数
        time_range = request.args.get('range', 'month')  # today, week, month, year
        
        # 计算时间范围
        now = datetime.now(timezone.utc)
        if time_range == 'today':
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
        elif time_range == 'week':
            start_time = now - timedelta(days=7)
        elif time_range == 'year':
            start_time = now - timedelta(days=365)
        else:  # month (default)
            start_time = now - timedelta(days=30)
        
        # 基础统计
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        total_devices = Device.query.count()
        
        # 设备使用率 - 已占用的设备槽位占总容量的百分比
        # 计算系统总设备容量（所有AdsPower账号的max_devices之和）
        total_device_capacity = db.session.query(func.sum(AdspowerAccount.max_devices)).filter(
            AdspowerAccount.is_active == True
        ).scalar() or 0
        
        # 计算已使用的设备槽位（Device表中的记录数）
        used_device_slots = Device.query.count()
        
        # 设备使用率 = (已使用槽位 / 总容量) × 100%
        device_usage_rate = (used_device_slots / total_device_capacity * 100) if total_device_capacity > 0 else 0
        
        # 订阅统计 - 基于结束日期判断活跃状态
        active_subscriptions = Subscription.query.filter(
            Subscription.end_date >= now
        ).count()
        
        # 计算本月收入 - 通过关联的支付记录计算
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        monthly_revenue = db.session.query(func.sum(Payment.amount)).join(
            Subscription, Payment.id == Subscription.payment_id
        ).filter(
            Payment.created_at >= month_start,
            Payment.status == 'paid'
        ).scalar() or 0
        
        # 转化率计算
        conversion_rate = (active_subscriptions / total_users * 100) if total_users > 0 else 0
        
        # 留存率（简单计算：最近30天活跃用户占比）
        recent_active_users = User.query.filter(
            User.last_login >= start_time
        ).count()
        retention_rate = (recent_active_users / total_users * 100) if total_users > 0 else 0
        
        
        # 实例容量统计
        instances = SubscriptionInstance.query.all()
        total_instance_capacity = sum(instance.capacity for instance in instances)
        used_instance_capacity = sum(instance.user_subscriptions.count() for instance in instances)
        instance_capacity_rate = (used_instance_capacity / total_instance_capacity * 100) if total_instance_capacity > 0 else 0
        
        # 订阅类型分布
        subscription_distribution = db.session.query(
            SubscriptionType.name,
            func.count(Subscription.id).label('count')
        ).join(
            Subscription, SubscriptionType.id == Subscription.subscription_type_id
        ).filter(
            Subscription.end_date >= now
        ).group_by(SubscriptionType.id).all()
        
        # 用户增长趋势数据（按时间范围）
        growth_data = []
        if time_range == 'today':
            # 按小时统计
            for i in range(24):
                hour_start = now.replace(hour=i, minute=0, second=0, microsecond=0)
                hour_end = hour_start + timedelta(hours=1)
                # 用户数统计
                user_count = User.query.filter(
                    User.created_at >= hour_start,
                    User.created_at < hour_end
                ).count()
                
                # 收入统计
                revenue = db.session.query(func.sum(Payment.amount)).filter(
                    Payment.created_at >= hour_start,
                    Payment.created_at < hour_end,
                    Payment.status == 'paid'
                ).scalar() or 0
                
                # 设备数统计
                device_count = Device.query.filter(
                    Device.created_at >= hour_start,
                    Device.created_at < hour_end
                ).count()
                
                growth_data.append({
                    'label': f"{i}:00",
                    'users': user_count,
                    'revenue': float(revenue),
                    'devices': device_count
                })
        else:
            # 按天统计
            days = 7 if time_range == 'week' else 30 if time_range == 'month' else 365
            for i in range(days):
                day = now - timedelta(days=i)
                day_start = day.replace(hour=0, minute=0, second=0, microsecond=0)
                day_end = day_start + timedelta(days=1)
                # 用户数统计
                user_count = User.query.filter(
                    User.created_at >= day_start,
                    User.created_at < day_end
                ).count()
                
                # 收入统计
                revenue = db.session.query(func.sum(Payment.amount)).filter(
                    Payment.created_at >= day_start,
                    Payment.created_at < day_end,
                    Payment.status == 'paid'
                ).scalar() or 0
                
                # 设备数统计
                device_count = Device.query.filter(
                    Device.created_at >= day_start,
                    Device.created_at < day_end
                ).count()
                
                growth_data.insert(0, {
                    'label': day.strftime('%m-%d'),
                    'users': user_count,
                    'revenue': float(revenue),
                    'devices': device_count
                })
        
        # 最近活动（从最近的用户注册和订阅变更获取）
        recent_activities = []
        
        # 获取最近注册的用户
        recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
        for user in recent_users:
            # 处理时区问题：如果数据库时间是naive的，假定它是UTC
            user_created_at = user.created_at
            if user_created_at.tzinfo is None:
                # 数据库中的时间是naive的，假定它是UTC时间
                user_created_at = user_created_at.replace(tzinfo=timezone.utc)
            time_diff = now - user_created_at
            if time_diff.days > 0:
                time_str = f"{time_diff.days}天前"
            elif time_diff.seconds > 3600:
                time_str = f"{time_diff.seconds // 3600}小时前"
            else:
                time_str = f"{time_diff.seconds // 60}分钟前"
            
            recent_activities.append({
                'time': time_str,
                'text': f'新用户注册: {user.email}',
                'type': 'success',
                'icon': 'bi-person-plus'
            })
        
        # 构建响应数据
        stats_data = {
            'core_metrics': {
                'active_users': active_users,
                'monthly_revenue': float(monthly_revenue),
                'conversion_rate': round(conversion_rate, 1),
                'retention_rate': round(retention_rate, 1)
            },
            'operational_metrics': {
                'total_users': total_users,
                'active_subscriptions': active_subscriptions,
                'device_usage_rate': round(device_usage_rate, 1),
                'instance_capacity_rate': round(instance_capacity_rate, 1)
            },
            'system_health': {
                'status': 'operational',
                'response_time': 85,  # 模拟数据，实际应从监控系统获取
                'error_rate': 0.12,   # 模拟数据，实际应从日志分析获取
                'last_update': now.isoformat()
            },
            'charts': {
                'growth_trend': growth_data,
                'subscription_distribution': [
                    {'label': item[0], 'value': item[1]} 
                    for item in subscription_distribution
                ]
            },
            'activities': recent_activities[:5],  # 最近5条活动
            'insights': []  # 智能洞察
        }
        
        # 生成智能洞察
        insights = []
        if conversion_rate < 50:
            insights.append({
                'icon': 'bi-graph-up',
                'text': f'付费转化率为{conversion_rate:.1f}%，建议优化定价策略或增加营销活动'
            })
        
        if retention_rate < 70:
            insights.append({
                'icon': 'bi-exclamation-circle',
                'text': f'用户活跃率为{retention_rate:.1f}%，建议开展用户激活和留存活动'
            })
        
        if device_usage_rate > 80:
            insights.append({
                'icon': 'bi-laptop',
                'text': '设备使用率较高，建议考虑扩容或优化资源分配'
            })
        
        if not insights:
            insights.append({
                'icon': 'bi-check-circle',
                'text': '系统运行正常，各项指标表现良好'
            })
        
        stats_data['insights'] = insights
        
        return jsonify_response(
            success=True,
            message="获取仪表板统计数据成功",
            data=stats_data
        )
        
    except Exception as e:
        logger.error(f"获取仪表板统计数据失败: {e}", exc_info=True)
        return jsonify_response(
            success=False,
            message=f"获取统计数据失败: {str(e)}",
            data=None,
            status_code=500
        )

@admin_bp.route('/login-sessions/active', methods=['GET'])
@admin_required  
def get_active_login_sessions():
    """获取所有活跃的登录会话"""
    try:
        active_sessions = LoginSession.query.filter(
            LoginSession.completed_time.is_(None),
            LoginSession.expiration_timestamp > datetime.utcnow()
        ).order_by(LoginSession.login_time.desc()).all()
        
        sessions_data = []
        for session in active_sessions:
            # 获取关联的用户和账号信息
            user = User.query.get(session.user_id)
            adspower_account = AdspowerAccount.query.get(session.adspower_account_id) if session.adspower_account_id else None
            
            sessions_data.append({
                'id': session.id,
                'user_id': session.user_id,
                'user_email': user.email if user else 'Unknown',
                'account_id': session.adspower_account_id,
                'account_username': adspower_account.username if adspower_account else 'N/A',
                'status': session.login_status or 'pending',
                'created': session.login_time.isoformat() if session.login_time else None,
                'expires': session.expiration_timestamp.isoformat() if session.expiration_timestamp else None,
                'ip_address': session.ip_address,
                'remaining_seconds': session.get_remaining_seconds()
            })
        
        return jsonify_response(
            success=True,
            message=f"找到 {len(sessions_data)} 个活跃会话",
            data=sessions_data,
            status_code=200
        )
    except Exception as e:
        logger.error(f"获取活跃登录会话时出错: {e}", exc_info=True)
        return jsonify_response(
            success=False,
            message="获取活跃会话失败",
            data=None,
            status_code=500
        )

# 设备审计相关路由
@admin_bp.route('/device-audits', methods=['GET'])
@admin_required
def get_device_audits():
    """获取设备审计日志"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        user_id = request.args.get('user_id', type=int)
        device_id = request.args.get('device_id', type=int)
        action = request.args.get('action')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 转换日期参数
        start_datetime = None
        end_datetime = None
        if start_date:
            try:
                start_datetime = datetime.fromisoformat(start_date)
            except ValueError:
                return jsonify_response(success=False, message="开始日期格式错误", status_code=400)
        
        if end_date:
            try:
                end_datetime = datetime.fromisoformat(end_date)
                # 如果只提供日期，设置为当天结束时间
                if len(end_date) == 10:  # YYYY-MM-DD format
                    end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
            except ValueError:
                return jsonify_response(success=False, message="结束日期格式错误", status_code=400)
        
        # 使用审计服务查询
        from adspower_manager.services.device_audit_service import DeviceAuditService
        audits, total = DeviceAuditService.get_device_audit_logs(
            device_id=device_id,
            user_id=user_id,
            action=action,
            start_date=start_datetime,
            end_date=end_datetime,
            page=page,
            per_page=per_page
        )
        
        # 构建返回数据
        audit_data = []
        for audit in audits:
            # 获取关联信息
            user = User.query.get(audit.user_id)
            operator = User.query.get(audit.operator_id) if audit.operator_id else None
            
            audit_data.append({
                'id': audit.id,
                'device_id': audit.device_id,
                'user_id': audit.user_id,
                'user_email': user.email if user else 'Unknown',
                'operator_id': audit.operator_id,
                'operator_email': operator.email if operator else None,
                'action': audit.action,
                'action_source': audit.action_source,
                'description': audit.description,
                'ip_address': audit.ip_address,
                'user_agent': audit.user_agent,
                'device_snapshot': audit.device_snapshot,
                'old_value': audit.old_value,
                'new_value': audit.new_value,
                'created_at': audit.created_at.isoformat() if audit.created_at else None
            })
        
        return jsonify_response(
            success=True,
            message=f"获取到 {len(audit_data)} 条审计记录",
            data={
                'audits': audit_data,
                'total': total,
                'page': page,
                'per_page': per_page,
                'total_pages': (total + per_page - 1) // per_page
            },
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"获取设备审计日志时出错: {e}", exc_info=True)
        return jsonify_response(
            success=False,
            message=f"获取审计日志失败: {str(e)}",
            status_code=500
        )

@admin_bp.route('/device-audits/device-changes', methods=['GET'])
@admin_required
def get_device_change_statistics():
    """获取设备更换次数统计"""
    try:
        # 获取查询参数
        days = request.args.get('days', 30, type=int)
        limit = request.args.get('limit', 20, type=int)
        
        # 使用审计服务查询
        from adspower_manager.services.device_audit_service import DeviceAuditService
        statistics = DeviceAuditService.get_device_change_statistics(
            days=days,
            limit=limit
        )
        
        return jsonify_response(
            success=True,
            message=f"获取到前 {len(statistics)} 个设备更换次数最多的用户",
            data={
                'statistics': statistics,
                'query_params': {
                    'days': days,
                    'limit': limit
                }
            },
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"获取设备更换统计时出错: {e}", exc_info=True)
        return jsonify_response(
            success=False,
            message=f"获取设备更换统计失败: {str(e)}",
            status_code=500
        )

@admin_bp.route('/device-audits/user/<int:user_id>/statistics', methods=['GET'])
@admin_required
def get_user_device_statistics(user_id):
    """获取指定用户的设备活动统计"""
    try:
        # 获取查询参数
        days = request.args.get('days', 30, type=int)
        
        # 获取用户信息
        user = User.query.get(user_id)
        if not user:
            return jsonify_response(success=False, message="用户不存在", status_code=404)
        
        # 使用审计服务查询
        from adspower_manager.services.device_audit_service import DeviceAuditService
        statistics = DeviceAuditService.get_user_device_activities(
            user_id=user_id,
            days=days
        )
        
        # 添加用户信息
        statistics['user'] = {
            'id': user.id,
            'email': user.email,
            'created_at': user.created_at.isoformat() if user.created_at else None
        }
        statistics['query_days'] = days
        
        return jsonify_response(
            success=True,
            message="获取用户设备活动统计成功",
            data=statistics,
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"获取用户设备活动统计时出错: {e}", exc_info=True)
        return jsonify_response(
            success=False,
            message=f"获取统计数据失败: {str(e)}",
            status_code=500
        )

@admin_bp.route('/device-audits/user/<int:user_id>/device-details', methods=['GET'])
@admin_required
def get_user_device_details(user_id):
    """获取用户设备使用详情"""
    try:
        # 获取查询参数
        days = request.args.get('days', 30, type=int)
        
        # 获取用户信息
        user = User.query.get(user_id)
        if not user:
            return jsonify_response(success=False, message="用户不存在", status_code=404)
        
        # 使用审计服务查询
        from adspower_manager.services.device_audit_service import DeviceAuditService
        details = DeviceAuditService.get_user_device_details(
            user_id=user_id,
            days=days
        )
        
        # 添加用户信息
        details['user'] = {
            'id': user.id,
            'email': user.email,
            'created_at': user.created_at.isoformat() if user.created_at else None
        }
        
        return jsonify_response(
            success=True,
            message="获取用户设备详情成功",
            data=details,
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"获取用户设备详情时出错: {e}", exc_info=True)
        return jsonify_response(
            success=False,
            message=f"获取设备详情失败: {str(e)}",
            status_code=500
        )


# ===== 兑换码管理API =====

@admin_bp.route('/redemption-codes', methods=['GET'])
@admin_required
def get_redemption_codes():
    """(Admin) 获取兑换码列表"""
    from ..services.redemption_service import RedemptionService

    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        created_by = request.args.get('created_by', type=int)
        is_used = request.args.get('is_used', type=bool)

        result = RedemptionService.get_redemption_codes(
            created_by=created_by,
            is_used=is_used,
            page=page,
            per_page=per_page
        )

        return jsonify_response(success=True, message="获取兑换码列表成功", data=result, status_code=200)

    except Exception as e:
        logger.exception(f"[管理员] 获取兑换码列表时发生错误: {e}")
        return jsonify_response(success=False, message=f"获取兑换码列表失败: {str(e)}", status_code=500)


@admin_bp.route('/redemption-codes', methods=['POST'])
@admin_required
def create_redemption_code():
    """(Admin) 创建兑换码"""
    from ..services.redemption_service import RedemptionService
    from datetime import datetime, timedelta

    try:
        data = request.json
        value = data.get('value')
        count = data.get('count', 1)
        expires_days = data.get('expires_days')
        description = data.get('description', '').strip()

        # 验证参数
        if not value or value <= 0:
            return jsonify_response(success=False, message="兑换金额必须大于0", status_code=400)

        if count <= 0 or count > 100:
            return jsonify_response(success=False, message="创建数量必须在1-100之间", status_code=400)

        # 计算过期时间
        expires_at = None
        if expires_days and expires_days > 0:
            expires_at = datetime.utcnow() + timedelta(days=expires_days)

        if count == 1:
            # 创建单个兑换码
            success, message, code = RedemptionService.create_redemption_code(
                value=value,
                created_by=g.user.id,
                expires_at=expires_at,
                description=description
            )

            if success:
                return jsonify_response(
                    success=True,
                    message=message,
                    data={'code': code.to_dict() if code else None},
                    status_code=200
                )
            else:
                return jsonify_response(success=False, message=message, status_code=400)
        else:
            # 批量创建兑换码
            success, message, codes = RedemptionService.batch_create_redemption_codes(
                value=value,
                count=count,
                created_by=g.user.id,
                expires_at=expires_at,
                description=description
            )

            return jsonify_response(
                success=success,
                message=message,
                data={'codes': [code.to_dict() for code in codes], 'count': len(codes)},
                status_code=200 if success else 400
            )

    except Exception as e:
        logger.exception(f"[管理员] 创建兑换码时发生错误: {e}")
        return jsonify_response(success=False, message=f"创建兑换码失败: {str(e)}", status_code=500)


@admin_bp.route('/redemption-codes/<int:code_id>/disable', methods=['POST'])
@admin_required
def disable_redemption_code(code_id):
    """(Admin) 禁用兑换码"""
    from ..services.redemption_service import RedemptionService

    try:
        success, message = RedemptionService.disable_redemption_code(code_id, g.user.id)

        return jsonify_response(
            success=success,
            message=message,
            status_code=200 if success else 400
        )

    except Exception as e:
        logger.exception(f"[管理员] 禁用兑换码 {code_id} 时发生错误: {e}")
        return jsonify_response(success=False, message=f"禁用兑换码失败: {str(e)}", status_code=500)


# ===== 余额管理API =====

@admin_bp.route('/users/<int:user_id>/balance', methods=['GET'])
@admin_required
def get_user_balance(user_id):
    """(Admin) 获取用户余额"""
    from ..services.balance_service import BalanceService

    try:
        balance = BalanceService.get_user_balance(user_id)
        if balance is None:
            return jsonify_response(success=False, message="用户不存在或获取余额失败", status_code=404)

        return jsonify_response(
            success=True,
            message="获取用户余额成功",
            data={'user_id': user_id, 'balance': float(balance)},
            status_code=200
        )

    except Exception as e:
        logger.exception(f"[管理员] 获取用户 {user_id} 余额时发生错误: {e}")
        return jsonify_response(success=False, message=f"获取用户余额失败: {str(e)}", status_code=500)


@admin_bp.route('/users/<int:user_id>/balance/adjust', methods=['POST'])
@admin_required
def adjust_user_balance(user_id):
    """(Admin) 调整用户余额"""
    from ..services.balance_service import BalanceService

    try:
        data = request.json
        amount = data.get('amount')
        description = data.get('description', '').strip()

        if amount is None or amount == 0:
            return jsonify_response(success=False, message="调整金额不能为0", status_code=400)

        if not description:
            return jsonify_response(success=False, message="请填写调整原因", status_code=400)

        success, message, new_balance = BalanceService.admin_adjust_balance(
            user_id=user_id,
            amount=Decimal(str(amount)),
            description=description,
            operator_id=g.user.id
        )

        if success:
            return jsonify_response(
                success=True,
                message=message,
                data={'user_id': user_id, 'new_balance': float(new_balance) if new_balance else None},
                status_code=200
            )
        else:
            return jsonify_response(success=False, message=message, status_code=400)

    except Exception as e:
        logger.exception(f"[管理员] 调整用户 {user_id} 余额时发生错误: {e}")
        return jsonify_response(success=False, message=f"调整用户余额失败: {str(e)}", status_code=500)


@admin_bp.route('/users/<int:user_id>/balance/transactions', methods=['GET'])
@admin_required
def get_user_balance_transactions(user_id):
    """(Admin) 获取用户余额变动记录"""
    from ..services.balance_service import BalanceService

    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)

        result = BalanceService.get_balance_transactions(user_id, page, per_page)

        return jsonify_response(success=True, message="获取用户余额记录成功", data=result, status_code=200)

    except Exception as e:
        logger.exception(f"[管理员] 获取用户 {user_id} 余额记录时发生错误: {e}")
        return jsonify_response(success=False, message=f"获取用户余额记录失败: {str(e)}", status_code=500)
