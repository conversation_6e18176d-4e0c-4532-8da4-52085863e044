import requests
import hashlib
import json
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import secrets
import logging
import pyotp
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


class KeyGenerator:
    def __init__(self):
        # 使用固定的 2048 bit 大素数 p
        self._p = int("81d6781a3ebbed1e39cb83597ee7242918c5e0746a30b8359fb6ddd052a3627c5473fcd9649ce66c120563e36cada9f27be060590a4aee2710162f37f6e6cc58c0d0bab183ccc6de1b74198b6e2d2d15c04e97584d8c9c167b50529ba00f5fa67e60d93c6d7f57853b7d091d58b86fa2dacadaaf75f79ef878ad9e199240efac926045bc4f158630f19917cb11888d9d4d7d0256564633f9c2188f98c867c3c4055eca93a6239454be0334809ed965c7db8d6be71db2319ad3de739fd473ef23aa609eebc973994f54aef1326e236adae90daed89219e1698759ed307563fb3963199a111a13050e547df81bb6027d9baee2becbf53e7777bdc16d665c261003", 16)
        self.pvt = secrets.randbits(256)  # 生成私钥
        self.pub = pow(2, self.pvt, self._p)  # 计算公钥: g^pvt mod p (g=2)
        self._shared = ""  # 共享密钥

    def generate_shared(self, a_hex):
        """计算共享密钥"""
        a_int = int(a_hex, 16)
        s = pow(a_int, self.pvt, self._p)  # a^pvt mod p
        s_hex = hex(s)[2:].zfill(512)[:512]  # 补齐到512字符
        self._shared = hashlib.sha256(s_hex.encode()).hexdigest()
        return self._shared

    def pub_hex(self):
        """获取公钥的16进制表示"""
        return hex(self.pub)[2:].zfill(512)

    def encrypt(self, data):
        """使用AES加密数据"""
        if not self._shared:
            raise RuntimeError("共享密钥未生成")
        
        # 如果是空对象或None，返回空字符串
        if data is None or (isinstance(data, dict) and len(data) == 0):
            return ""
            
        # 如果是字典或列表，转换为JSON字符串
        if isinstance(data, dict) or isinstance(data, list):
            raw = json.dumps(data, separators=(',', ':')).encode()
        elif isinstance(data, str):
            raw = data.encode()
        else:
            raw = str(data).encode()
        
        # 使用共享密钥的前16个字符作为AES密钥和IV
        key = self._shared[:16].encode()
        cipher = AES.new(key, AES.MODE_CBC, iv=key)
        # 对数据进行填充并加密
        encrypted = cipher.encrypt(pad(raw, AES.block_size))
        # 返回Base64编码的加密数据
        return base64.b64encode(encrypted).decode()

    def decrypt(self, cipher_b64):
        """使用AES解密数据"""
        if not self._shared:
            raise RuntimeError("共享密钥未生成")
        
        # 处理空字符串的情况
        if not cipher_b64:
            return ""
            
        # 使用共享密钥的后16个字符作为AES密钥和IV (注意与加密的区别)
        key = self._shared[-16:].encode()
        cipher = AES.new(key, AES.MODE_CBC, iv=key)
        # 解码Base64，解密，并去除填充
        encrypted_data = base64.b64decode(cipher_b64)
        decrypted_padded = cipher.decrypt(encrypted_data)
        decrypted = unpad(decrypted_padded, AES.block_size)
        # 返回解密后的字符串
        return decrypted.decode('utf-8')


class AdsPowerSession:
    """AdsPower协议会话管理类"""
    
    def __init__(self, cookies=None, api_base="https://api.adspower.net"):
        self.sess = requests.Session()
        self.kg = None
        self.web_uuid = None
        self.cpl = None
        self.api_base = api_base.rstrip('/')  # 确保没有尾部斜杠
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://app.adspower.net',
            'Pragma': 'no-cache',
            'Referer': 'https://app.adspower.net/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
        }
        
        # 设置cookies
        self.cookies = cookies or {
            '__SYS_ID': '0f372fe58ee58623c2b79b3dfafe7d21',
        }
        
        # 会话状态
        self.is_logged_in = False
        self.account_info = {}
        
    def get_ip_address(self):
        """从AdsParser API获取IP地址"""
        try:
            response = self.sess.get(
                f"{self.api_base}/sys/config/ip/get-ip",
                headers=self.headers,
                cookies=self.cookies
            )
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0 and 'ip' in data.get('data', {}):
                    return data['data']['ip']
            # 如果获取失败，返回默认IP
            return "**************"
        except Exception as e:
            logger.error(f"获取IP地址失败: {e}", exc_info=True)
            # 发生异常时返回默认IP
            return "**************"
    
    def init(self):
        """初始化会话，获取密钥"""
        # 获取服务器公钥和webUuid
        response = self.sess.get(
            f"{self.api_base}/sys/config/fe/config",
            headers=self.headers,
            cookies=self.cookies
        ).json()
        
        if response.get("code") != 0:
            raise Exception(f"获取配置失败: {response}")
            
        server_public_key = response["data"]["a"]
        self.web_uuid = response["data"]["b"]
        
        # 生成密钥对并计算共享密钥
        self.kg = KeyGenerator()
        self.kg.generate_shared(server_public_key)
        
        return True
    
    def login(self, login_name, password, remember=0, language="zh-CN", c_id="164461aa", ip_address=None,
              validate_code=None, validate_type=None, uid=None):
        """使用会话登录，支持二次验证"""
        # 确保已初始化
        if not self.kg:
            self.init()
        
        # 计算密码的MD5哈希
        if len(password) != 32 or any(c not in '0123456789abcdef' for c in password.lower()):
            # 如果不是32位16进制字符串，认为是原始密码，需要计算MD5
            password_md5 = hashlib.md5(password.encode()).hexdigest()
        else:
            # 如果已经是32位16进制字符串，可能已经是MD5，直接使用
            password_md5 = password
        
        # 获取IP地址，如果没有提供则从API获取
        if ip_address is None:
            ip_address = self.get_ip_address()
        
        # 准备登录数据
        login_data = {
            "login_name": login_name,
            "password": password_md5,
            "remember": remember,
            "language": language,
            "c_id": c_id,
            "wr": ip_address
        }
        
        # 如果提供了二次验证信息，添加到登录数据中
        if validate_code and validate_type and uid:
            login_data.update({
                "validate_code": validate_code,
                "validate_type": validate_type,
                "uid": uid
            })
        
        # 加密登录数据
        encrypted_login_data = self.kg.encrypt(login_data)
        
        # 准备请求数据
        request_data = {
            'a': self.web_uuid,
            'b': self.kg.pub_hex(),
            'c': encrypted_login_data
        }
        
        # 发送登录请求
        login_response = self.sess.post(
            f"{self.api_base}/sys/user/passport/login-v3",
            cookies=self.cookies,
            headers=self.headers,
            data=request_data
        )
        
        # 获取响应JSON
        response_json = login_response.json()
        result = {"response": response_json}
        
        # 检查是否需要二次验证
        if response_json.get('code') == 4033 and response_json.get('msg') == 'need twice verify':
            result["needs_verification"] = True
            # 如果有'd'字段，解密获取更多信息
            if 'd' in response_json.get('data', {}):
                try:
                    encrypted_data = response_json['data']['d']
                    # 处理空字符串的情况
                    if not encrypted_data:
                        result["decrypted"] = {}
                    else:
                        decrypted_data = self.kg.decrypt(encrypted_data)
                        
                        # 尝试解析为JSON
                        try:
                            decrypted_json = json.loads(decrypted_data)
                            result["decrypted"] = decrypted_json
                            # 提取验证所需的用户ID
                            if 'uid' in decrypted_json:
                                result["uid"] = decrypted_json['uid']
                            elif 'twice_valid_info' in decrypted_json and 'uid' in decrypted_json['twice_valid_info']:
                                result["uid"] = decrypted_json['twice_valid_info']['uid']
                        except json.JSONDecodeError:
                            # 如果不是有效的JSON，返回原始解密字符串
                            result["decrypted"] = decrypted_data
                except Exception as e:
                    result["decryption_error"] = str(e)
        # 如果登录成功或其他情况
        elif 'd' in response_json.get('data', {}):
            try:
                encrypted_data = response_json['data']['d']
                # 处理空字符串的情况
                if not encrypted_data:
                    result["decrypted"] = {}
                else:
                    decrypted_data = self.kg.decrypt(encrypted_data)
                    
                    # 尝试解析为JSON
                    try:
                        decrypted_json = json.loads(decrypted_data)
                        result["decrypted"] = decrypted_json
                        
                        # 如果登录成功并返回了cpl，保存下来
                        if 'cpl' in decrypted_json:
                            self.cpl = decrypted_json['cpl']
                            self.headers['Cpl'] = self.cpl
                            result["cpl"] = self.cpl
                            self.is_logged_in = True
                            self.account_info = decrypted_json
                            
                            # 调用set-sys-info初始化
                            sys_info_result = self._init_with_set_sys_info()
                            result["set_sys_info"] = sys_info_result
                    except json.JSONDecodeError:
                        # 如果不是有效的JSON，返回原始解密字符串
                        result["decrypted"] = decrypted_data
            except Exception as e:
                result["decryption_error"] = str(e)
        
        return result
    
    def _init_with_set_sys_info(self):
        """初始化会话，调用set-sys-info"""
        logger.debug("调用 set-sys-info 接口...")
        
        if self.cpl:
            self.headers['Cpl'] = self.cpl
        
        # 对于空对象，直接使用空字符串而不是加密
        c_cipher = ""
        if self.kg is not None:
            c_cipher = self.kg.encrypt({"device_info": ""})  # 将返回空字符串
            
        payload = {"a": self.web_uuid, "b": self.kg.pub_hex(), "c": c_cipher}
        
        r = self.sess.post(
            f"{self.api_base}/sys/user/passport/set-sys-info",
            data=payload,
            headers=self.headers,
            cookies=self.cookies
        ).json()
        
        logger.debug(f"set-sys-info 响应: {r}")
        
        if r.get("code") == 0 and "d" in r.get("data", {}):
            try:
                encrypted_data = r["data"]["d"]
                # 处理空字符串的情况
                if not encrypted_data:
                    return {"response": r, "decrypted": {}}
                else:
                    plain = self.kg.decrypt(encrypted_data)
                    return {"response": r, "decrypted": plain}
            except Exception as e:
                logger.error(f"set-sys-info 解密失败: {e}", exc_info=True)
                return {"response": r, "error": f"解密失败: {e}"}
        
        return {"response": r}
    
    def call_api(self, api_path, data=None):
        """调用API并自动处理加密解密"""
        if not self.kg:
            self.init()
        
        # 如果没有数据，默认使用空对象
        if data is None:
            data = {}
        
        # 确保api_path以/开头
        if not api_path.startswith('/'):
            api_path = '/' + api_path
            
        # 如果是空对象，则直接使用空字符串
        c_value = ""
        if data and len(data) > 0:
            # 只有非空对象才加密
            c_value = self.kg.encrypt(data)
        
        # 准备请求数据
        request_data = {
            'a': self.web_uuid,
            'b': self.kg.pub_hex(),
            'c': c_value
        }
        
        # 发送API请求
        response = self.sess.post(
            f"{self.api_base}{api_path}",
            data=request_data,
            headers=self.headers,
            cookies=self.cookies
        ).json()
        
        result = {"response": response}
        
        # 如果响应中包含加密数据，尝试解密
        if response.get("code") == 0 and 'd' in response.get('data', {}):
            try:
                encrypted_data = response['data']['d']
                # 处理空字符串的情况
                if not encrypted_data:
                    result["decrypted"] = {}
                else:
                    decrypted_data = self.kg.decrypt(encrypted_data)
                    
                    # 尝试解析为JSON
                    try:
                        decrypted_json = json.loads(decrypted_data)
                        result["decrypted"] = decrypted_json
                    except json.JSONDecodeError:
                        # 如果不是有效的JSON，返回原始解密字符串
                        result["decrypted"] = decrypted_data
            except Exception as e:
                result["decryption_error"] = str(e)
        
        return result
    
    def get_login_info(self):
        """获取登录信息
        
        调用 /sys/user/profile/get-login-info-v2 接口，获取当前登录用户的信息
        
        Returns:
            包含响应和解密数据的字典
        """
        logger.info("获取登录信息")
        return self.call_api("/sys/user/profile/get-login-info-v2", {})

    def get_all_devices(self):
        """获取所有设备"""
        logger.info("获取所有设备列表")
        result = self.call_api("/sys/user/device/get-all", {})
        
        # 处理特殊情况：设备数据可能在 'my_device' 字段中
        if result.get("response", {}).get("code") == 0 and "decrypted" in result:
            decrypted = result["decrypted"]
            if isinstance(decrypted, dict):
                # 如果数据在 'my_device' 字段中，将其移动到 'devices' 字段
                if "my_device" in decrypted and "devices" not in decrypted:
                    decrypted["devices"] = decrypted["my_device"]
                    # 确保是列表
                    if not isinstance(decrypted["devices"], list):
                        decrypted["devices"] = [decrypted["devices"]]
        
        return result
    
    def revoke_device(self, revoke_id):
        """撤销指定ID的设备"""
        logger.info(f"撤销设备 {revoke_id}")
        revoke_data = {"revoke_id": revoke_id}
        return self.call_api("/sys/user/device/revoke", revoke_data)
    
    def get_cookies(self):
        """获取当前会话的cookies"""
        return self.sess.cookies.get_dict()
    
    def set_cookies(self, cookies):
        """设置会话cookies"""
        if isinstance(cookies, dict):
            for name, value in cookies.items():
                self.sess.cookies.set(name, value)
        elif isinstance(cookies, list):
            for cookie in cookies:
                if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                    self.sess.cookies.set(cookie['name'], cookie['value'])


def parse_device_info(devices_data: List[Dict]) -> List[Dict]:
    """解析设备信息，转换为统一格式
    
    Args:
        devices_data: 从API获取的设备列表数据
        
    Returns:
        解析后的设备信息列表
    """
    parsed_devices = []
    
    for device in devices_data:
        # 解析设备类型，从ua_os_system获取
        device_type = 'Unknown'
        ua_os = device.get('ua_os_system', '').lower()
        
        if 'windows' in ua_os:
            device_type = 'Windows'
        elif 'mac' in ua_os:
            device_type = 'Mac'
        elif 'android' in ua_os:
            device_type = 'Android'
        elif 'ios' in ua_os or 'iphone' in ua_os or 'ipad' in ua_os:
            device_type = 'iOS'
        elif 'linux' in ua_os:
            device_type = 'Linux'
        
        # 生成设备名称（如果为空）
        device_name = device.get('device_name', '')
        if not device_name:
            # 使用操作系统和浏览器信息生成名称
            os_name = device.get('ua_os_system', 'Unknown OS')
            browser_name = device.get('ua_browser', 'Unknown Browser')
            device_name = f"{os_name} - {browser_name}"
        
        parsed_device = {
            'id': device.get('device_id', device.get('id')),  # 优先使用device_id
            'device_name': device_name,
            'name': device_name,  # 为了兼容check-login-status接口
            'device_type': device_type,
            'device_ip': device.get('online_ip', device.get('ip', '')),  # 优先使用online_ip
            'ip_address': device.get('online_ip', device.get('ip', '')),  # 兼容旧字段名
            'login_time': device.get('online_time', device.get('last_login_time')),
            'is_current': device.get('is_current', False),
            'raw_data': device  # 保留原始数据
        }
            
        parsed_devices.append(parsed_device)
    
    return parsed_devices


class AdsPowerProtocolAdapter:
    """协议适配器，提供与原AdspowerAPI兼容的接口"""
    
    def __init__(self, base_url="https://app-global.adspower.net"):
        self.base_url = base_url
        self.api_base = base_url.replace("app", "api")  # 转换为API URL
        if "app-global" in base_url:
            self.api_base = "https://api-global.adspower.net"
        self.sessions = {}  # account_id -> AdsPowerSession
        
    def get_session(self, account_id: str) -> AdsPowerSession:
        """获取或创建账号会话"""
        if account_id not in self.sessions:
            self.sessions[account_id] = AdsPowerSession(api_base=self.api_base)
        return self.sessions[account_id]
    
    def login_account(self, account) -> Tuple[bool, str, Optional[List[Dict]]]:
        """登录账号
        
        Args:
            account: AdspowerAccount对象
            
        Returns:
            (success, message, cookies)
        """
        session = self.get_session(str(account.id))
        
        try:
            # 执行登录
            result = session.login(
                login_name=account.username,
                password=account.password
            )
            logger.debug(f"直接登录结果: {result}")
            # 检查是否需要二次验证
            if result.get("needs_verification"):
                if account.totp_secret:
                    # 生成TOTP验证码
                    totp = pyotp.TOTP(account.totp_secret)
                    code = totp.now()
                    
                    # 重新登录with验证码
                    uid = result.get("uid") or (result.get("decrypted", {}).get("twice_valid_info", {}).get("uid"))
                    if uid:
                        result = session.login(
                            login_name=account.username,
                            password=account.password,
                            validate_code=code,
                            validate_type="sercet", # 这里一定是sercet 不要修改
                            uid=uid
                        )
                    else:
                        return False, "无法获取用户ID进行二次验证", None
                else:
                    return False, "账号需要二次验证但未配置TOTP密钥", None
            
            # 检查登录结果
            if result.get("response", {}).get("code") == 0:
                # 获取cookies
                cookies = []
                for name, value in session.get_cookies().items():
                    cookies.append({
                        'name': name,
                        'value': value,
                        'domain': '.adspower.net',
                        'path': '/'
                    })
                return True, "登录成功", cookies
            else:
                error_msg = result.get("response", {}).get("msg", "登录失败")
                return False, error_msg, None
                
        except Exception as e:
            logger.error(f"协议登录账号 {account.username} 时出错: {e}", exc_info=True)
            return False, f"登录异常: {str(e)}", None
    
    def get_devices_info(self, account, retry_on_failure=True) -> Optional[List[Dict]]:
        """获取账号的设备列表
        
        Args:
            account: AdspowerAccount对象
            retry_on_failure: 在失败时是否重试（重新登录）
            
        Returns:
            设备信息列表或None
        """
        session = self.get_session(str(account.id))
        
        # 确保已登录
        if not session.is_logged_in:
            success, msg, cookies = self.login_account(account)
            if not success:
                logger.error(f"获取设备列表前登录失败: {msg}")
                return None
            # 如果登录成功且返回了cookies，更新到账号对象
            if success and cookies:
                self._update_account_cookies(account, cookies)
        
        try:
            result = session.get_all_devices()
            
            if result.get("response", {}).get("code") == 0:
                devices_data = result.get("decrypted", {}).get("other_device", [])
                return parse_device_info(devices_data)
            else:
                error_msg = result.get('response', {}).get('msg', '未知错误')
                logger.error(f"获取设备列表失败: {error_msg}")
                
                # 如果启用重试且可能是会话过期或在其他设备登录
                if retry_on_failure and self._should_retry_login(error_msg):
                    logger.info(f"检测到需要重新登录的情况: {error_msg}")
                    logger.info("正在尝试重新登录...")
                    
                    # 清除当前会话状态
                    session.is_logged_in = False
                    session.cpl = None  # 清除会话令牌
                    
                    # 重新登录
                    success, msg, cookies = self.login_account(account)
                    if success:
                        logger.info("重新登录成功，再次尝试获取设备列表")
                        if cookies:
                            self._update_account_cookies(account, cookies)
                        # 递归调用，但禁用重试以避免无限循环
                        return self.get_devices_info(account, retry_on_failure=False)
                    else:
                        logger.error(f"重新登录失败: {msg}")
                
                return None
                
        except Exception as e:
            logger.error(f"协议获取设备列表时出错: {e}", exc_info=True)
            
            # 如果是网络或连接错误，也可以尝试重试
            if retry_on_failure and isinstance(e, (requests.exceptions.RequestException,)):
                logger.info("网络错误，尝试重新登录...")
                session.is_logged_in = False
                success, msg, cookies = self.login_account(account)
                if success:
                    if cookies:
                        self._update_account_cookies(account, cookies)
                    return self.get_devices_info(account, retry_on_failure=False)
            
            return None
    
    def logout_device(self, account, device_id: str) -> Tuple[bool, str]:
        """退出指定设备
        
        Args:
            account: AdspowerAccount对象
            device_id: 设备唯一标识符
            
        Returns:
            (success, message)
        """
        if not device_id:
            return False, "设备ID不能为空"
            
        session = self.get_session(str(account.id))
        
        # 确保已登录
        if not session.is_logged_in:
            success, msg, _ = self.login_account(account)
            if not success:
                return False, f"退出设备前登录失败: {msg}"
        
        try:
            logger.info(f"使用device_id退出设备: {device_id}")
            result = session.revoke_device(device_id)
            
            if result.get("response", {}).get("code") == 0:
                return True, f"成功退出设备 (ID: {device_id})"
            else:
                error_msg = result.get("response", {}).get("msg", "退出失败")
                return False, error_msg
                
        except Exception as e:
            logger.error(f"协议退出设备时出错: {e}", exc_info=True)
            return False, f"退出设备异常: {str(e)}"
    
    def check_account_validity(self, account) -> bool:
        """检查账号有效性
        
        Args:
            account: AdspowerAccount对象
            
        Returns:
            账号是否有效
        """
        success, _, _ = self.login_account(account)
        return success
    
    def check_account_health(self, account) -> Tuple[bool, str, Optional[Dict]]:
        """检查账号健康状态
        
        通过获取登录信息来判断账号是否健康，如果不健康则尝试重新登录
        
        Args:
            account: AdspowerAccount对象
            
        Returns:
            (is_healthy, status_message, login_info)
            - is_healthy: 账号是否健康
            - status_message: 状态消息
            - login_info: 登录信息（如果健康）
        """
        session = self.get_session(str(account.id))
        
        try:
            # 首先尝试获取登录信息
            logger.info(f"检查账号 {account.username} 的健康状态")
            result = session.get_login_info()
            
            # 检查响应
            if result.get("response", {}).get("code") == 0:
                # 成功获取登录信息，账号健康
                login_info = result.get("decrypted", {})
                logger.info(f"账号 {account.username} 健康，用户信息: {login_info.get('user_name', 'N/A')}")
                return True, "账号健康", login_info
            else:
                # 获取登录信息失败，可能需要重新登录
                error_msg = result.get("response", {}).get("msg", "未知错误")
                logger.warning(f"账号 {account.username} 获取登录信息失败: {error_msg}")
                
                # 判断是否需要重新登录
                if self._should_retry_login(error_msg):
                    logger.info(f"账号 {account.username} 需要重新登录")
                    
                    # 清除会话状态
                    session.is_logged_in = False
                    session.cpl = None
                    
                    # 尝试重新登录
                    success, msg, cookies = self.login_account(account)
                    if success:
                        logger.info(f"账号 {account.username} 重新登录成功")
                        if cookies:
                            self._update_account_cookies(account, cookies)
                        
                        # 再次尝试获取登录信息
                        result = session.get_login_info()
                        if result.get("response", {}).get("code") == 0:
                            login_info = result.get("decrypted", {})
                            return True, "重新登录后恢复健康", login_info
                        else:
                            return False, "重新登录后仍无法获取登录信息", None
                    else:
                        logger.error(f"账号 {account.username} 重新登录失败: {msg}")
                        return False, f"账号危险：{msg}", None
                else:
                    # 不需要重新登录的错误
                    return False, f"账号异常：{error_msg}", None
                    
        except Exception as e:
            logger.error(f"检查账号 {account.username} 健康状态时出错: {e}", exc_info=True)
            return False, f"健康检查异常：{str(e)}", None
    
    def _update_account_cookies(self, account, cookies):
        """更新账号的cookies到数据库
        
        Args:
            account: AdspowerAccount对象
            cookies: Cookie列表
        """
        try:
            if cookies and hasattr(account, 'cookies'):
                # 将cookies转换为JSON字符串
                cookies_str = json.dumps(cookies)
                account.cookies = cookies_str
                
                # 获取数据库会话并提交
                from sqlalchemy.orm import object_session
                session = object_session(account)
                if session:
                    session.add(account)
                    session.commit()
                    logger.info(f"已更新账号 {account.username} 的cookies到数据库")
                else:
                    logger.warning(f"无法获取账号 {account.username} 的数据库会话，cookies未保存")
        except Exception as e:
            logger.error(f"更新账号cookies时出错: {e}", exc_info=True)
    
    def _should_retry_login(self, error_msg: str) -> bool:
        """判断是否应该重新登录
        
        Args:
            error_msg: 错误消息
            
        Returns:
            是否需要重新登录
        """
        # 需要重新登录的关键词
        retry_keywords = [
            "登录",  # 中文登录
            "log in",  # 英文登录
            "login",
            "auth",
            "session",
            "another device",  # 在其他设备登录
            "logged in on another",
            "please log in again",  # 请重新登录
            "expired",  # 过期
            "invalid",  # 无效
            "unauthorized"  # 未授权
        ]
        
        # 检查错误消息中是否包含关键词
        error_msg_lower = error_msg.lower()
        for keyword in retry_keywords:
            if keyword.lower() in error_msg_lower:
                logger.debug(f"匹配到重试关键词: {keyword}")
                return True
        
        return False