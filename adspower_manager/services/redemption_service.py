"""
兑换码服务模块
处理兑换码的生成、验证、使用等功能
"""

import random
import string
from datetime import datetime, timedelta
from typing import Optional, Tuple, List
from decimal import Decimal

from flask import current_app
from sqlalchemy.exc import IntegrityError

from ..models import db, RedemptionCode, User
from ..utils import logger
from ..utils.i18n import t


class RedemptionService:
    """兑换码服务类"""
    
    # 兑换码字符集（排除易混淆字符）
    CODE_CHARSET = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
    CODE_PREFIX = 'GPT'
    CODE_LENGTH = 8  # 不包括前缀的长度
    
    @classmethod
    def generate_code(cls) -> str:
        """生成兑换码
        
        Returns:
            str: 生成的兑换码
        """
        # 生成随机字符串
        random_part = ''.join(random.choices(cls.CODE_CHARSET, k=cls.CODE_LENGTH))
        return f"{cls.CODE_PREFIX}-{random_part}"
    
    @classmethod
    def create_redemption_code(cls, value: float, created_by: int, 
                             expires_at: Optional[datetime] = None,
                             description: Optional[str] = None) -> Tuple[bool, str, Optional[RedemptionCode]]:
        """创建兑换码
        
        Args:
            value: 兑换金额
            created_by: 创建者用户ID
            expires_at: 过期时间（可选）
            description: 描述（可选）
            
        Returns:
            Tuple[bool, str, Optional[RedemptionCode]]: (成功标志, 消息, 兑换码对象)
        """
        try:
            # 验证创建者
            creator = User.query.get(created_by)
            if not creator:
                return False, t('user_not_found'), None

            if not creator.is_admin:
                return False, t('permission_denied'), None

            # 验证金额
            if value <= 0:
                return False, t('validation_error'), None
            
            # 生成唯一兑换码
            max_attempts = 10
            for attempt in range(max_attempts):
                code = cls.generate_code()
                
                # 检查是否已存在
                existing = RedemptionCode.query.filter_by(code=code).first()
                if not existing:
                    break
            else:
                return False, t('redemption_code_generation_failed'), None
            
            # 创建兑换码记录
            redemption_code = RedemptionCode(
                code=code,
                value=Decimal(str(value)),
                is_used=False,
                created_by=created_by,
                expires_at=expires_at,
                description=description,
                created_at=datetime.utcnow()
            )
            
            db.session.add(redemption_code)
            db.session.commit()
            
            logger.info(f"[兑换码创建] 管理员 {created_by} 创建兑换码 {code}，金额: {value}元")
            return True, t('redemption_code_generated'), redemption_code

        except IntegrityError as e:
            db.session.rollback()
            logger.error(f"[兑换码创建] 数据库完整性错误: {str(e)}")
            return False, t('redemption_code_generation_failed'), None
        except Exception as e:
            db.session.rollback()
            logger.error(f"[兑换码创建] 创建兑换码时发生错误: {str(e)}", exc_info=True)
            return False, t('redemption_code_generation_failed'), None
    
    @classmethod
    def batch_create_redemption_codes(cls, value: float, count: int, created_by: int,
                                    expires_at: Optional[datetime] = None,
                                    description: Optional[str] = None) -> Tuple[bool, str, List[RedemptionCode]]:
        """批量创建兑换码
        
        Args:
            value: 兑换金额
            count: 创建数量
            created_by: 创建者用户ID
            expires_at: 过期时间（可选）
            description: 描述（可选）
            
        Returns:
            Tuple[bool, str, List[RedemptionCode]]: (成功标志, 消息, 兑换码列表)
        """
        if count <= 0 or count > 100:
            return False, "批量创建数量必须在1-100之间", []
        
        created_codes = []
        failed_count = 0
        
        for i in range(count):
            success, message, code = cls.create_redemption_code(
                value=value,
                created_by=created_by,
                expires_at=expires_at,
                description=f"{description} (批次 {i+1}/{count})" if description else f"批次 {i+1}/{count}"
            )
            
            if success and code:
                created_codes.append(code)
            else:
                failed_count += 1
                logger.warning(f"[批量创建兑换码] 第 {i+1} 个兑换码创建失败: {message}")
        
        success_count = len(created_codes)
        if success_count == count:
            return True, t('redemption_code_generated'), created_codes
        elif success_count > 0:
            return True, t('redemption_code_generated'), created_codes
        else:
            return False, t('redemption_code_generation_failed'), []
    
    @classmethod
    def validate_redemption_code(cls, code: str) -> Tuple[bool, str, Optional[RedemptionCode]]:
        """验证兑换码
        
        Args:
            code: 兑换码
            
        Returns:
            Tuple[bool, str, Optional[RedemptionCode]]: (有效标志, 消息, 兑换码对象)
        """
        try:
            # 查找兑换码
            redemption_code = RedemptionCode.query.filter_by(code=code.upper().strip()).first()
            
            if not redemption_code:
                return False, t('redemption_code_not_found'), None

            # 检查是否已使用
            if redemption_code.is_used:
                return False, t('redemption_code_used'), redemption_code

            # 检查是否过期
            if redemption_code.is_expired():
                return False, t('redemption_code_expired'), redemption_code

            return True, t('success'), redemption_code
            
        except Exception as e:
            logger.error(f"[兑换码验证] 验证兑换码 {code} 时发生错误: {str(e)}", exc_info=True)
            return False, t('redemption_failed'), None
    
    @classmethod
    def use_redemption_code(cls, code: str, user_id: int) -> Tuple[bool, str, Optional[Decimal]]:
        """使用兑换码
        
        Args:
            code: 兑换码
            user_id: 使用者用户ID
            
        Returns:
            Tuple[bool, str, Optional[Decimal]]: (成功标志, 消息, 兑换金额)
        """
        try:
            # 验证用户
            user = User.query.get(user_id)
            if not user:
                return False, "用户不存在", None
            
            # 验证兑换码
            valid, message, redemption_code = cls.validate_redemption_code(code)
            if not valid or not redemption_code:
                return False, message, None
            
            # 标记为已使用
            redemption_code.is_used = True
            redemption_code.used_by = user_id
            redemption_code.used_at = datetime.utcnow()
            
            db.session.commit()
            
            logger.info(f"[兑换码使用] 用户 {user_id} 使用兑换码 {code}，获得 {redemption_code.value}元")
            return True, t('redemption_success'), redemption_code.value

        except Exception as e:
            db.session.rollback()
            logger.error(f"[兑换码使用] 用户 {user_id} 使用兑换码 {code} 时发生错误: {str(e)}", exc_info=True)
            return False, t('redemption_failed'), None
    
    @classmethod
    def get_redemption_codes(cls, created_by: Optional[int] = None, 
                           is_used: Optional[bool] = None,
                           page: int = 1, per_page: int = 20) -> dict:
        """获取兑换码列表
        
        Args:
            created_by: 创建者ID（可选）
            is_used: 是否已使用（可选）
            page: 页码
            per_page: 每页数量
            
        Returns:
            dict: 包含兑换码列表和分页信息的字典
        """
        try:
            query = RedemptionCode.query
            
            if created_by is not None:
                query = query.filter_by(created_by=created_by)
            
            if is_used is not None:
                query = query.filter_by(is_used=is_used)
            
            # 按创建时间倒序排列
            query = query.order_by(RedemptionCode.created_at.desc())
            
            # 分页
            pagination = query.paginate(
                page=page, 
                per_page=per_page, 
                error_out=False
            )
            
            return {
                'codes': [code.to_dict() for code in pagination.items],
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
            
        except Exception as e:
            logger.error(f"[兑换码查询] 查询兑换码列表时发生错误: {str(e)}", exc_info=True)
            return {
                'codes': [],
                'total': 0,
                'pages': 0,
                'current_page': page,
                'per_page': per_page,
                'has_next': False,
                'has_prev': False
            }
    
    @classmethod
    def disable_redemption_code(cls, code_id: int, operator_id: int) -> Tuple[bool, str]:
        """禁用兑换码
        
        Args:
            code_id: 兑换码ID
            operator_id: 操作者ID
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            # 验证操作者权限
            operator = User.query.get(operator_id)
            if not operator or not operator.is_admin:
                return False, t('permission_denied')

            # 查找兑换码
            redemption_code = RedemptionCode.query.get(code_id)
            if not redemption_code:
                return False, t('redemption_code_not_found')

            if redemption_code.is_used:
                return False, t('redemption_code_used')
            
            # 设置过期时间为当前时间（实现禁用效果）
            redemption_code.expires_at = datetime.utcnow()
            db.session.commit()
            
            logger.info(f"[兑换码禁用] 管理员 {operator_id} 禁用兑换码 {redemption_code.code}")
            return True, t('redemption_code_disabled_success')

        except Exception as e:
            db.session.rollback()
            logger.error(f"[兑换码禁用] 禁用兑换码 {code_id} 时发生错误: {str(e)}", exc_info=True)
            return False, t('admin.redemption.disable_failed')
