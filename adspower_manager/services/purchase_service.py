"""
购买服务模块
处理用户使用余额购买订阅的功能
"""

from datetime import datetime
from typing import Optional, <PERSON><PERSON>
from decimal import Decimal

from flask import current_app

from ..models import db, User, SubscriptionType
from ..services.balance_service import BalanceService
from ..services.subscription_service import SubscriptionService
from ..utils import logger


class PurchaseService:
    """购买服务类"""
    
    @classmethod
    def check_purchase_eligibility(cls, user_id: int, subscription_type_id: int) -> Tuple[bool, str, Optional[dict]]:
        """检查用户是否可以购买指定订阅
        
        Args:
            user_id: 用户ID
            subscription_type_id: 订阅类型ID
            
        Returns:
            Tuple[bool, str, Optional[dict]]: (可购买标志, 消息, 购买信息)
        """
        try:
            # 验证用户
            user = User.query.get(user_id)
            if not user:
                return False, "用户不存在", None
            
            # 验证订阅类型
            subscription_type = SubscriptionType.query.get(subscription_type_id)
            if not subscription_type:
                return False, "订阅类型不存在", None
            
            if not subscription_type.is_public:
                return False, "该订阅类型不可购买", None
            
            # 获取用户余额
            user_balance = BalanceService.get_user_balance(user_id)
            if user_balance is None:
                return False, "获取用户余额失败", None
            
            # 检查余额是否足够
            required_amount = Decimal(str(subscription_type.price))
            if user_balance < required_amount:
                return False, f"余额不足，当前余额: {user_balance}元，需要: {required_amount}元", None
            
            # 计算续费后的到期时间
            try:
                new_expiry_date = SubscriptionService().calculate_renewal_expiry_date(user_id, subscription_type_id)
            except Exception as e:
                logger.error(f"[购买检查] 计算续费到期时间失败: {str(e)}")
                new_expiry_date = None
            
            purchase_info = {
                'user_id': user_id,
                'subscription_type_id': subscription_type_id,
                'subscription_type_name': subscription_type.name,
                'price': float(required_amount),
                'days': subscription_type.days,
                'max_devices': subscription_type.max_devices,
                'current_balance': float(user_balance),
                'remaining_balance': float(user_balance - required_amount),
                'new_expiry_date': new_expiry_date.isoformat() if new_expiry_date else None
            }
            
            return True, "可以购买", purchase_info
            
        except Exception as e:
            logger.error(f"[购买检查] 检查用户 {user_id} 购买订阅 {subscription_type_id} 资格时发生错误: {str(e)}", exc_info=True)
            return False, f"检查购买资格失败: {str(e)}", None
    
    @classmethod
    def purchase_subscription(cls, user_id: int, subscription_type_id: int) -> Tuple[bool, str, Optional[dict]]:
        """购买订阅
        
        Args:
            user_id: 用户ID
            subscription_type_id: 订阅类型ID
            
        Returns:
            Tuple[bool, str, Optional[dict]]: (成功标志, 消息, 购买结果)
        """
        try:
            # 检查购买资格
            eligible, message, purchase_info = cls.check_purchase_eligibility(user_id, subscription_type_id)
            if not eligible or not purchase_info:
                return False, message, None
            
            # 获取订阅类型
            subscription_type = SubscriptionType.query.get(subscription_type_id)
            required_amount = Decimal(str(subscription_type.price))
            
            # 开始数据库事务
            try:
                # 1. 扣减余额
                success, balance_message, new_balance = BalanceService.deduct_balance(
                    user_id=user_id,
                    amount=required_amount,
                    related_type=BalanceService.RELATED_TYPE_SUBSCRIPTION,
                    description=f"购买订阅: {subscription_type.name}"
                )
                
                if not success:
                    return False, f"扣减余额失败: {balance_message}", None
                
                # 2. 创建或延长订阅
                subscription, sub_message = SubscriptionService.create_or_extend_subscription(
                    user_id=user_id,
                    subscription_type_id=subscription_type_id,
                    payment_id=None  # 不再需要支付记录
                )
                
                if not subscription:
                    # 如果订阅创建失败，需要退款
                    BalanceService.refund_balance(
                        user_id=user_id,
                        amount=required_amount,
                        related_type=BalanceService.RELATED_TYPE_SUBSCRIPTION,
                        description=f"订阅创建失败退款: {subscription_type.name}",
                        operator_id=None
                    )
                    return False, f"创建订阅失败: {sub_message}", None
                
                # 3. 更新余额变动记录的关联ID
                # 查找刚才创建的余额变动记录并更新关联ID
                latest_transaction = db.session.query(
                    db.select(db.func.max(db.text('balance_transactions.id')))
                    .where(db.text('balance_transactions.user_id = :user_id'))
                    .where(db.text('balance_transactions.transaction_type = :type'))
                ).params(user_id=user_id, type=BalanceService.TRANSACTION_TYPE_PURCHASE).scalar()
                
                if latest_transaction:
                    from ..models import BalanceTransaction
                    transaction = BalanceTransaction.query.get(latest_transaction)
                    if transaction:
                        transaction.related_id = subscription.id
                        
                db.session.commit()
                
                # 构建购买结果
                purchase_result = {
                    'subscription_id': subscription.id,
                    'subscription_type_name': subscription_type.name,
                    'amount_paid': float(required_amount),
                    'new_balance': float(new_balance),
                    'subscription_start_date': subscription.start_date.isoformat(),
                    'subscription_end_date': subscription.end_date.isoformat(),
                    'days_added': subscription_type.days,
                    'max_devices': subscription_type.max_devices
                }
                
                logger.info(f"[订阅购买] 用户 {user_id} 成功购买订阅 {subscription_type.name}，花费 {required_amount}元")
                return True, "购买成功", purchase_result
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"[订阅购买] 用户 {user_id} 购买订阅时数据库操作失败: {str(e)}", exc_info=True)
                return False, f"购买失败: {str(e)}", None
                
        except Exception as e:
            logger.error(f"[订阅购买] 用户 {user_id} 购买订阅 {subscription_type_id} 时发生错误: {str(e)}", exc_info=True)
            return False, f"购买失败: {str(e)}", None
    
    @classmethod
    def get_available_subscription_types(cls, user_id: int) -> list:
        """获取用户可购买的订阅类型列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            list: 可购买的订阅类型列表，包含购买信息
        """
        try:
            # 获取所有公开的订阅类型
            subscription_types = SubscriptionType.query.filter_by(is_public=True).all()
            
            # 获取用户余额
            user_balance = BalanceService.get_user_balance(user_id)
            if user_balance is None:
                user_balance = Decimal('0.00')
            
            available_types = []
            for sub_type in subscription_types:
                required_amount = Decimal(str(sub_type.price))
                can_afford = user_balance >= required_amount
                
                # 计算续费后的到期时间
                try:
                    new_expiry_date = SubscriptionService().calculate_renewal_expiry_date(user_id, sub_type.id)
                except Exception:
                    new_expiry_date = None
                
                type_info = {
                    'id': sub_type.id,
                    'name': sub_type.name,
                    'price': float(sub_type.price),
                    'days': sub_type.days,
                    'max_devices': sub_type.max_devices,
                    'description': sub_type.description,
                    'can_afford': can_afford,
                    'required_amount': float(required_amount),
                    'new_expiry_date': new_expiry_date.isoformat() if new_expiry_date else None
                }
                
                available_types.append(type_info)
            
            return available_types
            
        except Exception as e:
            logger.error(f"[可购买订阅] 获取用户 {user_id} 可购买订阅类型时发生错误: {str(e)}", exc_info=True)
            return []
    
    @classmethod
    def simulate_purchase(cls, user_id: int, subscription_type_id: int) -> Tuple[bool, str, Optional[dict]]:
        """模拟购买（不实际执行，仅返回购买信息）
        
        Args:
            user_id: 用户ID
            subscription_type_id: 订阅类型ID
            
        Returns:
            Tuple[bool, str, Optional[dict]]: (可购买标志, 消息, 模拟购买信息)
        """
        return cls.check_purchase_eligibility(user_id, subscription_type_id)
