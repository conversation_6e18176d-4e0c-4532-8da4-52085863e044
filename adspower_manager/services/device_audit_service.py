"""
设备审计服务
用于记录和查询设备操作历史，防止设备额度滥用
"""
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from sqlalchemy import and_, or_, desc, case
from extensions import db
from adspower_manager.models import DeviceAudit, Device, User
from adspower_manager.services.ip_location_service import IPLocationService
import logging

logger = logging.getLogger(__name__)


class DeviceAuditService:
    """设备审计服务类"""
    
    # 操作类型常量
    ACTION_REGISTER = 'register'
    ACTION_DELETE = 'delete'
    ACTION_LOGIN = 'login'
    ACTION_LOGOUT = 'logout'
    ACTION_VERIFY_SUCCESS = 'verify_success'
    ACTION_VERIFY_FAILED = 'verify_failed'
    ACTION_UPDATE = 'update'
    ACTION_SUSPICIOUS = 'suspicious'
    
    
    @classmethod
    def log_device_action(cls, 
                         device_id: Optional[int],
                         user_id: int,
                         action: str,
                         action_source: str = 'user',
                         description: str = None,
                         device_snapshot: Dict[str, Any] = None,
                         old_value: Dict[str, Any] = None,
                         new_value: Dict[str, Any] = None,
                         operator_id: int = None,
                         ip_address: str = None,
                         user_agent: str = None) -> DeviceAudit:
        """
        记录设备操作审计日志
        
        Args:
            device_id: 设备ID（可选，设备删除后为NULL）
            user_id: 用户ID
            action: 操作类型
            action_source: 操作来源（user/admin/system）
            description: 操作描述
            device_snapshot: 设备信息快照
            old_value: 修改前的值
            new_value: 修改后的值
            operator_id: 操作者ID（管理员操作时）
            ip_address: 操作IP地址
            user_agent: 浏览器信息
            
        Returns:
            DeviceAudit: 创建的审计记录
        """
        try:
            audit = DeviceAudit(
                device_id=device_id,
                user_id=user_id,
                action=action,
                action_source=action_source,
                description=description,
                device_snapshot=device_snapshot,
                old_value=old_value,
                new_value=new_value,
                operator_id=operator_id,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.session.add(audit)
            db.session.commit()
            
            logger.info(f"设备审计日志已记录: {action} - User:{user_id}, Device:{device_id}")
            return audit
            
        except Exception as e:
            logger.error(f"记录设备审计日志失败: {str(e)}")
            db.session.rollback()
            raise
    
    @classmethod
    def get_device_audit_logs(cls,
                             device_id: int = None,
                             user_id: int = None,
                             action: str = None,
                             start_date: datetime = None,
                             end_date: datetime = None,
                             page: int = 1,
                             per_page: int = 20) -> tuple:
        """
        查询设备审计日志
        
        Args:
            device_id: 设备ID
            user_id: 用户ID
            action: 操作类型
            start_date: 开始时间
            end_date: 结束时间
            page: 页码
            per_page: 每页记录数
            
        Returns:
            tuple: (审计记录列表, 总记录数)
        """
        query = DeviceAudit.query
        
        if device_id:
            query = query.filter(DeviceAudit.device_id == device_id)
        if user_id:
            query = query.filter(DeviceAudit.user_id == user_id)
        if action:
            query = query.filter(DeviceAudit.action == action)
        if start_date:
            query = query.filter(DeviceAudit.created_at >= start_date)
        if end_date:
            query = query.filter(DeviceAudit.created_at <= end_date)
        
        # 按创建时间倒序排列
        query = query.order_by(desc(DeviceAudit.created_at))
        
        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return pagination.items, pagination.total
    
    @classmethod
    def get_user_device_activities(cls, user_id: int, days: int = 30) -> Dict[str, Any]:
        """
        获取用户设备活动统计
        
        Args:
            user_id: 用户ID
            days: 统计天数
            
        Returns:
            dict: 活动统计信息
        """
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 查询指定时间范围内的审计记录
        audits = DeviceAudit.query.filter(
            and_(
                DeviceAudit.user_id == user_id,
                DeviceAudit.created_at >= start_date
            )
        ).all()
        
        # 统计各类操作
        stats = {
            'total_actions': len(audits),
            'register_count': 0,
            'delete_count': 0,
            'login_count': 0,
            'logout_count': 0,
            'unique_devices': set(),
            'unique_ips': set(),
            'daily_actions': {}
        }
        
        for audit in audits:
            # 统计操作类型
            if audit.action == cls.ACTION_REGISTER:
                stats['register_count'] += 1
            elif audit.action == cls.ACTION_DELETE:
                stats['delete_count'] += 1
            elif audit.action == cls.ACTION_LOGIN:
                stats['login_count'] += 1
            elif audit.action == cls.ACTION_LOGOUT:
                stats['logout_count'] += 1
            
            # 统计唯一设备和IP
            if audit.device_id:
                stats['unique_devices'].add(audit.device_id)
            if audit.ip_address:
                stats['unique_ips'].add(audit.ip_address)
            
            # 按日期统计
            date_key = audit.created_at.strftime('%Y-%m-%d')
            if date_key not in stats['daily_actions']:
                stats['daily_actions'][date_key] = 0
            stats['daily_actions'][date_key] += 1
        
        # 转换set为数量
        stats['unique_devices_count'] = len(stats['unique_devices'])
        stats['unique_ips_count'] = len(stats['unique_ips'])
        del stats['unique_devices']
        del stats['unique_ips']
        
        return stats
    
    
    @classmethod
    def get_device_change_statistics(cls, days: int = 30, limit: int = 20) -> List[Dict[str, Any]]:
        """
        获取设备更换次数统计（按用户）
        
        Args:
            days: 统计天数
            limit: 返回前N个用户
            
        Returns:
            list: 用户设备更换统计列表
        """
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 查询设备注册和删除操作
        device_changes = db.session.query(
            DeviceAudit.user_id,
            db.func.count(DeviceAudit.id).label('total_changes'),
            db.func.sum(case((DeviceAudit.action == cls.ACTION_REGISTER, 1), else_=0)).label('register_count'),
            db.func.sum(case((DeviceAudit.action == cls.ACTION_DELETE, 1), else_=0)).label('delete_count'),
            db.func.count(db.distinct(DeviceAudit.ip_address)).label('unique_ips'),
            db.func.max(DeviceAudit.created_at).label('last_action_time')
        ).filter(
            and_(
                DeviceAudit.created_at >= start_date,
                DeviceAudit.action.in_([cls.ACTION_REGISTER, cls.ACTION_DELETE])
            )
        ).group_by(DeviceAudit.user_id).order_by(db.desc('total_changes')).limit(limit).all()
        
        result = []
        for record in device_changes:
            user = User.query.get(record.user_id)
            if user:
                # 计算更换频率（每天平均更换次数）
                change_frequency = record.total_changes / days if days > 0 else 0
                
                result.append({
                    'user_id': record.user_id,
                    'email': user.email,
                    'total_changes': record.total_changes,
                    'register_count': record.register_count or 0,
                    'delete_count': record.delete_count or 0,
                    'unique_ips': record.unique_ips or 0,
                    'last_action_time': record.last_action_time.isoformat() if record.last_action_time else None,
                    'change_frequency': round(change_frequency, 2),
                    'created_at': user.created_at.isoformat() if user.created_at else None
                })
        
        return result
    
    @classmethod
    def get_user_device_details(cls, user_id: int, days: int = 30) -> Dict[str, Any]:
        """
        获取用户设备使用详情，包括所有设备名称和IP地址
        
        Args:
            user_id: 用户ID
            days: 统计天数
            
        Returns:
            dict: 用户设备详情
        """
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 查询用户的所有设备操作记录
        audits = DeviceAudit.query.filter(
            and_(
                DeviceAudit.user_id == user_id,
                DeviceAudit.created_at >= start_date,
                DeviceAudit.action.in_([cls.ACTION_REGISTER, cls.ACTION_DELETE])
            )
        ).order_by(desc(DeviceAudit.created_at)).all()
        
        # 统计设备信息
        devices_info = {}  # 设备名称 -> {device_ips, access_ips, actions, last_seen}
        all_device_ips = set()  # 所有设备IP
        all_access_ips = set()  # 所有访问IP
        
        for audit in audits:
            # 从设备快照中获取设备名称
            device_name = None
            if audit.device_snapshot:
                device_name = audit.device_snapshot.get('device_name', 'Unknown')
            elif audit.device and audit.device.device_name:
                device_name = audit.device.device_name
            else:
                device_name = 'Unknown'
            
            # 初始化设备信息
            if device_name not in devices_info:
                devices_info[device_name] = {
                    'device_ips': set(),  # AdsPower设备IP
                    'access_ips': set(),  # 网站访问IP
                    'register_count': 0,
                    'delete_count': 0,
                    'first_seen': audit.created_at,
                    'last_seen': audit.created_at,
                    'actions': []
                }
            
            # 分别收集设备IP和访问IP
            device_ip = None
            if audit.device_snapshot and audit.device_snapshot.get('device_ip'):
                device_ip = audit.device_snapshot.get('device_ip')
                devices_info[device_name]['device_ips'].add(device_ip)
                all_device_ips.add(device_ip)
            
            # 访问IP始终收集
            if audit.ip_address:
                devices_info[device_name]['access_ips'].add(audit.ip_address)
                all_access_ips.add(audit.ip_address)
            
            if audit.action == cls.ACTION_REGISTER:
                devices_info[device_name]['register_count'] += 1
            elif audit.action == cls.ACTION_DELETE:
                devices_info[device_name]['delete_count'] += 1
            
            # 更新首次使用和最后使用时间
            devices_info[device_name]['first_seen'] = min(
                devices_info[device_name]['first_seen'], 
                audit.created_at
            )
            devices_info[device_name]['last_seen'] = max(
                devices_info[device_name]['last_seen'], 
                audit.created_at
            )
            
            devices_info[device_name]['actions'].append({
                'action': audit.action,
                'time': audit.created_at.isoformat(),
                'device_ip': device_ip,  # AdsPower设备IP
                'access_ip': audit.ip_address  # 网站访问IP
            })
        
        # 收集所有需要查询位置的IP
        all_ips_to_query = list(all_device_ips | all_access_ips)
        
        # 批量查询IP位置信息
        ip_locations = {}
        if all_ips_to_query:
            try:
                ip_locations = IPLocationService.batch_get_locations(all_ips_to_query)
            except Exception as e:
                logger.error(f"批量查询IP位置失败: {str(e)}")
        
        # 转换为列表格式
        devices_list = []
        for device_name, info in devices_info.items():
            # 构建设备IP信息（包含位置）
            device_ips_with_location = []
            for ip in info['device_ips']:
                location = ip_locations.get(ip, {})
                device_ips_with_location.append({
                    'ip': ip,
                    'location': location.get('location_str', '未知'),
                    'isp': location.get('isp', '')
                })
            
            # 构建访问IP信息（包含位置）
            access_ips_with_location = []
            for ip in info['access_ips']:
                location = ip_locations.get(ip, {})
                access_ips_with_location.append({
                    'ip': ip,
                    'location': location.get('location_str', '未知'),
                    'isp': location.get('isp', '')
                })
            
            devices_list.append({
                'device_name': device_name,
                'device_ip_count': len(info['device_ips']),
                'device_ips': list(info['device_ips']),
                'device_ips_with_location': device_ips_with_location,
                'access_ip_count': len(info['access_ips']),
                'access_ips': list(info['access_ips']),
                'access_ips_with_location': access_ips_with_location,
                'register_count': info['register_count'],
                'delete_count': info['delete_count'],
                'first_seen': info['first_seen'].isoformat(),
                'last_seen': info['last_seen'].isoformat(),
                'is_active': info['register_count'] > info['delete_count'],
                'recent_actions': info['actions'][-5:]  # 最近5次操作
            })
        
        # 按最后使用时间排序
        devices_list.sort(key=lambda x: x['last_seen'], reverse=True)
        
        # 统计数据
        unique_devices = len(devices_info)
        unique_device_ips = len(all_device_ips)
        unique_access_ips = len(all_access_ips)
        
        return {
            'user_id': user_id,
            'devices': devices_list,
            'unique_devices_count': unique_devices,
            'unique_device_ips_count': unique_device_ips,
            'unique_access_ips_count': unique_access_ips,
            'total_operations': len(audits),
            'period_days': days
        }