"""
余额服务模块
处理用户余额的查询、充值、扣减等功能
"""

from datetime import datetime
from typing import Optional, Tuple, List
from decimal import Decimal

from flask import current_app
from sqlalchemy.exc import IntegrityError

from ..models import db, User, BalanceTransaction, RedemptionCode
from ..utils import logger


class BalanceService:
    """余额服务类"""
    
    # 交易类型常量
    TRANSACTION_TYPE_RECHARGE = 'recharge'  # 充值
    TRANSACTION_TYPE_PURCHASE = 'purchase'  # 购买
    TRANSACTION_TYPE_REFUND = 'refund'      # 退款
    TRANSACTION_TYPE_ADMIN_ADJUST = 'admin_adjust'  # 管理员调整
    
    # 关联类型常量
    RELATED_TYPE_REDEMPTION_CODE = 'redemption_code'
    RELATED_TYPE_SUBSCRIPTION = 'subscription'
    RELATED_TYPE_ADMIN = 'admin'
    
    @classmethod
    def get_user_balance(cls, user_id: int) -> Optional[Decimal]:
        """获取用户余额
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[Decimal]: 用户余额，如果用户不存在返回None
        """
        try:
            user = User.query.get(user_id)
            if not user:
                return None
            
            return user.balance or Decimal('0.00')
            
        except Exception as e:
            logger.error(f"[余额查询] 查询用户 {user_id} 余额时发生错误: {str(e)}", exc_info=True)
            return None
    
    @classmethod
    def recharge_balance(cls, user_id: int, amount: Decimal, 
                        related_id: Optional[int] = None,
                        related_type: str = RELATED_TYPE_REDEMPTION_CODE,
                        description: Optional[str] = None,
                        operator_id: Optional[int] = None) -> Tuple[bool, str, Optional[Decimal]]:
        """充值余额
        
        Args:
            user_id: 用户ID
            amount: 充值金额
            related_id: 关联ID（如兑换码ID）
            related_type: 关联类型
            description: 描述
            operator_id: 操作者ID（管理员充值时使用）
            
        Returns:
            Tuple[bool, str, Optional[Decimal]]: (成功标志, 消息, 新余额)
        """
        try:
            # 验证用户
            user = User.query.get(user_id)
            if not user:
                return False, "用户不存在", None
            
            # 验证金额
            if amount <= 0:
                return False, "充值金额必须大于0", None
            
            # 获取当前余额
            current_balance = user.balance or Decimal('0.00')
            new_balance = current_balance + amount
            
            # 更新用户余额
            user.balance = new_balance
            user.balance_updated_at = datetime.utcnow()
            
            # 创建余额变动记录
            transaction = BalanceTransaction(
                user_id=user_id,
                transaction_type=cls.TRANSACTION_TYPE_RECHARGE,
                amount=amount,
                balance_before=current_balance,
                balance_after=new_balance,
                related_id=related_id,
                related_type=related_type,
                description=description or f"余额充值 {amount}元",
                operator_id=operator_id,
                created_at=datetime.utcnow()
            )
            
            db.session.add(transaction)
            db.session.commit()
            
            logger.info(f"[余额充值] 用户 {user_id} 充值 {amount}元，余额: {current_balance} -> {new_balance}")
            return True, "充值成功", new_balance
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"[余额充值] 用户 {user_id} 充值 {amount}元时发生错误: {str(e)}", exc_info=True)
            return False, f"充值失败: {str(e)}", None
    
    @classmethod
    def deduct_balance(cls, user_id: int, amount: Decimal,
                      related_id: Optional[int] = None,
                      related_type: str = RELATED_TYPE_SUBSCRIPTION,
                      description: Optional[str] = None,
                      operator_id: Optional[int] = None) -> Tuple[bool, str, Optional[Decimal]]:
        """扣减余额
        
        Args:
            user_id: 用户ID
            amount: 扣减金额
            related_id: 关联ID（如订阅ID）
            related_type: 关联类型
            description: 描述
            operator_id: 操作者ID
            
        Returns:
            Tuple[bool, str, Optional[Decimal]]: (成功标志, 消息, 新余额)
        """
        try:
            # 验证用户
            user = User.query.get(user_id)
            if not user:
                return False, "用户不存在", None
            
            # 验证金额
            if amount <= 0:
                return False, "扣减金额必须大于0", None
            
            # 获取当前余额
            current_balance = user.balance or Decimal('0.00')
            
            # 检查余额是否足够
            if current_balance < amount:
                return False, f"余额不足，当前余额: {current_balance}元，需要: {amount}元", None
            
            new_balance = current_balance - amount
            
            # 更新用户余额
            user.balance = new_balance
            user.balance_updated_at = datetime.utcnow()
            
            # 创建余额变动记录
            transaction = BalanceTransaction(
                user_id=user_id,
                transaction_type=cls.TRANSACTION_TYPE_PURCHASE,
                amount=-amount,  # 负数表示扣减
                balance_before=current_balance,
                balance_after=new_balance,
                related_id=related_id,
                related_type=related_type,
                description=description or f"购买服务扣减 {amount}元",
                operator_id=operator_id,
                created_at=datetime.utcnow()
            )
            
            db.session.add(transaction)
            db.session.commit()
            
            logger.info(f"[余额扣减] 用户 {user_id} 扣减 {amount}元，余额: {current_balance} -> {new_balance}")
            return True, "扣减成功", new_balance
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"[余额扣减] 用户 {user_id} 扣减 {amount}元时发生错误: {str(e)}", exc_info=True)
            return False, f"扣减失败: {str(e)}", None
    
    @classmethod
    def refund_balance(cls, user_id: int, amount: Decimal,
                      related_id: Optional[int] = None,
                      related_type: str = RELATED_TYPE_SUBSCRIPTION,
                      description: Optional[str] = None,
                      operator_id: Optional[int] = None) -> Tuple[bool, str, Optional[Decimal]]:
        """退款到余额
        
        Args:
            user_id: 用户ID
            amount: 退款金额
            related_id: 关联ID
            related_type: 关联类型
            description: 描述
            operator_id: 操作者ID
            
        Returns:
            Tuple[bool, str, Optional[Decimal]]: (成功标志, 消息, 新余额)
        """
        try:
            # 验证用户
            user = User.query.get(user_id)
            if not user:
                return False, "用户不存在", None
            
            # 验证金额
            if amount <= 0:
                return False, "退款金额必须大于0", None
            
            # 获取当前余额
            current_balance = user.balance or Decimal('0.00')
            new_balance = current_balance + amount
            
            # 更新用户余额
            user.balance = new_balance
            user.balance_updated_at = datetime.utcnow()
            
            # 创建余额变动记录
            transaction = BalanceTransaction(
                user_id=user_id,
                transaction_type=cls.TRANSACTION_TYPE_REFUND,
                amount=amount,
                balance_before=current_balance,
                balance_after=new_balance,
                related_id=related_id,
                related_type=related_type,
                description=description or f"退款 {amount}元",
                operator_id=operator_id,
                created_at=datetime.utcnow()
            )
            
            db.session.add(transaction)
            db.session.commit()
            
            logger.info(f"[余额退款] 用户 {user_id} 退款 {amount}元，余额: {current_balance} -> {new_balance}")
            return True, "退款成功", new_balance
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"[余额退款] 用户 {user_id} 退款 {amount}元时发生错误: {str(e)}", exc_info=True)
            return False, f"退款失败: {str(e)}", None
    
    @classmethod
    def admin_adjust_balance(cls, user_id: int, amount: Decimal,
                           description: str, operator_id: int) -> Tuple[bool, str, Optional[Decimal]]:
        """管理员调整余额
        
        Args:
            user_id: 用户ID
            amount: 调整金额（正数为增加，负数为减少）
            description: 调整原因
            operator_id: 操作者ID（管理员）
            
        Returns:
            Tuple[bool, str, Optional[Decimal]]: (成功标志, 消息, 新余额)
        """
        try:
            # 验证操作者权限
            operator = User.query.get(operator_id)
            if not operator or not operator.is_admin:
                return False, "只有管理员可以调整余额", None
            
            # 验证用户
            user = User.query.get(user_id)
            if not user:
                return False, "用户不存在", None
            
            # 验证金额
            if amount == 0:
                return False, "调整金额不能为0", None
            
            # 获取当前余额
            current_balance = user.balance or Decimal('0.00')
            
            # 如果是减少余额，检查是否足够
            if amount < 0 and current_balance < abs(amount):
                return False, f"余额不足，当前余额: {current_balance}元，尝试减少: {abs(amount)}元", None
            
            new_balance = current_balance + amount
            
            # 更新用户余额
            user.balance = new_balance
            user.balance_updated_at = datetime.utcnow()
            
            # 创建余额变动记录
            transaction = BalanceTransaction(
                user_id=user_id,
                transaction_type=cls.TRANSACTION_TYPE_ADMIN_ADJUST,
                amount=amount,
                balance_before=current_balance,
                balance_after=new_balance,
                related_id=operator_id,
                related_type=cls.RELATED_TYPE_ADMIN,
                description=f"管理员调整: {description}",
                operator_id=operator_id,
                created_at=datetime.utcnow()
            )
            
            db.session.add(transaction)
            db.session.commit()
            
            action = "增加" if amount > 0 else "减少"
            logger.info(f"[余额调整] 管理员 {operator_id} {action}用户 {user_id} 余额 {abs(amount)}元，余额: {current_balance} -> {new_balance}")
            return True, f"余额调整成功，{action} {abs(amount)}元", new_balance
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"[余额调整] 管理员 {operator_id} 调整用户 {user_id} 余额时发生错误: {str(e)}", exc_info=True)
            return False, f"余额调整失败: {str(e)}", None
    
    @classmethod
    def get_balance_transactions(cls, user_id: int, page: int = 1, per_page: int = 20) -> dict:
        """获取用户余额变动记录
        
        Args:
            user_id: 用户ID
            page: 页码
            per_page: 每页数量
            
        Returns:
            dict: 包含交易记录和分页信息的字典
        """
        try:
            query = BalanceTransaction.query.filter_by(user_id=user_id)
            query = query.order_by(BalanceTransaction.created_at.desc())
            
            # 分页
            pagination = query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )
            
            return {
                'transactions': [transaction.to_dict() for transaction in pagination.items],
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
            
        except Exception as e:
            logger.error(f"[余额记录查询] 查询用户 {user_id} 余额记录时发生错误: {str(e)}", exc_info=True)
            return {
                'transactions': [],
                'total': 0,
                'pages': 0,
                'current_page': page,
                'per_page': per_page,
                'has_next': False,
                'has_prev': False
            }
    
    @classmethod
    def redeem_code_and_recharge(cls, user_id: int, code: str) -> Tuple[bool, str, Optional[Decimal]]:
        """使用兑换码充值余额
        
        Args:
            user_id: 用户ID
            code: 兑换码
            
        Returns:
            Tuple[bool, str, Optional[Decimal]]: (成功标志, 消息, 新余额)
        """
        try:
            from .redemption_service import RedemptionService
            
            # 使用兑换码
            success, message, amount = RedemptionService.use_redemption_code(code, user_id)
            if not success or amount is None:
                return False, message, None
            
            # 获取兑换码记录
            redemption_code = RedemptionCode.query.filter_by(code=code.upper().strip()).first()
            
            # 充值余额
            success, recharge_message, new_balance = cls.recharge_balance(
                user_id=user_id,
                amount=amount,
                related_id=redemption_code.id if redemption_code else None,
                related_type=cls.RELATED_TYPE_REDEMPTION_CODE,
                description=f"使用兑换码 {code} 充值 {amount}元"
            )
            
            if success:
                return True, f"兑换成功，获得 {amount}元余额", new_balance
            else:
                # 如果充值失败，需要回滚兑换码状态
                if redemption_code:
                    redemption_code.is_used = False
                    redemption_code.used_by = None
                    redemption_code.used_at = None
                    db.session.commit()
                
                return False, f"兑换失败: {recharge_message}", None
                
        except Exception as e:
            db.session.rollback()
            logger.error(f"[兑换码充值] 用户 {user_id} 使用兑换码 {code} 充值时发生错误: {str(e)}", exc_info=True)
            return False, f"兑换失败: {str(e)}", None
