"""
IP地理位置查询服务
支持IPv4和IPv6地址的地理位置查询
"""
import requests
import logging
from typing import Dict, Optional
from functools import lru_cache
import ipaddress
import time

logger = logging.getLogger(__name__)


class IPLocationService:
    """IP地理位置查询服务"""
    
    # 使用ip-api.com的免费服务（每分钟45次请求限制）
    API_URL = "http://ip-api.com/json/{ip}?lang=zh-CN"
    
    # 缓存配置
    CACHE_SIZE = 1000  # 缓存最多1000个IP
    CACHE_TTL = 86400  # 缓存24小时
    
    # 请求速率限制
    MIN_REQUEST_INTERVAL = 1.5  # 最小请求间隔（秒）
    _last_request_time = 0
    
    @classmethod
    @lru_cache(maxsize=CACHE_SIZE)
    def get_location(cls, ip: str) -> Dict[str, Optional[str]]:
        """
        获取IP地理位置信息
        
        Args:
            ip: IP地址（支持IPv4和IPv6）
            
        Returns:
            dict: 包含地理位置信息的字典
                {
                    'country': '国家',
                    'region': '省/州',
                    'city': '城市',
                    'isp': '运营商',
                    'location_str': '简化的位置字符串',
                    'success': True/False
                }
        """
        default_result = {
            'country': None,
            'region': None,
            'city': None,
            'isp': None,
            'location_str': '未知',
            'success': False
        }
        
        # 验证IP地址格式
        try:
            ip_obj = ipaddress.ip_address(ip)
            
            # 检查是否为私有IP
            if ip_obj.is_private:
                return {
                    **default_result,
                    'location_str': '内网IP',
                    'success': True
                }
            
            # 检查是否为本地回环地址
            if ip_obj.is_loopback:
                return {
                    **default_result,
                    'location_str': '本地回环',
                    'success': True
                }
                
        except ValueError:
            logger.warning(f"无效的IP地址格式: {ip}")
            return default_result
        
        # 速率限制
        current_time = time.time()
        time_since_last_request = current_time - cls._last_request_time
        if time_since_last_request < cls.MIN_REQUEST_INTERVAL:
            time.sleep(cls.MIN_REQUEST_INTERVAL - time_since_last_request)
        
        try:
            # 发送请求
            response = requests.get(
                cls.API_URL.format(ip=ip),
                timeout=5
            )
            cls._last_request_time = time.time()
            
            if response.status_code != 200:
                logger.error(f"IP查询API返回错误状态码: {response.status_code}")
                return default_result
            
            data = response.json()
            
            if data.get('status') == 'success':
                # 构建位置字符串
                location_parts = []
                
                # 国家
                country = data.get('country', '')
                if country and country != '中国':
                    location_parts.append(country)
                
                # 省/州
                region = data.get('regionName', '')
                if region and region not in location_parts:
                    location_parts.append(region)
                
                # 城市
                city = data.get('city', '')
                if city and city not in location_parts and city != region:
                    location_parts.append(city)
                
                # 运营商
                isp = data.get('isp', '')
                
                return {
                    'country': country,
                    'region': region,
                    'city': city,
                    'isp': isp,
                    'location_str': ' '.join(location_parts) if location_parts else '未知',
                    'success': True
                }
            else:
                logger.warning(f"IP查询失败: {ip}, 原因: {data.get('message', '未知')}")
                return default_result
                
        except requests.exceptions.Timeout:
            logger.error(f"IP查询超时: {ip}")
            return default_result
        except requests.exceptions.RequestException as e:
            logger.error(f"IP查询请求失败: {ip}, 错误: {str(e)}")
            return default_result
        except Exception as e:
            logger.error(f"IP查询异常: {ip}, 错误: {str(e)}")
            return default_result
    
    @classmethod
    def get_location_string(cls, ip: str) -> str:
        """
        获取简化的位置字符串
        
        Args:
            ip: IP地址
            
        Returns:
            str: 位置字符串，如"中国 广东 深圳"
        """
        location_info = cls.get_location(ip)
        return location_info.get('location_str', '未知')
    
    @classmethod
    def batch_get_locations(cls, ips: list) -> Dict[str, Dict]:
        """
        批量获取IP地理位置信息
        
        Args:
            ips: IP地址列表
            
        Returns:
            dict: IP到位置信息的映射
        """
        results = {}
        for ip in ips:
            if ip:
                results[ip] = cls.get_location(ip)
        return results