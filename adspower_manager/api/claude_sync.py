# -*- coding: utf-8 -*-
"""
Claude系统同步API
提供订阅数据查询接口供claude-code-custom系统调用
"""
from flask import Blueprint, request, current_app
from datetime import datetime
import logging

from ..models import User, Subscription, SubscriptionType
from ..utils import jsonify_response

logger = logging.getLogger(__name__)

# 创建蓝图
claude_sync_bp = Blueprint('claude_sync', __name__)

@claude_sync_bp.route('/user-subscriptions/<email>', methods=['GET'])
def get_user_subscriptions_by_email(email):
    """通过邮箱获取用户订阅（需要API密钥认证）"""
    # 验证API密钥
    api_key = request.headers.get('X-API-Key')
    expected_key = current_app.config.get('CLAUDE_SYNC_API_KEY', 'default-sync-key-123')
    
    if not api_key or api_key != expected_key:
        logger.warning(f"未授权的API访问尝试: {request.remote_addr}")
        return jsonify_response(
            success=False,
            message="未授权的访问",
            status_code=401
        )
    
    try:
        # 查找用户
        user = User.query.filter_by(email=email).first()
        if not user:
            logger.info(f"查询用户 {email} 订阅: 用户不存在")
            return jsonify_response(
                success=True,
                data={'user_email': email, 'subscriptions': []},
                message="用户不存在"
            )
        
        # 获取所有订阅，只查询标记为需要同步的类型
        subscriptions = Subscription.query.join(
            SubscriptionType
        ).filter(
            Subscription.user_id == user.id,
            SubscriptionType.sync_to_claude == True  # 只返回需要同步的订阅
        ).all()
        
        result = {
            'user_email': email,
            'subscriptions': []
        }
        
        for sub in subscriptions:
            sub_type = sub.subscription_type_details
            instance = sub.subscription_instance
            
            if not sub_type or not instance:
                continue
                
            sub_data = {
                'subscription_id': sub.id,
                'type': {
                    'id': sub_type.id,
                    'name': sub_type.name,
                    'max_devices': sub_type.max_devices,
                    'days': sub_type.days
                },
                'instance': {
                    'id': instance.id,
                    'name': instance.name,
                    'capacity': instance.capacity,
                    'is_active': instance.is_active
                },
                'start_date': sub.start_date.isoformat(),
                'end_date': sub.end_date.isoformat(),
                'is_expired': sub.end_date < datetime.utcnow(),  # 标识是否已过期
                'max_devices': sub.max_devices,  # 用户订阅的设备限制
                'created_at': sub.created_at.isoformat() if sub.created_at else None
            }
            
            result['subscriptions'].append(sub_data)
        
        logger.info(f"API查询用户 {email} 订阅数据，返回 {len(result['subscriptions'])} 条")
        
        return jsonify_response(
            success=True,
            data=result,
            message=f"找到 {len(result['subscriptions'])} 个订阅"
        )
        
    except Exception as e:
        logger.error(f"API获取用户订阅失败: {str(e)}", exc_info=True)
        return jsonify_response(
            success=False,
            message=f"获取订阅数据失败: {str(e)}",
            status_code=500
        )

@claude_sync_bp.route('/subscription-types', methods=['GET'])
def get_all_subscription_types():
    """获取所有订阅类型（供claude系统了解套餐信息）"""
    api_key = request.headers.get('X-API-Key')
    expected_key = current_app.config.get('CLAUDE_SYNC_API_KEY', 'default-sync-key-123')
    
    if not api_key or api_key != expected_key:
        return jsonify_response(
            success=False,
            message="未授权的访问",
            status_code=401
        )
    
    try:
        types = SubscriptionType.query.filter_by(is_public=True).all()
        
        result = []
        for t in types:
            result.append({
                'id': t.id,
                'name': t.name,
                'price': t.price,
                'days': t.days,
                'max_devices': t.max_devices,
                'description': t.description
            })
        
        logger.info(f"API查询订阅类型，返回 {len(result)} 个类型")
        
        return jsonify_response(
            success=True,
            data={'subscription_types': result}
        )
        
    except Exception as e:
        logger.error(f"获取订阅类型失败: {str(e)}", exc_info=True)
        return jsonify_response(
            success=False,
            message=f"获取订阅类型失败: {str(e)}",
            status_code=500
        )