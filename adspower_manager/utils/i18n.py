"""
国际化工具模块
提供后端API响应消息的多语言支持
"""

from flask import request
import logging

logger = logging.getLogger(__name__)

# 支持的语言列表
SUPPORTED_LANGUAGES = ['zh-CN', 'en-US']
DEFAULT_LANGUAGE = 'zh-CN'

# 消息字典
MESSAGES = {
    'zh-CN': {
        # 通用消息
        'success': '操作成功',
        'failed': '操作失败',
        'invalid_request': '无效的请求',
        'unauthorized': '未授权访问',
        'forbidden': '禁止访问',
        'not_found': '资源不存在',
        'internal_error': '服务器内部错误',
        'validation_error': '数据验证失败',
        
        # 认证相关
        'login_success': '登录成功',
        'login_failed': '登录失败',
        'logout_success': '退出成功',
        'token_invalid': '令牌无效',
        'token_expired': '令牌已过期',
        'permission_denied': '权限不足',
        
        # 用户相关
        'user_not_found': '用户不存在',
        'user_created': '用户创建成功',
        'user_updated': '用户更新成功',
        'user_deleted': '用户删除成功',
        'email_exists': '邮箱已存在',
        'username_exists': '用户名已存在',
        
        # 余额相关
        'balance_insufficient': '余额不足',
        'balance_updated': '余额更新成功',
        'balance_recharged': '充值成功',
        'balance_deducted': '扣费成功',
        'balance_query_success': '余额查询成功',
        
        # 兑换码相关
        'redemption_success': '兑换成功',
        'redemption_failed': '兑换失败',
        'redemption_code_invalid': '无效的兑换码',
        'redemption_code_used': '兑换码已被使用',
        'redemption_code_expired': '兑换码已过期',
        'redemption_code_disabled': '兑换码已被禁用',
        'redemption_code_not_found': '兑换码不存在',
        'redemption_code_generated': '兑换码生成成功',
        'redemption_code_generation_failed': '兑换码生成失败',
        'redemption_code_disabled_success': '兑换码禁用成功',
        
        # 订阅相关
        'subscription_created': '订阅创建成功',
        'subscription_updated': '订阅更新成功',
        'subscription_cancelled': '订阅取消成功',
        'subscription_not_found': '订阅不存在',
        'subscription_expired': '订阅已过期',
        'subscription_active': '订阅已激活',
        
        # 购买相关
        'purchase_success': '购买成功',
        'purchase_failed': '购买失败',
        'purchase_eligibility_check_failed': '购买资格检查失败',
        'insufficient_balance_for_purchase': '余额不足，无法购买',
        
        # 设备相关
        'device_not_found': '设备不存在',
        'device_limit_exceeded': '设备数量超出限制',
        'device_registered': '设备注册成功',
        'device_unregistered': '设备注销成功',
    },
    
    'en-US': {
        # Common messages
        'success': 'Operation successful',
        'failed': 'Operation failed',
        'invalid_request': 'Invalid request',
        'unauthorized': 'Unauthorized access',
        'forbidden': 'Access forbidden',
        'not_found': 'Resource not found',
        'internal_error': 'Internal server error',
        'validation_error': 'Data validation failed',
        
        # Authentication related
        'login_success': 'Login successful',
        'login_failed': 'Login failed',
        'logout_success': 'Logout successful',
        'token_invalid': 'Invalid token',
        'token_expired': 'Token expired',
        'permission_denied': 'Permission denied',
        
        # User related
        'user_not_found': 'User not found',
        'user_created': 'User created successfully',
        'user_updated': 'User updated successfully',
        'user_deleted': 'User deleted successfully',
        'email_exists': 'Email already exists',
        'username_exists': 'Username already exists',
        
        # Balance related
        'balance_insufficient': 'Insufficient balance',
        'balance_updated': 'Balance updated successfully',
        'balance_recharged': 'Recharge successful',
        'balance_deducted': 'Deduction successful',
        'balance_query_success': 'Balance query successful',
        
        # Redemption code related
        'redemption_success': 'Redemption successful',
        'redemption_failed': 'Redemption failed',
        'redemption_code_invalid': 'Invalid redemption code',
        'redemption_code_used': 'Redemption code has been used',
        'redemption_code_expired': 'Redemption code has expired',
        'redemption_code_disabled': 'Redemption code has been disabled',
        'redemption_code_not_found': 'Redemption code not found',
        'redemption_code_generated': 'Redemption code generated successfully',
        'redemption_code_generation_failed': 'Failed to generate redemption code',
        'redemption_code_disabled_success': 'Redemption code disabled successfully',
        
        # Subscription related
        'subscription_created': 'Subscription created successfully',
        'subscription_updated': 'Subscription updated successfully',
        'subscription_cancelled': 'Subscription cancelled successfully',
        'subscription_not_found': 'Subscription not found',
        'subscription_expired': 'Subscription has expired',
        'subscription_active': 'Subscription is active',
        
        # Purchase related
        'purchase_success': 'Purchase successful',
        'purchase_failed': 'Purchase failed',
        'purchase_eligibility_check_failed': 'Purchase eligibility check failed',
        'insufficient_balance_for_purchase': 'Insufficient balance for purchase',
        
        # Device related
        'device_not_found': 'Device not found',
        'device_limit_exceeded': 'Device limit exceeded',
        'device_registered': 'Device registered successfully',
        'device_unregistered': 'Device unregistered successfully',
    }
}


def get_user_language():
    """
    获取用户的语言设置
    优先级：URL参数 > Accept-Language头 > 默认语言
    """
    # 1. 检查URL参数
    lang = request.args.get('lang')
    if lang and lang in SUPPORTED_LANGUAGES:
        return lang
    
    # 2. 检查Accept-Language头
    if request.headers.get('Accept-Language'):
        accept_languages = request.headers.get('Accept-Language').split(',')
        for lang_header in accept_languages:
            lang = lang_header.strip().split(';')[0]
            if lang in SUPPORTED_LANGUAGES:
                return lang
            # 处理简化的语言代码 (如 'zh' -> 'zh-CN', 'en' -> 'en-US')
            if lang == 'zh':
                return 'zh-CN'
            elif lang == 'en':
                return 'en-US'
    
    # 3. 返回默认语言
    return DEFAULT_LANGUAGE


def t(key, *args, **kwargs):
    """
    翻译函数
    
    Args:
        key: 消息键
        *args: 格式化参数
        **kwargs: 格式化参数
    
    Returns:
        翻译后的消息
    """
    try:
        lang = get_user_language()
        message = MESSAGES.get(lang, {}).get(key)
        
        if not message:
            # 如果当前语言没有找到，尝试使用默认语言
            message = MESSAGES.get(DEFAULT_LANGUAGE, {}).get(key)
        
        if not message:
            # 如果都没有找到，返回键名
            logger.warning(f"Translation key '{key}' not found for language '{lang}'")
            return key
        
        # 格式化消息
        if args or kwargs:
            return message.format(*args, **kwargs)
        
        return message
        
    except Exception as e:
        logger.error(f"Error translating key '{key}': {e}")
        return key


def create_response(success=True, message_key=None, message=None, data=None, **kwargs):
    """
    创建标准化的API响应
    
    Args:
        success: 是否成功
        message_key: 消息键（用于国际化）
        message: 直接消息（如果提供，将覆盖message_key）
        data: 响应数据
        **kwargs: 其他响应字段
    
    Returns:
        标准化的响应字典
    """
    response = {
        'success': success,
        **kwargs
    }
    
    # 设置消息
    if message:
        response['message'] = message
    elif message_key:
        response['message'] = t(message_key)
    else:
        response['message'] = t('success' if success else 'failed')
    
    # 设置数据
    if data is not None:
        response['data'] = data
    
    return response


def success_response(message_key='success', data=None, **kwargs):
    """创建成功响应"""
    return create_response(success=True, message_key=message_key, data=data, **kwargs)


def error_response(message_key='failed', data=None, **kwargs):
    """创建错误响应"""
    return create_response(success=False, message_key=message_key, data=data, **kwargs)
