from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import Action<PERSON>hains # <-- Import ActionChains
import time
import json
import os
import sys
import platform
import logging
from .webdriver_pool import get_account_driver_manager # Import get_account_driver_manager
import threading
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from adspower_manager.models import AdspowerAccount
from adspower_manager.webdriver_pool import get_account_driver_manager, AccountWebDriverManager # Import AccountWebDriverManager
import re
import hashlib
from sqlalchemy.orm import object_session
from .adspower_protocol import AdsPowerProtocolAdapter  # 导入协议适配器

# 配置日志
logger = logging.getLogger(__name__)


class AdspowerAPI:
    """AdsPower API访问类"""

    def __init__(self, base_url="https://app-global.adspower.net", use_protocol=True):
        """初始化AdsPower API接口
        
        Args:
            base_url: AdsPower平台的基础URL
            use_protocol: 是否使用协议模式（默认True，使用协议模式）
        """
        self.base_url = base_url
        self.account_driver_lock = threading.RLock()
        self.use_protocol = use_protocol
        
        # 协议模式下使用协议适配器
        if self.use_protocol:
            self.protocol_adapter = AdsPowerProtocolAdapter(base_url)
            logger.info("[AdsPower API初始化] 模式:协议模式, 优势:更高效的API调用")
        else:
            logger.info("[AdsPower API初始化] 模式:Selenium模式(已弃用), 警告:建议使用协议模式")

    def _get_driver(self, account_id, cookies, username=None, password=None, totp_secret=None):
        """获取WebDriver实例

        Args:
            account_id: 账号ID
            cookies: 账号cookies
            username: 用户名（用于登录时使用）
            password: 密码（用于登录时使用）
            totp_secret: TOTP密钥（用于登录时使用）
            
        Returns:
            WebDriver: WebDriver实例
        """
        # --- 获取账号邮箱用于日志 ---
        # 尝试从 AccountWebDriverManager 的缓存获取 email
        driver_manager = get_account_driver_manager()
        account_email = username or driver_manager.account_drivers.get(str(account_id), {}).get('email', '未知邮箱')
        log_prefix = f"[AdsAPI] 账号 {account_email}" # 使用邮箱作为主要标识
        # ---\
        logger.debug(f"{log_prefix} 请求底层WebDriver实例")
        # 使用账号WebDriver管理器获取专用驱动
        driver = driver_manager.get_driver(
            account_id=account_id,
            cookies=cookies,
            username=username,
            password=password,
            totp_secret=totp_secret
        )
        return driver

    def _release_driver(self, account_id, driver):
        """释放WebDriver实例

        Args:
            account_id: 账号ID
            driver: 要释放的WebDriver实例
        """
        # --- 获取账号邮箱用于日志 ---
        driver_manager = get_account_driver_manager()
        account_email = driver_manager.account_drivers.get(str(account_id), {}).get('email', '未知邮箱')
        log_prefix = f"[AdsAPI] 账号 {account_email}" # 使用邮箱作为主要标识
        # ---\
        logger.debug(f"{log_prefix} _release_driver 被调用，但AccountWebDriverManager管理实例生命周期，无需操作")
        # 使用账号WebDriver管理器，无需释放，只需更新最后使用时间
        pass

    def _load_cookies(self, driver, cookies, account_id="未知账号", account_email="未知邮箱"):
        """加载Cookie到指定的WebDriver实例

        Args:
            driver: WebDriver实例
            cookies: 要加载的Cookie列表或字符串
            account_id: 账号ID (用于日志)
            account_email: 账号邮箱 (用于日志)

        Returns:
            bool: 是否成功加载Cookie
        """
        log_prefix = f"[AdsAPI] 账号 {account_email}" # 使用邮箱作为主要标识
        if self.use_protocol:
            return True
        try:
            # 先访问一次网站，才能添加Cookie
            driver.get(self.base_url)
            time.sleep(2)  # 等待页面加载

            if not cookies:
                logger.error(f"{log_prefix} 加载Cookie失败：没有可用的Cookies")
                return False

            logger.info(f"{log_prefix} 准备加载 {len(cookies) if isinstance(cookies, list) else '未知数量'} 个Cookie")

            driver.delete_all_cookies()

            if isinstance(cookies, str):
                try:
                    cookies = json.loads(cookies)
                except:
                    logger.error(f"{log_prefix} Cookies格式无效", exc_info=True)
                    return False

            for cookie in cookies:
                try:
                    if isinstance(cookie, str):
                        try:
                            cookie = json.loads(cookie)
                        except:
                            continue

                    if not isinstance(cookie, dict) or 'name' not in cookie or 'value' not in cookie:
                        continue

                    cookie_dict = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie.get('domain', '.adspower.net'),
                        'path': cookie.get('path', '/')
                    }

                    if 'expiry' in cookie:
                        cookie_dict['expiry'] = cookie['expiry']

                    driver.add_cookie(cookie_dict)
                except Exception as e:
                    logger.warning(f"{log_prefix} 添加单个Cookie失败: {str(e)}")

            driver.refresh()
            time.sleep(2)

            current_url = driver.current_url
            if "login" in current_url.lower():
                logger.warning(f"{log_prefix} Cookie加载后检测到登录页面，可能无效")
                return False

            return True
        except Exception as e:
            logger.error(f"{log_prefix} 加载Cookie时出错: {str(e)}", exc_info=True)
            return False

    def get_devices_info(self, account):
        """获取AdsPower账号的设备列表信息 (适配新的个人设置页面HTML)

        Args:
            account: AdspowerAccount对象

        Returns:
            list: 设备信息列表, 每个设备包含 'id', 'name', 'ip_address', 'status', 'last_open' 等键。
                  如果获取失败或无设备，返回空列表。
        """
        # 如果使用协议模式，调用协议适配器
        if self.use_protocol:
            return self.protocol_adapter.get_devices_info(account)
        
        # 以下是原有的Selenium实现
        method_start_time = time.time()
        account_id = str(account.id)
        # --- 获取 account_email 用于日志 ---
        account_email = account.username if hasattr(account, 'username') else '未知邮箱'
        log_prefix = f"[AdsAPI] 账号 {account_email}" # 使用邮箱作为主要标识
        logger.info(f"{log_prefix} 开始获取设备信息 (时间: {method_start_time:.3f})")

        # account_email = account.username # 这行可以移除，上面已获取
        # 获取原始的 Cookie (存储在 cookies 字段中)
        original_cookies_str = self._get_account_cookies(account) # 使用辅助方法获取
        driver = None # 初始化 driver
        instance_id = None # <-- Initialize instance_id to None here

        try:
            driver_get_start = time.time()
            logger.info(f"{log_prefix} 准备获取WebDriver实例用于设备扫描 (时间: {driver_get_start:.3f})")
            # 获取 driver manager 实例
            driver_manager = get_account_driver_manager()
            # 调用 driver_manager 获取 driver (只传递 account_id)
            # Manager 内部会处理凭据和 Cookie
            driver, instance_id = driver_manager.get_driver(
                account_id=account_id # 仅传递 account_id
            )
            driver_get_end = time.time()
            logger.info(f"{log_prefix} [资源获取成功] 获取浏览器实例 - 耗时:{driver_get_end - driver_get_start:.3f}秒, 状态:就绪")

            if not driver:
                logger.error(f"{log_prefix} [资源获取失败] 无法获取浏览器实例 - 影响:设备扫描失败, 用户无法查看设备列表")
                return None # 返回 None 表示驱动获取失败

            # --- 导航到个人设置页面
            device_page_url = f"{self.base_url}/personalSettings"
            navigation_start = time.time()
            logger.info(f"{log_prefix} 开始导航到个人设置页面: {device_page_url} (时间: {navigation_start:.3f})")
            driver.get(device_page_url)
            # 使用 WebDriverWait 等待页面加载完成，而不是固定 time.sleep
            try:
                WebDriverWait(driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div[class^='_session_control_']"))
                )
                navigation_end = time.time()
                logger.info(f"{log_prefix} [页面导航成功] 设备管理页面已加载 - URL:{driver.current_url}, 耗时:{navigation_end - navigation_start:.3f}秒")

                # --- END: Check and skip new user guide ---

            except TimeoutException: # This except is for the main page load wait
                navigation_end = time.time()
                msg = f"{log_prefix} 加载个人设置页面超时 (耗时: {navigation_end - navigation_start:.3f}秒)"
                logger.error(msg, exc_info=True)
                return None # 无法继续解析
            # --- END: Force Refresh ---

            # --- BEGIN: Parse device information ---
            parsing_successful = False # Initialize Flag (Moved earlier)
            try: # Outer try for parsing setup and 'More' button
                logger.info(f"{log_prefix} 开始解析设备列表...")
                session_control_area = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div[class^='_session_control_']"))
                )
                logger.info(f"{log_prefix} 找到登录活动区域")

                # --- Locate the 'Other Devices' container specifically ---
                other_devices_container_start = time.time()
                logger.info(f"{log_prefix} 开始查找'其他'设备容器... (时间: {other_devices_container_start:.3f})")
                try:
                    other_devices_container = session_control_area.find_element(By.CSS_SELECTOR, "div[class^='_other_device_']")
                    other_devices_container_end = time.time()
                    logger.info(f"{log_prefix} 找到'其他'设备容器 (耗时: {other_devices_container_end - other_devices_container_start:.3f}秒)")

                    # --- ADD EXPLICIT WAIT for scroll_wrapper ---
                    scroll_wrapper_selector = "div[class^='_scroll_wrapper_']"
                    scroll_wrapper = None # Initialize scroll_wrapper
                    device_elements = []  # Initialize device_elements to be used throughout
                    try:
                        logger.debug(f"{log_prefix} 在'其他设备容器'内等待滚动区域 '{scroll_wrapper_selector}' 可见...")
                        scroll_wrapper = WebDriverWait(other_devices_container, 5).until(
                            EC.visibility_of_element_located((By.CSS_SELECTOR, scroll_wrapper_selector))
                        )
                        logger.debug(f"{log_prefix} 滚动区域 '{scroll_wrapper_selector}' 已找到并可见。")
                        # Populate the main device_elements list here
                        device_elements = scroll_wrapper.find_elements(By.CSS_SELECTOR, "div[class^='_info_wrapper_']")
                        logger.info(f"{log_prefix} 从'其他'设备的滚动区域内获取到 {len(device_elements)} 个初始设备条目") # Changed log slightly
                    except TimeoutException:
                        logger.info(f"{log_prefix} 在'其他设备容器'内等待滚动区域 '{scroll_wrapper_selector}' 超时或未找到，假设无设备条目。")
                        # device_elements remains [] as initialized
                    # --- END ADD EXPLICIT WAIT ---

                except NoSuchElementException:
                    other_devices_container_end = time.time()
                    logger.info(f"{log_prefix} 未找到'其他'设备容器，可能没有其他设备记录 (耗时: {other_devices_container_end - other_devices_container_start:.3f}秒)")
                    return [] # Return empty list if other_devices_container itself is not found

                # --- 点击 "更多" 按钮逻辑 (Search within 'other_devices_container') ---
                more_button_search_start = time.time()
                
                # Use the length of the already fetched device_elements for initial_count
                initial_count = len(device_elements)
                logger.info(f"{log_prefix} 用于'更多'按钮逻辑的初始'其他'设备数量: {initial_count} (基于显式等待结果)")
                
                # 使用JavaScript快速检查是否存在"更多"按钮
                logger.info(f"{log_prefix} 使用JavaScript检查'更多'按钮... (时间: {more_button_search_start:.3f})")
                
                has_more_button_js = """
                    return document.querySelector("div[class^='_other_device_'] div[class^='_see_more_']") !== null;
                """ # Updated selector for more button
                more_button_exists = driver.execute_script(has_more_button_js)
                more_button_search_js_end = time.time()
                logger.info(f"{log_prefix} JavaScript检查'更多'按钮完成 (检查耗时: {more_button_search_js_end - more_button_search_start:.3f}秒)")
                
                if more_button_exists and scroll_wrapper: # Only proceed if scroll_wrapper was found
                    logger.info(f"{log_prefix} JavaScript检测到'更多'按钮存在")
                    click_more_button_js = """
                        var moreBtn = document.querySelector("div[class^='_other_device_'] div[class^='_see_more_']"); // Updated selector
                        if (moreBtn) {
                            moreBtn.click();
                            return true;
                        }
                        return false;
                    """
                    more_button_click_start = time.time()
                    logger.info(f"{log_prefix} 使用JavaScript点击'更多'按钮... (时间: {more_button_click_start:.3f})")
                    click_success = driver.execute_script(click_more_button_js)
                    
                    if click_success:
                        logger.info(f"{log_prefix} JavaScript成功点击'更多'按钮 (时间: {time.time():.3f})")
                        
                        wait_for_more_start = time.time()
                        logger.info(f"{log_prefix} 开始等待设备列表更新... (时间: {wait_for_more_start:.3f})")
                        try:
                            # Wait for the number of _info_wrapper_ elements inside the *found* scroll_wrapper to increase
                            WebDriverWait(scroll_wrapper, 5).until(
                                lambda d: len(d.find_elements(By.CSS_SELECTOR, "div[class^='_info_wrapper_']")) > initial_count
                            )
                            wait_for_more_end = time.time()
                            # Re-fetch device_elements from the same scroll_wrapper
                            device_elements = scroll_wrapper.find_elements(By.CSS_SELECTOR, "div[class^='_info_wrapper_']")
                            final_count_after_wait = len(device_elements)
                            logger.info(f"{log_prefix} 设备列表更新成功，'其他'设备数量从 {initial_count} 增加到 {final_count_after_wait} (等待耗时: {wait_for_more_end - wait_for_more_start:.3f}秒)")
                        except TimeoutException:
                            wait_for_more_end = time.time()
                            logger.warning(f"{log_prefix} 等待设备列表更新超时 (等待: {wait_for_more_end - wait_for_more_start:.3f}秒)，'其他'设备数量未增加 (初始: {initial_count})")
                            # device_elements remains as it was before clicking 'More'
                    else:
                        logger.warning(f"{log_prefix} JavaScript未能点击'更多'按钮，可能点击时按钮已消失 (时间: {time.time():.3f})")
                elif scroll_wrapper: # if more button does not exist but scroll_wrapper does
                    logger.info(f"{log_prefix} JavaScript未检测到'更多'按钮，继续解析当前显示的 {len(device_elements)} 个设备")
                # If scroll_wrapper was not found, device_elements is already [], and we skip 'More' logic
                
                # --- REMOVE Redundant device element fetching section ---
                # The device_elements list is now populated correctly from the explicit wait and 'More' button logic

                devices = []
                js_extraction_successful = False

                # --- 尝试 JS 批量提取 ---
                if device_elements:
                    js_extraction_start = time.time()
                    logger.info(f"{log_prefix} 开始JavaScript批量提取'其他'设备信息... (时间: {js_extraction_start:.3f})")
                    try:
                        # --- Use the JS script from the PREVIOUS edit (which doesn't check isCurrent) ---
                        batch_script = r"""
                        return Array.from(arguments[0]).map(el => {
                            let name = '未知设备';
                            try {
                                let nameElem = el.querySelector("div[class^='_top_info_'] span");
                                if (nameElem && nameElem.textContent.trim()) {
                                    name = nameElem.textContent.trim();
                                } else {
                                    let parentElem = el.querySelector("div[class^='_top_info_']");
                                    name = parentElem ? parentElem.textContent.trim() : name;
                                }
                            } catch (e) { console.error("Error extracting device name: ", e); }

                            let ip = '';
                            try {
                                let ipElem = el.querySelector("div[class^='_bottom_info_']");
                                let ipText = ipElem ? ipElem.textContent.trim() : '';
                                // Regex to find IP (v4 or v6)
                                let ipMatch = ipText.match(/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|[0-9a-fA-F:]{3,})/);
                                if (ipMatch) ip = ipMatch[0]; // Use group 0 for the whole match
                            } catch (e) { console.error("Error extracting IP: ", e); }

                            let time = null;
                            try {
                                // Use the specific time span selector from Python logic
                                let dateElem = el.querySelector("span[class^='_date_']");
                                if (dateElem) time = dateElem.textContent.trim();
                            } catch (e) { console.error("Error extracting time: ", e); }


                            let deviceType = 'Unknown';
                            let rawHref = null;
                            try {
                                let useElem = el.querySelector("div[class^='_icon_'] svg use");
                                if (useElem) {
                                    rawHref = useElem.getAttribute('xlink:href') || useElem.getAttribute('href');
                                    if (rawHref && typeof rawHref === 'string' && rawHref.includes('#')) {
                                        deviceType = rawHref.split('#')[1] || 'Unknown';
                                    }
                                }
                            } catch (e) { console.error("Error extracting device type icon: ", e); }

                            return { name: name, ip: ip, time: time, type: deviceType };
                        });
                        """
                        # Pass the CORRECT device_elements (from scroll_wrapper)
                        js_execute_start = time.time()
                        logger.info(f"{log_prefix} 执行JavaScript脚本... (时间: {js_execute_start:.3f})")
                        all_devices_info = driver.execute_script(batch_script, device_elements)
                        js_execute_end = time.time()
                        logger.debug(f"{log_prefix} JavaScript原始返回数据: {json.dumps(all_devices_info)}")
                        logger.info(f"{log_prefix} JavaScript脚本执行完成 (耗时: {js_execute_end - js_execute_start:.3f}秒)，获取到 {len(all_devices_info)} 个原始'其他'设备信息")

                        js_processing_start = time.time()
                        logger.info(f"{log_prefix} 开始处理JavaScript返回的设备信息... (时间: {js_processing_start:.3f})")
                        processed_count = 0
                        for idx, info in enumerate(all_devices_info):
                            # No need to check isCurrent here, as we only passed 'other' devices
                            device_name = info.get('name', f'未知设备_{account_id}_{idx}')
                            generated_id = f"parsed_{hashlib.md5(device_name.encode()).hexdigest()[:8]}"
                            device_data = {
                                'id': generated_id,
                                'name': device_name,
                                'status': 'offline', # Assuming all 'other' devices are offline for this purpose
                                'ip_address': info.get('ip', ''),
                                'last_login': info.get('time'),
                                'device_type': info.get('type', 'Unknown')
                            }
                            devices.append(device_data)
                            processed_count += 1
                        js_processing_end = time.time()
                        js_extraction_end = time.time()
                        logger.info(f"{log_prefix} JavaScript返回数据处理完成 (耗时: {js_processing_end - js_processing_start:.3f}秒)，得到 {processed_count} 个'其他'设备信息")
                        logger.info(f"{log_prefix} 整个JavaScript提取过程完成 (总耗时: {js_extraction_end - js_extraction_start:.3f}秒)")
                        js_extraction_successful = True
                        parsing_successful = True # <--- JS success = parsing success

                    except Exception as js_e:
                        js_extraction_end = time.time()
                        logger.warning(f"{log_prefix} JavaScript批量提取'其他'设备信息失败 (耗时: {js_extraction_end - js_extraction_start:.3f}秒): {str(js_e)}", exc_info=True)
                        devices = [] # Reset list for fallback
                        js_extraction_successful = False
                        # parsing_successful remains False, let Python try

                else:
                    # No elements found initially in the scroll wrapper
                    logger.info(f"{log_prefix} 未在'其他'设备滚动区域找到设备元素。")
                    parsing_successful = True # Successfully determined there are 0 'other' elements

                # --- 回退到 Python 循环 ---
                if not js_extraction_successful and device_elements: # Iterate over the correct device_elements
                    python_fallback_start = time.time()
                    logger.info(f"{log_prefix} JavaScript提取失败，开始Python回退解析... (时间: {python_fallback_start:.3f})")
                    if not device_elements:
                         logger.info(f"{log_prefix} 未找到'其他'设备元素，无法进行Python回退解析。")
                         # parsing_successful remains False (or True if JS path set it)
                    else:
                        for i, element in enumerate(device_elements): # Use correct elements
                            # --- Python parsing loop (NO NEED to check for online status) ---
                            device_info = {}
                            try:
                                # REMOVED: Online status check is not needed as we target 'other' devices
                                name_element = element.find_element(By.CSS_SELECTOR, "div[class^='_top_info_'] span")
                                device_info['name'] = name_element.text.strip()
                                if not device_info['name']:
                                    name_parent = element.find_element(By.CSS_SELECTOR, "div[class^='_top_info_']")
                                    device_info['name'] = name_parent.text.strip()

                                type_element = element.find_element(By.CSS_SELECTOR, "div[class^='_icon_'] svg use")
                                href = type_element.get_attribute('xlink:href') or type_element.get_attribute('href')
                                device_info['device_type'] = href.split('#')[1] if href and '#' in href else 'Unknown'

                                ip_address = ""
                                last_login = None
                                try:
                                    bottom_info_elem = element.find_element(By.CSS_SELECTOR, "div[class^='_bottom_info_']")
                                    bottom_info_text = bottom_info_elem.text.strip()
                                    ip_match = re.search(r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|[0-9a-fA-F:]+)', bottom_info_text)
                                    if ip_match: ip_address = ip_match.group(1)
                                    else: logger.warning(f"{log_prefix} [Python] 未能在'其他'设备 '{device_info.get('name', '未知')}' 的文本 '{bottom_info_text}' 中匹配到IP地址")

                                    time_element = element.find_element(By.CSS_SELECTOR, "span[class^='_date_']")
                                    last_login = time_element.text.strip()
                                except NoSuchElementException:
                                    logger.warning(f"{log_prefix} [Python] 解析'其他'设备 {device_info.get('name', '未知')} 的底部信息(IP/时间)时未找到特定元素")
                                except Exception as bottom_e:
                                    logger.error(f"{log_prefix} [Python] 解析'其他'设备 {device_info.get('name', '未知')} 的底部信息时发生意外错误: {bottom_e}", exc_info=True)

                                device_info['ip_address'] = ip_address
                                device_info['last_login'] = last_login
                                device_info['id'] = f"parsed_{hashlib.md5(device_info['name'].encode()).hexdigest()[:8]}"
                                device_info['status'] = 'offline' # Assuming 'other' devices are offline
                                devices.append(device_info)
                                logger.debug(f"{log_prefix} [Python] 成功解析'其他'设备: {device_info}")
                            except NoSuchElementException as parse_inner_err:
                                logger.warning(f"{log_prefix} [Python] 解析单个'其他'设备元素时出错(可能是名称或类型): {parse_inner_err} - 跳过元素 {i+1}")
                                continue
                            except Exception as parse_generic_err:
                                logger.error(f"{log_prefix} [Python] 解析单个'其他'设备元素 {i+1} 时发生未知错误: {parse_generic_err}", exc_info=True)
                                continue
                        # --- End of Python loop ---
                        python_fallback_end = time.time()
                        if devices: # If Python fallback produced results
                             parsing_successful = True
                             logger.info(f"{log_prefix} Python回退解析完成 (耗时: {python_fallback_end - python_fallback_start:.3f}秒)，共找到 {len(devices)} 个有效'其他'设备信息")
                        else: # Python fallback also failed or found nothing
                             logger.warning(f"{log_prefix} Python回退解析未找到有效'其他'设备信息 (耗时: {python_fallback_end - python_fallback_start:.3f}秒)")
                             # If JS failed AND Python found nothing, parsing_successful might still be False.
                             # Let's ensure it's True if we attempted Python fallback, even if 0 results.
                             parsing_successful = True


            except Exception as parse_outer_err: # Correctly aligned with the outer 'try' starting line 226
                # Catches errors in finding session_control_area, other_devices_container etc.
                logger.error(f"{log_prefix} 解析'其他'设备列表时发生外部错误: {parse_outer_err}", exc_info=True) # Correct indentation
                parsing_successful = False # Definite failure here
                if driver and instance_id:
                    driver_manager._save_error_screenshot(driver, account_id, instance_id, "get_devices_parsing_error")
                return None # <--- Return None for definite parsing failure

            # --- END: Parse device information ---
            parsing_end_time = time.time()
            method_end_time = time.time()
            log_method = "JS" if js_extraction_successful else "Python Fallback" if parsing_successful else "Failed"
            logger.info(f"{log_prefix} '其他'设备信息提取完成 (方法: {log_method})，共 {len(devices)} 个设备")
            logger.info(f"{log_prefix} 整个get_devices_info方法执行完成 (总耗时: {method_end_time - method_start_time:.3f}秒)")

            # Return devices list if parsing attempt was completed (parsing_successful is True)
            # Return None only if major exceptions occurred before or during parsing setup
            # If parsing_successful is True, return devices (even if empty)
            # If parsing_successful is False (due to parse_outer_err), None was already returned.
            # The logic implicitly handles this: if we reach here, parsing_successful should be True.
            logger.info(f"{log_prefix} 获取到的设备信息: {devices}") # <-- 新增日志记录
            return devices


        except (TimeoutException, WebDriverException) as wd_e:
             logger.error(f"{log_prefix} WebDriver/Timeout 错误导致获取设备信息失败: {getattr(wd_e, 'msg', str(wd_e))}", exc_info=True)
             # parsing_successful remains False (initial value)
             return None # Return None for major webdriver/timeout issues

        except Exception as e:
            logger.error(f"{log_prefix} 获取设备信息时发生未知错误: {str(e)}", exc_info=True)
            # parsing_successful remains False (initial value)
            if driver and instance_id:
                 driver_manager._save_error_screenshot(driver, account_id, instance_id, "get_devices_outer_error")
            return None # Return None for major unexpected errors

        finally:
            if instance_id and account_id:
                release_success = locals().get('parsing_successful', False)
                logger.debug(f"{log_prefix} get_devices_info 完成，准备释放实例 {instance_id} 回管理器 (操作成功标志: {release_success})")
                try:
                    driver_manager.release_driver(account_id, instance_id, success=release_success)
                    logger.info(f"{log_prefix} 实例 {instance_id} 已释放回管理器")
                except Exception as release_e:
                    logger.error(f"{log_prefix} 释放实例 {instance_id} 时出错: {release_e}", exc_info=True)
            elif driver: # Should not happen if instance_id exists
                 logger.warning(f"{log_prefix} 有 driver 对象但无 instance_id 或 account_id，无法正常释放！")

        # Should not be reached
        logger.error(f"{log_prefix} get_devices_info 意外到达函数末尾，返回 None")
        return None # Return None if somehow reached end

    def get_current_devices_count(self, account):
        """获取当前设备数量

        Args:
            account: AdspowerAccount对象

        Returns:
            int: 设备数量
        """
        # --- 获取账号邮箱用于日志 ---
        account_email = account.username if hasattr(account, 'username') else '未知邮箱'
        log_prefix = f"[AdsAPI] 账号 {account_email}" # 使用邮箱作为主要标识
        # ---\
        logger.debug(f"{log_prefix} 请求设备数量")
        devices = self.get_devices_info(account)
        count = len(devices) if devices is not None else -1 # Return -1 if get_devices_info failed
        logger.debug(f"{log_prefix} 设备数量: {count}")
        return count if count >= 0 else 0 # Return 0 if count is -1 (error)

    def check_account_login_status(self, account):
        """检查账号的登录状态和Cookie有效性

        Args:
            account: AdspowerAccount对象

        Returns:
            bool: 是否已登录且Cookie有效
        """
        account_id = str(account.id)
        account_email = account.username if hasattr(account, 'username') else '未知邮箱'
        log_prefix = f"[AdsAPI] 账号 {account_email}" # 使用邮箱作为主要标识
        logger.info(f"{log_prefix} 开始检查登录状态和Cookie有效性")
        
        if self.use_protocol:
            # 使用协议模式检查登录状态
            try:
                # 通过获取登录信息来验证登录状态
                session = self.protocol_adapter.get_session(str(account.id))
                
                # 如果会话已登录，尝试获取登录信息验证
                if session.is_logged_in:
                    result = session.get_login_info()
                    if result.get("response", {}).get("code") == 0:
                        logger.info(f"{log_prefix} 协议模式验证登录状态有效")
                        return True
                    else:
                        logger.warning(f"{log_prefix} 协议模式获取登录信息失败，登录状态可能无效")
                        session.is_logged_in = False
                        return False
                else:
                    logger.info(f"{log_prefix} 协议模式会话未登录")
                    return False
            except Exception as e:
                logger.error(f"{log_prefix} 协议模式检查登录状态时出错: {e}", exc_info=True)
                return False
        driver_manager = get_account_driver_manager()
        driver = None
        instance_id = None # <-- Initialize instance_id to None here

        # --- 不再需要检查缓存的健康状态，直接尝试获取驱动 ---
        # is_healthy = driver_manager.is_account_healthy(account_id)
        # if is_healthy: ... (移除旧的缓存检查逻辑)

        try:
            # 获取 driver (这会触发健康检查和可能的登录)
            # 只传递 account_id
            logger.debug(f"{log_prefix} 尝试从管理器获取实例以检查登录状态...")
            driver, instance_id = driver_manager.get_driver(account_id=account_id, timeout=60)

            if not driver:
                logger.error(f"{log_prefix} 无法获取WebDriver进行登录状态检查 (管理器返回 None)")
                # Explicitly update status? Maybe not needed, manager handles internal state.
                # driver_manager.health_status[account_id] = ...
                return False

            # --- 如果成功获取驱动，说明实例是健康的 (READY 状态) ---
            logger.info(f"{log_prefix} 成功获取到健康实例 {instance_id}，Cookie 有效")

            return True # Getting a READY driver means it's logged in and healthy

        except (RuntimeError, ValueError, TimeoutError) as manager_e: # Catch driver acquisition errors
             logger.warning(f"{log_prefix} 获取实例进行健康检查失败: {manager_e}")
             return False # Cannot get driver, assume unhealthy/login failed
        except Exception as e:
             logger.error(f"{log_prefix} 检查登录状态时发生意外错误: {e}", exc_info=True)
             return False # Unexpected error, assume failure
        finally:
             # 确保获取到的实例被释放回管理器
             if instance_id and account_id:
                  try:
                       logger.debug(f"{log_prefix} check_account_login_status 完成，释放实例 {instance_id}")
                       # If an error occurred *before* getting the driver, instance_id is None.
                       # If an error occurred *after*, assume success=False? Or let manager handle?
                       # Let's assume success=True here, as the check itself was just getting the driver.
                       # If getting driver failed, manager_e was caught. If other error, return False anyway.
                       driver_manager.release_driver(account_id, instance_id, success=True)
                  except Exception as release_e:
                       logger.error(f"{log_prefix} 释放实例 {instance_id} 时出错: {release_e}", exc_info=True)

    def disable_account(self, account):
        """禁用账号（Cookie失效时）

        Args:
            account: AdspowerAccount对象

        Returns:
            bool: 是否成功禁用
        """
        try:
            account_id = str(account.id)
            account_email = account.username if hasattr(account, 'username') else '未知邮箱'
            log_prefix = f"[AdsAPI] 账号 {account_email}" # 使用邮箱作为主要标识
            logger.warning(f"{log_prefix} 准备禁用账号")
            
            if self.use_protocol:
                # 协议模式下清除会话
                if str(account.id) in self.protocol_adapter.sessions:
                    del self.protocol_adapter.sessions[str(account.id)]
                    logger.info(f"{log_prefix} 已清除协议会话")
            driver_manager = get_account_driver_manager()
            # 尝试关闭与此账号关联的驱动（如果存在）
            driver_manager.close_driver(account_id)

            account.is_active = False
            account.last_error = "Cookie已失效或登录失败，账号已自动禁用"
            account.last_check_time = int(time.time())

            try:
                from sqlalchemy.orm import object_session
                session = object_session(account)
                if session:
                    session.add(account)
                    session.commit()
                    logger.info(f"{log_prefix} 账号已成功禁用")
                    return True
                else:
                    logger.error(f"{log_prefix} 禁用账号失败：无法获取数据库会话")
                    return False # Added return False here
            except Exception as e:
                logger.error(f"{log_prefix} 禁用账号时数据库操作出错: {str(e)}", exc_info=True)
                session = object_session(account)
                if session:
                    session.rollback()
                return False

        except Exception as e:
            logger.error(f"[AdsAPI] 禁用账号 ({account.id if account else '未知'}) 过程中发生外部错误: {str(e)}", exc_info=True)
            return False

    def _get_account_cookies(self, account):
        """从账号对象中获取cookies

        Args:
            account: AdspowerAccount对象

        Returns:
            str or None: cookies字符串，如果无效或没有则返回None
        """
        account_id_str = str(account.id) if account else "未知ID"
        account_email = getattr(account, 'username', '未知邮箱')
        log_prefix = f"[AdsAPI] 账号 {account_email}" # 使用邮箱作为主要标识

        try:
            if not account:
                logger.error(f"[AdsAPI] 获取Cookie失败：account 对象为空")
                return None

            # 优先从新的 cookies 字段读取
            if hasattr(account, 'cookies') and account.cookies:
                 if isinstance(account.cookies, str):
                     # 尝试验证是否是有效的JSON，但即使不是也返回原始字符串，让调用者处理
                     try:
                         json.loads(account.cookies)
                         # logger.debug(f"{log_prefix} cookies 字段包含有效的JSON Cookie")
                     except (json.JSONDecodeError, TypeError):
                         logger.warning(f"{log_prefix} 的 cookies 不是有效的JSON字符串，但仍返回原始内容")
                     return account.cookies
                 else:
                     # 如果不是字符串，尝试序列化（虽然理论上不应该发生）
                     try:
                         logger.warning(f"{log_prefix} 的 cookies 字段不是字符串，尝试序列化")
                         return json.dumps(account.cookies)
                     except Exception as dump_e:
                         logger.error(f"{log_prefix} 无法序列化 cookies: {dump_e}", exc_info=True)
                         return None
            else:
                # logger.info(f"{log_prefix} 没有可用的cookies (cookies字段为空或无效)") # 改为debug级别？
                logger.debug(f"{log_prefix} 没有可用的cookies (cookies字段为空)")
                return None
        except Exception as e:
            logger.error(f"{log_prefix} 获取账号cookies时出错: {str(e)}", exc_info=True)
            return None

    def logout_device(self, account, device_id):
        """通过API退出指定设备

        Args:
            account: AdspowerAccount 对象
            device_id: 设备唯一标识符

        Returns:
            tuple: (bool, str) 操作是否成功及中文消息
        """
        # 如果使用协议模式，调用协议适配器
        if self.use_protocol:
            return self.protocol_adapter.logout_device(account, device_id)
        # 如果不使用协议模式，返回不支持的错误
        return False, "当前配置不支持通过浏览器退出设备，请使用协议模式"

    def check_account_health(self, account):
        """检查账号健康状态
        
        通过获取登录信息来判断账号是否健康，如果不健康则尝试重新登录
        
        Args:
            account: AdspowerAccount对象
            
        Returns:
            tuple: (is_healthy, status_message, login_info)
                - is_healthy: 账号是否健康
                - status_message: 状态消息
                - login_info: 登录信息（如果健康）
        """
        account_email = account.username if hasattr(account, 'username') else '未知邮箱'
        log_prefix = f"[AdsAPI] 账号 {account_email}"
        
        # 如果使用协议模式，调用协议适配器的健康检查
        if self.use_protocol:
            logger.info(f"{log_prefix} 使用协议模式检查账号健康状态")
            return self.protocol_adapter.check_account_health(account)
        
        # Selenium模式下不再支持
        logger.error(f"{log_prefix} 健康检查仅支持协议模式")
        return False, "不支持的操作模式", None

    def get_login_info(self, account):
        """获取账号的登录信息
        
        Args:
            account: AdspowerAccount对象
            
        Returns:
            dict: 包含响应和解密数据的字典，如果失败返回None
        """
        account_email = account.username if hasattr(account, 'username') else '未知邮箱'
        log_prefix = f"[AdsAPI] 账号 {account_email}"
        
        # 只在协议模式下支持
        if not self.use_protocol:
            logger.warning(f"{log_prefix} get_login_info 仅在协议模式下支持")
            return None
        
        logger.info(f"{log_prefix} 获取登录信息")
        session = self.protocol_adapter.get_session(str(account.id))
        
        # 确保已登录
        if not session.is_logged_in:
            success, msg, cookies = self.protocol_adapter.login_account(account)
            if not success:
                logger.error(f"{log_prefix} 获取登录信息前登录失败: {msg}")
                return None
        
        try:
            result = session.get_login_info()
            return result
        except Exception as e:
            logger.error(f"{log_prefix} 获取登录信息时出错: {e}", exc_info=True)
            return None


# 全局AdsPower API实例
_adspower_api = None


def get_adspower_api():
    """获取全局AdsPower API实例"""
    global _adspower_api
    if _adspower_api is None:
        _adspower_api = AdspowerAPI()
    return _adspower_api

# --- Cookie Handling --- #

def get_stored_cookies(account):
    """从 AdspowerAccount 对象获取存储的 Cookies (JSON字符串)

    Args:
        account: AdspowerAccount 对象

    Returns:
        str: Cookies JSON 字符串，如果无效或不存在则返回 None
    """
    log_prefix = f"[Cookie Util 账号 {account.username} (ID: {account.id})]"
    # 优先从新的 cookies 字段读取
    if hasattr(account, 'cookies') and account.cookies:
        if isinstance(account.cookies, str):
            # 验证是否是有效的 JSON
            try:
                json.loads(account.cookies)
                logger.debug(f"{log_prefix} cookies 字段包含有效的JSON Cookie")
                return account.cookies
            except json.JSONDecodeError:
                logger.warning(f"{log_prefix} 的 cookies 不是有效的JSON字符串，但仍返回原始内容")
                return account.cookies # 返回原始字符串供调试?
            except Exception as parse_err:
                logger.error(f"{log_prefix} 解析 cookies 字符串时出错: {parse_err}", exc_info=True)
                return None
        elif isinstance(account.cookies, (list, dict)):
            # 如果是列表或字典，尝试序列化（理论上不应发生，应存储为字符串）
            logger.warning(f"{log_prefix} 的 cookies 字段不是字符串，尝试序列化")
            try:
                return json.dumps(account.cookies)
            except Exception as dump_e:
                logger.error(f"{log_prefix} 无法序列化 cookies: {dump_e}", exc_info=True)
                return None
        else:
            logger.warning(f"{log_prefix} cookies 字段类型未知: {type(account.cookies)}，无法使用")
            return None
            
    # 如果 cookies 字段为空或不存在，可以考虑是否需要回退到 remarks (如果确定旧数据可能存在那里)
    # 但为了清晰，这里默认只使用 cookies 字段
    # logger.info(f"{log_prefix} 没有可用的cookies (cookies字段为空或无效)")
    return None

# def _login_and_get_cookies(self, account):
#     # ... (函数内其他逻辑不变) ...
    
#     try:
#         # ... (登录操作代码) ...
        
#         if login_success:
#             logger.info(f"{log_prefix} 登录成功，获取新 Cookies")
#             new_cookies_list = self.driver.get_cookies()
            
#             if new_cookies_list:
#                 try:
#                     new_cookies_str = json.dumps(new_cookies_list)
#                     # 更新 account 对象的 cookies 字段
#                     account.cookies = new_cookies_str 
#                     # 如果需要，也更新 remarks? (注释掉)
#                     # account.remarks = new_cookies_str 
#                     db.session.add(account) # 将更改添加到会话
#                     # 注意：这里不 commit，由调用者负责 commit
#                     logger.info(f"{log_prefix} 新 Cookies 已更新到 account 对象的 cookies 字段 (未提交)")
#                     return new_cookies_list, None
#                 except Exception as json_err:
#                     error_message = f"序列化新 Cookie 失败: {json_err}"
#                     logger.error(f"{log_prefix} {error_message}")
#                     return None, error_message
#             else:
#                 error_message = "登录成功但未能获取 Cookies"
#                 logger.warning(f"{log_prefix} {error_message}")
#                 return None, error_message
#         else:
#             # login_result 包含错误信息
#             return None, login_result[1]
#     except Exception as e:
#          # ... (异常处理) ...
#         return None, f"登录或获取 Cookie 时发生异常: {e}"
#     finally:
#         # ... (清理逻辑) ...
#         pass
