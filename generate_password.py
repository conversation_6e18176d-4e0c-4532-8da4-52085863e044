#!/usr/bin/env python3
"""
生成 bcrypt 密码哈希工具
使用方法: python generate_password.py
"""

import bcrypt
import getpass
import sys


def generate_password_hash(password: str, rounds: int = 12) -> str:
    """生成 bcrypt 密码哈希"""
    salt = bcrypt.gensalt(rounds=rounds)
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')


def verify_password(password: str, hashed: str) -> bool:
    """验证密码"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))


def main():
    print("=== Bcrypt 密码哈希生成器 ===\n")
    
    while True:
        print("选择操作:")
        print("1. 生成密码哈希")
        print("2. 验证密码哈希")
        print("3. 退出")
        
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            # 生成密码哈希
            password = getpass.getpass("请输入密码: ")
            if not password:
                print("密码不能为空！\n")
                continue
                
            confirm = getpass.getpass("请再次输入密码: ")
            if password != confirm:
                print("两次输入的密码不一致！\n")
                continue
            
            rounds = input("请输入成本因子 (默认12): ").strip()
            rounds = int(rounds) if rounds.isdigit() else 12
            
            if rounds < 4 or rounds > 31:
                print("成本因子必须在 4-31 之间！\n")
                continue
            
            hash_value = generate_password_hash(password, rounds)
            print(f"\n生成的密码哈希:")
            print(f"{hash_value}")
            print(f"\n哈希详情:")
            print(f"- 算法版本: $2b$")
            print(f"- 成本因子: {rounds} (迭代 {2**rounds} 次)")
            print(f"- 哈希长度: {len(hash_value)} 字符\n")
            
        elif choice == '2':
            # 验证密码哈希
            hash_input = input("请输入要验证的哈希值: ").strip()
            if not hash_input.startswith('$2b$'):
                print("无效的 bcrypt 哈希格式！\n")
                continue
                
            password = getpass.getpass("请输入密码: ")
            
            try:
                is_valid = verify_password(password, hash_input)
                if is_valid:
                    print("✓ 密码正确！\n")
                else:
                    print("✗ 密码错误！\n")
            except Exception as e:
                print(f"验证失败: {e}\n")
                
        elif choice == '3':
            print("再见！")
            sys.exit(0)
            
        else:
            print("无效选择，请重试！\n")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n操作已取消")
        sys.exit(0)