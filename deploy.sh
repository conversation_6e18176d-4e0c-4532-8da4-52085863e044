#!/bin/bash

# AI拼车共享平台一键部署脚本
# 适用于 Ubuntu 20.04+ / CentOS 7+ / Debian 10+
# 使用方法: curl -fsSL https://raw.githubusercontent.com/fzlzjerry/gpt_share_automation/main/deploy.sh | bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 安装基础软件
install_dependencies() {
    log_step "安装基础软件包..."
    
    if [[ $OS == *"Ubuntu"* ]] || [[ $OS == *"Debian"* ]]; then
        sudo apt update
        sudo apt install -y git curl wget vim nginx python3 python3-pip python3-venv \
            build-essential libssl-dev libffi-dev python3-dev \
            sqlite3 supervisor certbot python3-certbot-nginx \
            ufw fail2ban htop
    elif [[ $OS == *"CentOS"* ]] || [[ $OS == *"Red Hat"* ]]; then
        sudo yum update -y
        sudo yum install -y git curl wget vim nginx python3 python3-pip \
            gcc openssl-devel libffi-devel python3-devel \
            sqlite supervisor certbot python3-certbot-nginx \
            firewalld fail2ban htop
    else
        log_error "不支持的操作系统: $OS"
        exit 1
    fi
}

# 安装Docker
install_docker() {
    log_step "安装Docker..."
    
    if ! command -v docker &> /dev/null; then
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker $USER
        rm get-docker.sh
    else
        log_info "Docker已安装"
    fi
    
    # 安装Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose
    else
        log_info "Docker Compose已安装"
    fi
    
    # 启动Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    if [[ $OS == *"Ubuntu"* ]] || [[ $OS == *"Debian"* ]]; then
        sudo ufw --force reset
        sudo ufw default deny incoming
        sudo ufw default allow outgoing
        sudo ufw allow ssh
        sudo ufw allow 80
        sudo ufw allow 443
        sudo ufw --force enable
    elif [[ $OS == *"CentOS"* ]] || [[ $OS == *"Red Hat"* ]]; then
        sudo systemctl start firewalld
        sudo systemctl enable firewalld
        sudo firewall-cmd --permanent --add-service=ssh
        sudo firewall-cmd --permanent --add-service=http
        sudo firewall-cmd --permanent --add-service=https
        sudo firewall-cmd --reload
    fi
}

# 下载项目
download_project() {
    log_step "下载项目代码..."
    
    PROJECT_DIR="/opt/aicarpools"
    sudo mkdir -p $PROJECT_DIR
    sudo chown $USER:$USER $PROJECT_DIR
    
    cd $PROJECT_DIR
    
    if [[ -d .git ]]; then
        log_info "项目已存在，更新代码..."
        git pull origin main
    else
        git clone https://github.com/fzlzjerry/gpt_share_automation.git .
    fi
}

# 配置环境
configure_environment() {
    log_step "配置环境变量..."
    
    cd /opt/aicarpools
    
    if [[ ! -f .env.production ]]; then
        cp .env.example .env.production
        
        # 生成随机密钥
        SECRET_KEY=$(openssl rand -hex 32)
        JWT_SECRET_KEY=$(openssl rand -hex 32)
        
        # 更新配置文件
        sed -i "s/your-super-secret-key-change-this-in-production/$SECRET_KEY/" .env.production
        sed -i "s/your-jwt-secret-key-change-this-too/$JWT_SECRET_KEY/" .env.production
        sed -i "s/FLASK_ENV=development/FLASK_ENV=production/" .env.production
        
        log_warn "请编辑 /opt/aicarpools/.env.production 文件，配置邮件服务器等信息"
    else
        log_info "环境配置文件已存在"
    fi
}

# 创建Docker配置
create_docker_config() {
    log_step "创建Docker配置..."
    
    cd /opt/aicarpools
    
    # 创建docker-compose.yml
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  app:
    build: .
    container_name: aicarpools-app
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      - ./instance:/app/instance
      - ./logs:/app/logs
      - ./.env.production:/app/.env
    environment:
      - FLASK_ENV=production
    depends_on:
      - redis
    networks:
      - aicarpools-network

  redis:
    image: redis:7-alpine
    container_name: aicarpools-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - aicarpools-network

volumes:
  redis_data:

networks:
  aicarpools-network:
    driver: bridge
EOF
}

# 创建Nginx配置
create_nginx_config() {
    log_step "创建Nginx配置..."
    
    # 获取域名
    read -p "请输入您的域名 (例如: aicarpools.com): " DOMAIN
    if [[ -z "$DOMAIN" ]]; then
        DOMAIN="aicarpools.com"
    fi
    
    # 创建Nginx配置文件
    sudo tee /etc/nginx/sites-available/aicarpools << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN dashboard.$DOMAIN;

    # 临时配置，用于获取SSL证书
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/aicarpools /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    sudo nginx -t
    sudo systemctl restart nginx
    sudo systemctl enable nginx
}

# 部署应用
deploy_application() {
    log_step "部署应用..."
    
    cd /opt/aicarpools
    
    # 构建并启动服务
    docker-compose build
    docker-compose up -d
    
    # 等待服务启动
    sleep 10
    
    # 初始化数据库
    docker-compose exec -T app flask db upgrade
    docker-compose exec -T app flask init-db
    
    log_info "应用部署完成"
}

# 获取SSL证书
setup_ssl() {
    log_step "配置SSL证书..."
    
    read -p "是否要获取SSL证书? (y/n): " GET_SSL
    if [[ $GET_SSL == "y" || $GET_SSL == "Y" ]]; then
        read -p "请输入您的邮箱地址: " EMAIL
        if [[ -z "$EMAIL" ]]; then
            log_warn "跳过SSL证书配置"
            return
        fi
        
        # 获取证书
        sudo certbot --nginx -d $DOMAIN -d www.$DOMAIN -d dashboard.$DOMAIN --email $EMAIL --agree-tos --non-interactive
        
        # 设置自动续期
        echo "0 2 * * * root certbot renew --quiet --deploy-hook 'systemctl reload nginx'" | sudo tee /etc/cron.d/certbot-renew
        
        log_info "SSL证书配置完成"
    fi
}

# 创建系统服务
create_system_service() {
    log_step "创建系统服务..."
    
    sudo tee /etc/systemd/system/aicarpools.service << EOF
[Unit]
Description=AI拼车共享平台
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/aicarpools
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable aicarpools.service
}

# 创建监控脚本
create_monitoring() {
    log_step "创建监控脚本..."
    
    mkdir -p /opt/aicarpools/scripts
    
    cat > /opt/aicarpools/scripts/monitor.sh << 'EOF'
#!/bin/bash
# 监控脚本

LOG_FILE="/opt/aicarpools/logs/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查服务状态
if ! docker-compose ps | grep -q "Up"; then
    echo "[$DATE] 服务异常，尝试重启" >> $LOG_FILE
    docker-compose restart
fi

# 检查磁盘空间
DISK_USAGE=$(df /opt/aicarpools | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "[$DATE] 磁盘空间不足: ${DISK_USAGE}%" >> $LOG_FILE
fi
EOF
    
    chmod +x /opt/aicarpools/scripts/monitor.sh
    
    # 添加到crontab
    (crontab -l 2>/dev/null; echo "*/5 * * * * /opt/aicarpools/scripts/monitor.sh") | crontab -
}

# 显示部署信息
show_deployment_info() {
    log_step "部署完成！"
    
    echo ""
    echo "=================================="
    echo "  AI拼车共享平台部署成功！"
    echo "=================================="
    echo ""
    echo "访问地址:"
    echo "  主页: http://$DOMAIN"
    echo "  管理后台: http://$DOMAIN/admin"
    echo ""
    echo "默认管理员账号:"
    echo "  邮箱: <EMAIL>"
    echo "  密码: admin123"
    echo ""
    echo "重要文件位置:"
    echo "  项目目录: /opt/aicarpools"
    echo "  配置文件: /opt/aicarpools/.env.production"
    echo "  日志目录: /opt/aicarpools/logs"
    echo ""
    echo "常用命令:"
    echo "  查看服务状态: docker-compose ps"
    echo "  查看日志: docker-compose logs -f"
    echo "  重启服务: docker-compose restart"
    echo "  停止服务: docker-compose down"
    echo ""
    echo "下一步:"
    echo "1. 编辑配置文件 /opt/aicarpools/.env.production"
    echo "2. 配置邮件服务器信息"
    echo "3. 重启服务: cd /opt/aicarpools && docker-compose restart"
    echo "4. 访问网站并修改默认管理员密码"
    echo ""
    log_warn "请务必修改默认管理员密码！"
}

# 主函数
main() {
    log_info "开始部署AI拼车共享平台..."
    
    check_root
    detect_os
    install_dependencies
    install_docker
    configure_firewall
    download_project
    configure_environment
    create_docker_config
    create_nginx_config
    deploy_application
    setup_ssl
    create_system_service
    create_monitoring
    show_deployment_info
    
    log_info "部署脚本执行完成！"
}

# 执行主函数
main "$@"
